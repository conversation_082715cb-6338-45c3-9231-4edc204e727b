/* visualizer.css */

/*
  为我们将要创建的canvas元素定义样式。
  ID选择器 #audio-visualizer-canvas 用于精确匹配我们用JS创建的画布。
*/
#audio-visualizer-canvas {
  /*
    现在使用JavaScript动态设置position: fixed
    这样可以确保canvas直接覆盖在视频上，不受容器布局影响
  */

  /*
    pointer-events: none;
    这是实现"幽灵层"最关键的属性。
    它告诉浏览器，这个元素不应成为鼠标事件的目标。
    所有点击、悬停等事件都将"穿透"这个画布，直接作用于其下方的YouTube播放器控件。
  */
  pointer-events: none;

  /*
    opacity: 0.8;
    设置轻微的透明度，使可视化效果不会完全遮挡视频内容
  */
  opacity: 0.8;

  /* 临时调试样式 */
  border: 3px solid red !important;
  background-color: rgba(0, 0, 0, 0.3) !important;

  /* 确保canvas在最顶层 */
  z-index: 9999 !important;
}
