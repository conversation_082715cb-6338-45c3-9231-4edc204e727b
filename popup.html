<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Aural-Visual Bridge v2.0</title>
    <style>
        body {
            width: 350px;
            min-height: 400px;
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .header h1 {
            font-size: 1.5em;
            margin: 0 0 5px 0;
        }

        .header p {
            font-size: 0.9em;
            opacity: 0.8;
            margin: 0;
        }

        .section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }

        .section h3 {
            margin: 0 0 10px 0;
            font-size: 1.1em;
            color: #ffd700;
        }

        .toggle-group {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .toggle-group:last-child {
            margin-bottom: 0;
        }

        .toggle-label {
            font-size: 0.9em;
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .toggle-switch.active {
            background: #4CAF50;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }

        .toggle-switch.active::after {
            transform: translateX(26px);
        }

        .status-section {
            text-align: center;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-active { background-color: #4CAF50; }
        .status-inactive { background-color: #9E9E9E; }

        .message {
            display: none;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            text-align: center;
            font-size: 0.9em;
        }

        .message.success {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid #4CAF50;
        }

        .message.error {
            background: rgba(244, 67, 54, 0.3);
            border: 1px solid #F44336;
        }

        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 0.8em;
            opacity: 0.7;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 10px 0;
        }

        .feature-list li {
            padding: 5px 0;
            font-size: 0.85em;
            opacity: 0.9;
        }

        .feature-list li:before {
            content: "✨";
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎵 Aural-Visual Bridge</h1>
        <p>AI音频分类与可视化</p>
    </div>

    <div class="section">
        <h3>🤖 AI功能</h3>
        <div class="toggle-group">
            <span class="toggle-label">音频事件识别</span>
            <div class="toggle-switch active" id="enableAI"></div>
        </div>
        <div class="toggle-group">
            <span class="toggle-label">语音指令检测</span>
            <div class="toggle-switch active" id="enableSpeech"></div>
        </div>
    </div>

    <div class="section">
        <h3>🎨 界面设置</h3>
        <div class="toggle-group">
            <span class="toggle-label">显示标签</span>
            <div class="toggle-switch active" id="enableLabels"></div>
        </div>
        <div class="toggle-group">
            <span class="toggle-label">频谱可视化</span>
            <div class="toggle-switch active" id="enableVisualization"></div>
        </div>
    </div>

    <div class="section status-section">
        <h3>📊 系统状态</h3>
        <div style="margin: 10px 0;">
            <span class="status-indicator status-active"></span>
            <span>AI模型已加载</span>
        </div>
        <div style="margin: 10px 0;">
            <span class="status-indicator status-active"></span>
            <span>音频处理正常</span>
        </div>
        <div style="margin: 10px 0;">
            <span class="status-indicator status-inactive"></span>
            <span>等待YouTube视频</span>
        </div>
    </div>

    <div class="section">
        <h3>✨ 新功能</h3>
        <ul class="feature-list">
            <li>521种音频事件识别</li>
            <li>实时标签显示</li>
            <li>GPU硬件加速</li>
            <li>隐私保护处理</li>
        </ul>
    </div>

    <div id="message" class="message"></div>

    <div class="footer">
        <p>v2.0.0 | 让每个人都能"看见"声音</p>
    </div>

    <script src="popup.js"></script>
    <script>
        // 简单的切换开关交互
        document.addEventListener('DOMContentLoaded', function() {
            const toggles = document.querySelectorAll('.toggle-switch');
            
            toggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    this.classList.toggle('active');
                    
                    // 模拟设置保存
                    const setting = this.id;
                    const isActive = this.classList.contains('active');
                    
                    console.log(`${setting}: ${isActive}`);
                    
                    // 显示成功消息
                    showMessage('设置已更新', 'success');
                });
            });
        });

        function showMessage(text, type) {
            const messageEl = document.getElementById('message');
            messageEl.textContent = text;
            messageEl.className = `message ${type}`;
            messageEl.style.display = 'block';
            
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 2000);
        }
    </script>
</body>
</html>
