/**
 * Jest测试环境设置
 */

// 模拟浏览器环境
global.performance = {
  now: jest.fn(() => Date.now()),
  memory: {
    usedJSHeapSize: 1024 * 1024 * 10, // 10MB
    totalJSHeapSize: 1024 * 1024 * 50, // 50MB
    jsHeapSizeLimit: 1024 * 1024 * 100 // 100MB
  }
};

// 模拟Web Audio API
global.AudioContext = jest.fn(() => ({
  createMediaElementSource: jest.fn(() => ({
    connect: jest.fn()
  })),
  createAnalyser: jest.fn(() => ({
    fftSize: 2048,
    frequencyBinCount: 1024,
    smoothingTimeConstant: 0.8,
    getByteTimeDomainData: jest.fn(),
    getByteFrequencyData: jest.fn(),
    connect: jest.fn()
  })),
  destination: {},
  sampleRate: 44100,
  state: 'running',
  resume: jest.fn(),
  close: jest.fn()
}));

global.webkitAudioContext = global.AudioContext;

// 模拟Canvas API
global.HTMLCanvasElement.prototype.getContext = jest.fn(() => ({
  clearRect: jest.fn(),
  fillRect: jest.fn(),
  fillText: jest.fn(),
  beginPath: jest.fn(),
  moveTo: jest.fn(),
  lineTo: jest.fn(),
  stroke: jest.fn(),
  arc: jest.fn(),
  fill: jest.fn()
}));

// 模拟Chrome扩展API
global.chrome = {
  storage: {
    sync: {
      get: jest.fn((keys, callback) => {
        callback({});
      }),
      set: jest.fn((data, callback) => {
        if (callback) callback();
      })
    }
  },
  runtime: {
    lastError: null
  }
};

// 模拟fetch API
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve('')
  })
);

// 模拟TensorFlow.js
jest.mock('@tensorflow/tfjs', () => ({
  ready: jest.fn(() => Promise.resolve()),
  setBackend: jest.fn(() => Promise.resolve()),
  getBackend: jest.fn(() => 'webgl'),
  loadGraphModel: jest.fn(() => Promise.resolve({
    predict: jest.fn(),
    inputs: [{ shape: [null, 16000], name: 'input', dtype: 'float32' }],
    outputs: [{ shape: [null, 521], name: 'output', dtype: 'float32' }],
    dispose: jest.fn()
  })),
  loadLayersModel: jest.fn(() => Promise.resolve({
    predict: jest.fn(),
    inputs: [{ shape: [null, 43, 40, 1], name: 'input', dtype: 'float32' }],
    outputs: [{ shape: [null, 18], name: 'output', dtype: 'float32' }],
    dispose: jest.fn()
  })),
  tensor1d: jest.fn((data) => ({
    data: jest.fn(() => Promise.resolve(data)),
    dispose: jest.fn(),
    expandDims: jest.fn(() => ({
      data: jest.fn(() => Promise.resolve(data)),
      dispose: jest.fn()
    }))
  })),
  tensor3d: jest.fn((data, shape) => ({
    data: jest.fn(() => Promise.resolve(data)),
    dispose: jest.fn(),
    expandDims: jest.fn(() => ({
      data: jest.fn(() => Promise.resolve(data)),
      dispose: jest.fn()
    }))
  })),
  zeros: jest.fn((shape) => ({
    data: jest.fn(() => Promise.resolve(new Float32Array(shape.reduce((a, b) => a * b, 1)))),
    dispose: jest.fn(),
    expandDims: jest.fn(() => ({
      data: jest.fn(() => Promise.resolve(new Float32Array(shape.reduce((a, b) => a * b, 1)))),
      dispose: jest.fn()
    }))
  })),
  memory: jest.fn(() => ({
    numBytes: 1024 * 1024 * 10,
    numTensors: 5,
    numDataBuffers: 5
  })),
  disposeVariables: jest.fn(),
  engine: jest.fn(() => ({
    backendNames: jest.fn(() => ['webgl', 'cpu'])
  })),
  env: jest.fn(() => ({
    set: jest.fn()
  }))
}));

// 模拟ResizeObserver
global.ResizeObserver = jest.fn(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}));

// 模拟MutationObserver
global.MutationObserver = jest.fn(() => ({
  observe: jest.fn(),
  disconnect: jest.fn()
}));

// 模拟requestAnimationFrame
global.requestAnimationFrame = jest.fn((callback) => {
  return setTimeout(callback, 16); // 模拟60fps
});

global.cancelAnimationFrame = jest.fn((id) => {
  clearTimeout(id);
});

// 模拟localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};
global.localStorage = localStorageMock;

// 模拟console方法（避免测试输出过多日志）
global.console = {
  ...console,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

// 设置测试超时
jest.setTimeout(10000);

// 清理函数
afterEach(() => {
  jest.clearAllMocks();
});
