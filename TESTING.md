# 测试指南

## 安装测试

### 1. 加载扩展
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含项目文件的文件夹
6. 确认扩展出现在列表中且已启用

### 2. 基本功能测试

#### 测试步骤：
1. 访问任意YouTube视频（推荐使用有音乐的视频）
2. 开始播放视频
3. 观察是否出现可视化效果

#### 预期结果：
- 视频播放器上应该出现半透明的可视化图层
- 可以看到：
  - 蓝色的频谱条（低频和中频）
  - 白色的波形线（音量变化）
  - 当有高频声音时，应该看到黄色/红色的频谱条

#### 推荐测试视频类型：
- **音乐视频**：可以清楚看到频谱变化
- **包含哨声的视频**：测试高频警示功能
- **说话视频**：测试中频显示
- **安静场景**：确认在无声时可视化正常

### 3. 交互测试

#### 测试鼠标交互：
1. 尝试点击播放/暂停按钮
2. 拖动进度条
3. 调整音量
4. 点击全屏按钮

#### 预期结果：
- 所有YouTube原生控件应该正常工作
- 可视化图层不应该阻挡任何操作

### 4. 响应式测试

#### 测试不同播放器模式：
1. 默认播放器大小
2. 影院模式
3. 全屏模式
4. 调整浏览器窗口大小

#### 预期结果：
- 可视化图层应该始终完美贴合播放器
- 在所有模式下都应该正常显示

### 5. 性能测试

#### 观察指标：
- CPU使用率（通过任务管理器）
- 视频播放是否流畅
- 浏览器是否有卡顿

#### 预期结果：
- CPU使用率增加应该很小（通常<5%）
- 视频播放应该保持流畅
- 不应该影响其他标签页的性能

## 故障排除

### 问题：可视化不显示
**可能原因和解决方案：**
1. **音频未播放**：确保视频正在播放且有声音
2. **浏览器音频策略**：在页面上点击一次以允许音频播放
3. **扩展未加载**：检查 `chrome://extensions/` 确认扩展已启用
4. **控制台错误**：按F12打开开发者工具，查看Console标签页是否有错误

### 问题：可视化显示但没有颜色变化
**可能原因和解决方案：**
1. **音频静音**：检查YouTube播放器和系统音量
2. **音频内容**：尝试播放有明显音乐的视频
3. **频率范围**：某些音频可能主要在中频范围，尝试包含高音的内容

### 问题：影响视频控制
**可能原因和解决方案：**
1. **CSS问题**：检查 `visualizer.css` 中是否有 `pointer-events: none;`
2. **z-index冲突**：可能需要调整CSS中的z-index值

### 问题：性能问题
**可能原因和解决方案：**
1. **硬件加速**：确保Chrome启用了硬件加速
2. **其他扩展冲突**：尝试禁用其他扩展进行测试
3. **设备性能**：在较老的设备上可能需要降低可视化复杂度

## 调试技巧

### 查看控制台日志：
1. 按F12打开开发者工具
2. 切换到Console标签页
3. 查找以"YouTube Aural-Visual Bridge"开头的日志

### 检查元素：
1. 右键点击视频播放器
2. 选择"检查元素"
3. 查找ID为"audio-visualizer-canvas"的canvas元素

### 验证音频连接：
在控制台中运行：
```javascript
// 检查AudioContext状态
console.log('AudioContext state:', window.audioContext?.state);
```

## 成功标准

扩展安装成功的标志：
- ✅ 可视化图层正常显示
- ✅ 颜色随音频内容变化
- ✅ 不影响YouTube原生功能
- ✅ 支持不同播放器模式
- ✅ 性能影响最小
