{"name": "@tensorflow/tfjs", "version": "4.22.0", "description": "An open-source machine learning framework.", "private": false, "main": "dist/tf.node.js", "module": "dist/index.js", "jsdelivr": "dist/tf.min.js", "unpkg": "dist/tf.min.js", "types": "dist/index.d.ts", "miniprogram": "dist/miniprogram", "bin": {"tfjs-custom-module": "dist/tools/custom_module/cli.js"}, "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs.git"}, "license": "Apache-2.0", "devDependencies": {"@babel/core": "^7.21.3", "@babel/polyfill": "^7.12.1", "@babel/preset-env": "^7.20.2", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-commonjs": "^24.1.0", "@rollup/plugin-node-resolve": "^15.0.2", "@rollup/plugin-typescript": "^11.1.0", "@types/argparse": "^1.0.38", "@types/jasmine": "2.8.7", "@types/node": "~10.17.50", "@types/shelljs": "^0.8.4", "@types/yargs": "^15.0.7", "clang-format": "~1.2.2", "commander": "~2.14.1", "jasmine": "3.1.0", "jasmine-core": "~3.1.0", "karma": "~6.3.2", "karma-browserstack-launcher": "~1.6.0", "karma-chrome-launcher": "~2.2.0", "karma-firefox-launcher": "~1.1.0", "karma-jasmine": "~1.1.1", "karma-typescript": "~5.5.1", "karma-typescript-es6-transform": "^5.5.1", "npm-run-all": "~4.1.3", "rimraf": "~2.6.2", "rollup": "~3.20.2", "rollup-plugin-terser": "~7.0.2", "rollup-plugin-visualizer": "~5.9.0", "shelljs": "~0.8.1", "ts-node": "~8.8.2", "tslint": "~5.11.0", "tslint-no-circular-imports": "~0.5.0", "typescript": "5.0.4", "yalc": "1.0.0-pre.50"}, "scripts": {"build": "tsc && yarn build-cli && yarn bundle", "build-ci": "tsc && yarn build-cli && yarn bundle-ci", "bundle": "rollup -c", "bundle-ci": "rollup -c --ci", "build-link-package": "cd ../link-package && yarn build-deps-for tfjs", "build-deps": "yarn build-link-package", "build-deps-ci": "yarn build-deps", "build-cli": "tsc --project ./tools/custom_module/tsconfig.json && chmod +x ./dist/tools/custom_module/cli.js", "run-custom-build": "ts-node -s ./tools/custom_module/cli.ts", "build-npm": "./scripts/build-npm.sh", "link-local": "yalc link", "publish-local": "yarn build-npm && yalc push", "publish-npm": "npm publish", "lint": "tslint -p . -t verbose", "coverage": "KARMA_COVERAGE=1 karma start --browsers='Chrome' --singleRun", "test": "yarn && yarn build-deps && yarn build && karma start", "test-dev": "karma start", "test-tools": "ts-node --skip-ignore --project ./tools/custom_module/tsconfig.json run_tools_tests.ts", "test-ci": "./scripts/test-ci.sh"}, "dependencies": {"@tensorflow/tfjs-backend-cpu": "4.22.0", "@tensorflow/tfjs-backend-webgl": "4.22.0", "@tensorflow/tfjs-converter": "4.22.0", "@tensorflow/tfjs-core": "4.22.0", "@tensorflow/tfjs-data": "4.22.0", "@tensorflow/tfjs-layers": "4.22.0", "argparse": "^1.0.10", "chalk": "^4.1.0", "core-js": "3.29.1", "regenerator-runtime": "^0.13.5", "yargs": "^16.0.3"}, "browser": {"node-fetch": false, "util": false, "crypto": false}}