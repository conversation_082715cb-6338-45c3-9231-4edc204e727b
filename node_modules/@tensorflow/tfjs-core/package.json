{"name": "@tensorflow/tfjs-core", "version": "4.22.0", "description": "Hardware-accelerated JavaScript library for machine intelligence", "private": false, "main": "dist/tf-core.node.js", "jsdelivr": "dist/tf-core.min.js", "unpkg": "dist/tf-core.min.js", "types": "dist/index.d.ts", "jsnext:main": "dist/index.js", "module": "dist/index.js", "miniprogram": "dist/miniprogram", "engines": {"yarn": ">= 1.3.2"}, "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs.git", "directory": "tfjs-core"}, "license": "Apache-2.0", "devDependencies": {"@bazel/bazelisk": "^1.12.0", "@bazel/ibazel": "^0.16.2"}, "scripts": {"build": "bazel build :tfjs-core_pkg", "publish-npm": "bazel run :tfjs-core_pkg.publish", "coverage": "bazel coverage :tfjs-core_node_test", "test": "bazel test :tests --test_output=all", "test-dev": "ibazel test :tests --test_output=all", "test-browser": "bazel test :tfjs-core_test --test_output=streamed", "test-browser-debug": "bazel run :tfjs-core_test --test_output=streamed", "test-node": "bazel test :tfjs-core_node_test --test_output=streamed", "test-node-debug": "bazel run :tfjs-core_node_test --config=debug --test_output=streamed", "test-async-backends": "bazel test :tfjs-core_async_backends_test --test_output=streamed", "test-snippets": "bazel test :test_snippets_test --test_output=streamed"}, "dependencies": {"@types/long": "^4.0.1", "@types/offscreencanvas": "~2019.7.0", "@types/seedrandom": "^2.4.28", "@webgpu/types": "0.1.38", "long": "4.0.0", "node-fetch": "~2.6.1", "seedrandom": "^3.0.5"}, "browser": {"node-fetch": false, "util": false, "crypto": false, "worker_threads": false}, "sideEffects": ["./dist/index.js", "./dist/engine.js", "./dist/tensor.js", "./dist/base_side_effects.js", "./dist/flags.js", "./dist/platforms/*.js", "./dist/register_all_gradients.js", "./dist/public/chained_ops/*.js", "./dist/io/*.js", "./src/index.mjs", "./src/engine.mjs", "./src/tensor.mjs", "./src/base_side_effects.mjs", "./src/flags.mjs", "./src/platforms/*.mjs", "./src/register_all_gradients.mjs", "./src/public/chained_ops/*.mjs", "./src/io/*.mjs"], "resolutions": {"minimist": "1.2.6"}}