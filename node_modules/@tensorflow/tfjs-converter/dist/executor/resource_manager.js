/**
 * Contains global resources of a model.
 */
export class ResourceManager {
    constructor(hashTableNameToHandle = {}, hashTableMap = {}) {
        this.hashTableNameToHandle = hashTableNameToHandle;
        this.hashTableMap = hashTableMap;
    }
    /**
     * Register a `HashTable` in the resource manager.
     *
     * The `HashTable` can be retrieved by `resourceManager.getHashTableById`,
     * where id is the table handle tensor's id.
     *
     * @param name Op node name that creates the `HashTable`.
     * @param hashTable The `HashTable` to be added to resource manager.
     */
    addHashTable(name, hashTable) {
        this.hashTableNameToHandle[name] = hashTable.handle;
        this.hashTableMap[hashTable.id] = hashTable;
    }
    /**
     * Get the table handle by node name.
     * @param name Op node name that creates the `HashTable`. This name is also
     *     used in the inputs list of lookup and import `HashTable` ops.
     */
    getHashTableHandleByName(name) {
        return this.hashTableNameToHandle[name];
    }
    /**
     * Get the actual `HashTable` by its handle tensor's id.
     * @param id The id of the handle tensor.
     */
    getHashTableById(id) {
        return this.hashTableMap[id];
    }
    /**
     * Dispose `ResourceManager`, including its hashTables and tensors in them.
     */
    dispose() {
        for (const key in this.hashTableMap) {
            this.hashTableMap[key].clearAndClose();
            delete this.hashTableMap[key];
        }
        for (const name in this.hashTableNameToHandle) {
            this.hashTableNameToHandle[name].dispose();
            delete this.hashTableNameToHandle[name];
        }
    }
}
//# sourceMappingURL=data:application/json;base64,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