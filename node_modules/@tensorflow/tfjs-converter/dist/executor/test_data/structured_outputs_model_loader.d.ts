/**
 * @license
 * Copyright 2022 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-converter/dist/executor/test_data/structured_outputs_model_loader" />
export declare const STRUCTURED_OUTPUTS_MODEL: {
    modelTopology: {
        node: ({
            name: string;
            op: string;
            attr: {
                value: {
                    tensor: {
                        dtype: string;
                        tensorShape: {
                            dim?: undefined;
                        };
                    };
                };
                dtype: {
                    type: string;
                };
                shape?: undefined;
                transpose_b?: undefined;
                transpose_a?: undefined;
                T?: undefined;
                Tidx?: undefined;
                N?: undefined;
            };
            input?: undefined;
            device?: undefined;
        } | {
            name: string;
            op: string;
            attr: {
                dtype: {
                    type: string;
                };
                value: {
                    tensor: {
                        dtype: string;
                        tensorShape: {
                            dim: {
                                size: string;
                            }[];
                        };
                    };
                };
                shape?: undefined;
                transpose_b?: undefined;
                transpose_a?: undefined;
                T?: undefined;
                Tidx?: undefined;
                N?: undefined;
            };
            input?: undefined;
            device?: undefined;
        } | {
            name: string;
            op: string;
            attr: {
                dtype: {
                    type: string;
                };
                shape: {
                    shape: {
                        dim: {
                            size: string;
                        }[];
                    };
                };
                value?: undefined;
                transpose_b?: undefined;
                transpose_a?: undefined;
                T?: undefined;
                Tidx?: undefined;
                N?: undefined;
            };
            input?: undefined;
            device?: undefined;
        } | {
            name: string;
            op: string;
            input: string[];
            device: string;
            attr: {
                transpose_b: {
                    b: boolean;
                };
                transpose_a: {
                    b: boolean;
                };
                T: {
                    type: string;
                };
                value?: undefined;
                dtype?: undefined;
                shape?: undefined;
                Tidx?: undefined;
                N?: undefined;
            };
        } | {
            name: string;
            op: string;
            input: string[];
            attr: {
                Tidx: {
                    type: string;
                };
                T: {
                    type: string;
                };
                N: {
                    i: string;
                };
                value?: undefined;
                dtype?: undefined;
                shape?: undefined;
                transpose_b?: undefined;
                transpose_a?: undefined;
            };
            device?: undefined;
        } | {
            name: string;
            op: string;
            input: string[];
            attr: {
                T: {
                    type: string;
                };
                value?: undefined;
                dtype?: undefined;
                shape?: undefined;
                transpose_b?: undefined;
                transpose_a?: undefined;
                Tidx?: undefined;
                N?: undefined;
            };
            device?: undefined;
        })[];
        library: {};
        versions: {
            producer: number;
        };
    };
    format: string;
    generatedBy: string;
    convertedBy: string;
    weightSpecs: {
        name: string;
        shape: number[];
        dtype: string;
    }[];
    weightData: ArrayBufferLike;
    signature: {
        inputs: {
            'input1:0': {
                name: string;
                dtype: string;
                tensorShape: {
                    dim: {
                        size: string;
                    }[];
                };
            };
            'input3:0': {
                name: string;
                dtype: string;
                tensorShape: {
                    dim: {
                        size: string;
                    }[];
                };
            };
            'input2:0': {
                name: string;
                dtype: string;
                tensorShape: {
                    dim: {
                        size: string;
                    }[];
                };
            };
        };
        outputs: {
            'Identity_1:0': {
                name: string;
                dtype: string;
                tensorShape: {
                    dim: {
                        size: string;
                    }[];
                };
            };
            'Identity:0': {
                name: string;
                dtype: string;
                tensorShape: {
                    dim: {
                        size: string;
                    }[];
                };
            };
            'Identity_2:0': {
                name: string;
                dtype: string;
                tensorShape: {
                    dim: {
                        size: string;
                    }[];
                };
            };
        };
    };
    userDefinedMetadata: {
        structuredOutputKeys: string[];
    };
};
