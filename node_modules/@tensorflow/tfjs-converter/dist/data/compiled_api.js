/**
 * @license
 * Copyright 2019 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * =============================================================================
 */
/** DataType enum. */
export var DataType;
(function (DataType) {
    // These properties must be quoted since they are used by parseDtypeParam
    // in tfjs-converter/src/operations/operation_mapper.ts to look up dtypes
    // by string name. If they are not quoted, Closure will mangle their names.
    // Not a legal value for DataType.  Used to indicate a DataType field
    // has not been set.
    DataType[DataType["DT_INVALID"] = 0] = "DT_INVALID";
    // Data types that all computation devices are expected to be
    // capable to support.
    DataType[DataType["DT_FLOAT"] = 1] = "DT_FLOAT";
    DataType[DataType["DT_DOUBLE"] = 2] = "DT_DOUBLE";
    DataType[DataType["DT_INT32"] = 3] = "DT_INT32";
    DataType[DataType["DT_UINT8"] = 4] = "DT_UINT8";
    DataType[DataType["DT_INT16"] = 5] = "DT_INT16";
    DataType[DataType["DT_INT8"] = 6] = "DT_INT8";
    DataType[DataType["DT_STRING"] = 7] = "DT_STRING";
    DataType[DataType["DT_COMPLEX64"] = 8] = "DT_COMPLEX64";
    DataType[DataType["DT_INT64"] = 9] = "DT_INT64";
    DataType[DataType["DT_BOOL"] = 10] = "DT_BOOL";
    DataType[DataType["DT_QINT8"] = 11] = "DT_QINT8";
    DataType[DataType["DT_QUINT8"] = 12] = "DT_QUINT8";
    DataType[DataType["DT_QINT32"] = 13] = "DT_QINT32";
    DataType[DataType["DT_BFLOAT16"] = 14] = "DT_BFLOAT16";
    DataType[DataType["DT_QINT16"] = 15] = "DT_QINT16";
    DataType[DataType["DT_QUINT16"] = 16] = "DT_QUINT16";
    DataType[DataType["DT_UINT16"] = 17] = "DT_UINT16";
    DataType[DataType["DT_COMPLEX128"] = 18] = "DT_COMPLEX128";
    DataType[DataType["DT_HALF"] = 19] = "DT_HALF";
    DataType[DataType["DT_RESOURCE"] = 20] = "DT_RESOURCE";
    DataType[DataType["DT_VARIANT"] = 21] = "DT_VARIANT";
    DataType[DataType["DT_UINT32"] = 22] = "DT_UINT32";
    DataType[DataType["DT_UINT64"] = 23] = "DT_UINT64";
    // Do not use!  These are only for parameters.  Every enum above
    // should have a corresponding value below (verified by types_test).
    DataType[DataType["DT_FLOAT_REF"] = 101] = "DT_FLOAT_REF";
    DataType[DataType["DT_DOUBLE_REF"] = 102] = "DT_DOUBLE_REF";
    DataType[DataType["DT_INT32_REF"] = 103] = "DT_INT32_REF";
    DataType[DataType["DT_UINT8_REF"] = 104] = "DT_UINT8_REF";
    DataType[DataType["DT_INT16_REF"] = 105] = "DT_INT16_REF";
    DataType[DataType["DT_INT8_REF"] = 106] = "DT_INT8_REF";
    DataType[DataType["DT_STRING_REF"] = 107] = "DT_STRING_REF";
    DataType[DataType["DT_COMPLEX64_REF"] = 108] = "DT_COMPLEX64_REF";
    DataType[DataType["DT_INT64_REF"] = 109] = "DT_INT64_REF";
    DataType[DataType["DT_BOOL_REF"] = 110] = "DT_BOOL_REF";
    DataType[DataType["DT_QINT8_REF"] = 111] = "DT_QINT8_REF";
    DataType[DataType["DT_QUINT8_REF"] = 112] = "DT_QUINT8_REF";
    DataType[DataType["DT_QINT32_REF"] = 113] = "DT_QINT32_REF";
    DataType[DataType["DT_BFLOAT16_REF"] = 114] = "DT_BFLOAT16_REF";
    DataType[DataType["DT_QINT16_REF"] = 115] = "DT_QINT16_REF";
    DataType[DataType["DT_QUINT16_REF"] = 116] = "DT_QUINT16_REF";
    DataType[DataType["DT_UINT16_REF"] = 117] = "DT_UINT16_REF";
    DataType[DataType["DT_COMPLEX128_REF"] = 118] = "DT_COMPLEX128_REF";
    DataType[DataType["DT_HALF_REF"] = 119] = "DT_HALF_REF";
    DataType[DataType["DT_RESOURCE_REF"] = 120] = "DT_RESOURCE_REF";
    DataType[DataType["DT_VARIANT_REF"] = 121] = "DT_VARIANT_REF";
    DataType[DataType["DT_UINT32_REF"] = 122] = "DT_UINT32_REF";
    DataType[DataType["DT_UINT64_REF"] = 123] = "DT_UINT64_REF";
})(DataType || (DataType = {}));
export var SaverDef;
(function (SaverDef) {
    /** CheckpointFormatVersion enum. */
    let CheckpointFormatVersion;
    (function (CheckpointFormatVersion) {
        CheckpointFormatVersion[CheckpointFormatVersion["LEGACY"] = 0] = "LEGACY";
        CheckpointFormatVersion[CheckpointFormatVersion["V1"] = 1] = "V1";
        CheckpointFormatVersion[CheckpointFormatVersion["V2"] = 2] = "V2";
    })(CheckpointFormatVersion = SaverDef.CheckpointFormatVersion || (SaverDef.CheckpointFormatVersion = {}));
})(SaverDef || (SaverDef = {}));
//# sourceMappingURL=data:application/json;base64,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