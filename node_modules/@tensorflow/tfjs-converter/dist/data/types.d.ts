/// <amd-module name="@tensorflow/tfjs-converter/dist/data/types" />
/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { DataType, Tensor } from '@tensorflow/tfjs-core';
import { HashTable } from '../executor/hash_table';
import { TensorArray } from '../executor/tensor_array';
import { TensorList } from '../executor/tensor_list';
export type NamedTensorMap = {
    [key: string]: Tensor;
};
export type NamedTensorsMap = {
    [key: string]: Tensor[];
};
export type TensorArrayMap = {
    [key: number]: TensorArray;
};
export type TensorListMap = {
    [key: number]: TensorList;
};
export type HashTableMap = {
    [key: number]: HashTable;
};
export interface TensorInfo {
    name: string;
    shape?: number[];
    dtype: DataType;
}
