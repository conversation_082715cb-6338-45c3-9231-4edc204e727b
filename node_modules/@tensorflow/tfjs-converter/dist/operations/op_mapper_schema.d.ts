/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-converter/dist/operations/op_mapper_schema" />
export declare const json: {
    $schema: string;
    definitions: {
        OpMapper: {
            type: string;
            properties: {
                tfOpName: {
                    type: string;
                };
                category: {
                    $ref: string;
                };
                inputs: {
                    type: string;
                    items: {
                        $ref: string;
                    };
                };
                attrs: {
                    type: string;
                    items: {
                        $ref: string;
                    };
                };
                customExecutor: {
                    $ref: string;
                };
                outputs: {
                    type: string;
                };
            };
            required: string[];
            additionalProperties: boolean;
        };
        Category: {
            type: string;
            enum: string[];
        };
        InputParamMapper: {
            type: string;
            properties: {
                name: {
                    type: string;
                };
                type: {
                    $ref: string;
                };
                defaultValue: {
                    anyOf: ({
                        type: string;
                        items?: undefined;
                    } | {
                        type: string;
                        items: {
                            type: string;
                        };
                    })[];
                };
                notSupported: {
                    type: string;
                };
                start: {
                    type: string;
                };
                end: {
                    type: string;
                };
            };
            required: string[];
            additionalProperties: boolean;
        };
        ParamTypes: {
            type: string;
            enum: string[];
        };
        AttrParamMapper: {
            type: string;
            properties: {
                name: {
                    type: string;
                };
                type: {
                    $ref: string;
                };
                defaultValue: {
                    anyOf: ({
                        type: string;
                        items?: undefined;
                    } | {
                        type: string;
                        items: {
                            type: string;
                        };
                    })[];
                };
                notSupported: {
                    type: string;
                };
                tfName: {
                    type: string;
                };
                tfDeprecatedName: {
                    type: string;
                };
            };
            required: string[];
            additionalProperties: boolean;
        };
        OpExecutor: {
            type: string;
            additionalProperties: boolean;
        };
    };
    items: {
        $ref: string;
    };
    type: string;
};
