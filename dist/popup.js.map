{"version": 3, "file": "popup.js", "mappings": "MA4BA,SAASA,IACP,OAAO,IAAIC,QAASC,IAClBC,OAAOC,QAAQC,YAAY,CAAEC,KAAM,cAAiBC,IAC9CA,GAAYA,EAASC,QACvBN,EAAQK,EAASE,QAEjBP,EAAQ,CAAC,MAIjB,CAEA,SAASQ,EAAUD,GACjB,OAAO,IAAIR,QAASC,IAClBC,OAAOC,QAAQC,YAAY,CAAEC,KAAM,aAAcG,UAAWF,IAC1DL,EAAQK,GAAYA,EAASC,YAGnC,CA0DA,SAASG,EAAYC,GACnBC,EAAYD,EAAS,UACvB,CAMA,SAASC,EAAYD,EAASN,GAC5B,MAAMQ,EAAaC,SAASC,eAAe,WACvCF,IACFA,EAAWG,YAAcL,EACzBE,EAAWI,UAAY,WAAWZ,IAClCQ,EAAWK,MAAMC,QAAU,QAE3BC,WAAW,KACTP,EAAWK,MAAMC,QAAU,QAC1B,KAEP,CAvHAL,SAASO,iBAAiB,mBAAoB,YAO9CC,iBACE,KAoCF,SAAkBd,GAEhB,MAAMe,EAAWT,SAASC,eAAe,YAC3B,IAAAS,EAAVD,IACFA,EAASE,SAAsC,KAAnB,QAATD,EAAAhB,EAAOkB,UAAE,IAAAF,OAAA,EAATA,EAAWG,eAGhC,MAAMC,EAAed,SAASC,eAAe,gBAC3B,IAAAc,EAAdD,IACFA,EAAaH,SAAoC,KAAjB,QAATI,EAAArB,EAAOsB,UAAE,IAAAD,OAAA,EAATA,EAAWE,aAGpC,MAAMC,EAAsBlB,SAASC,eAAe,uBAC3B,IAAAkB,EAArBD,IACFA,EAAoBP,SAA6C,KAA1B,QAATQ,EAAAzB,EAAOsB,UAAE,IAAAG,OAAA,EAATA,EAAWD,qBAE7C,CA/CIE,OAHqBnC,KAoDzB,WAEE,MAAMwB,EAAWT,SAASC,eAAe,YACrCQ,GACFA,EAASF,iBAAiB,SAAUC,UAClC,MAAMd,QAAeT,IACrBS,EAAOkB,GAAKlB,EAAOkB,IAAM,CAAC,EAC1BlB,EAAOkB,GAAGC,aAAeQ,EAAEC,OAAOX,cAC5BhB,EAAUD,GAChBE,EAAY,aAKhB,MAAMkB,EAAed,SAASC,eAAe,gBACzCa,GACFA,EAAaP,iBAAiB,SAAUC,UACtC,MAAMd,QAAeT,IACrBS,EAAOsB,GAAKtB,EAAOsB,IAAM,CAAC,EAC1BtB,EAAOsB,GAAGC,WAAaI,EAAEC,OAAOX,cAC1BhB,EAAUD,GAChBE,EAAY,aAKhB,MAAMsB,EAAsBlB,SAASC,eAAe,uBAChDiB,GACFA,EAAoBX,iBAAiB,SAAUC,UAC7C,MAAMd,QAAeT,IACrBS,EAAOsB,GAAKtB,EAAOsB,IAAM,CAAC,EAC1BtB,EAAOsB,GAAGE,oBAAsBG,EAAEC,OAAOX,cACnChB,EAAUD,GAChBE,EAAY,aAGlB,CAlFI2B,EAEF,CAAE,MAAOC,GAuFT1B,EArFY,QAqFS,QApFrB,CACF,CAlBE2B,EACF,E", "sources": ["webpack://youtube-aural-visual-bridge/./src/popup.js"], "sourcesContent": ["/**\n * Chrome扩展弹出窗口脚本\n */\n\ndocument.addEventListener('DOMContentLoaded', function() {\n  console.log('Popup loaded');\n  \n  // 初始化弹出窗口\n  initializePopup();\n});\n\nasync function initializePopup() {\n  try {\n    // 获取当前配置\n    const config = await getConfig();\n    \n    // 更新UI\n    updateUI(config);\n    \n    // 设置事件监听器\n    setupEventListeners();\n    \n  } catch (error) {\n    console.error('Failed to initialize popup:', error);\n    showError('初始化失败');\n  }\n}\n\nfunction getConfig() {\n  return new Promise((resolve) => {\n    chrome.runtime.sendMessage({ type: 'GET_CONFIG' }, (response) => {\n      if (response && response.success) {\n        resolve(response.config);\n      } else {\n        resolve({});\n      }\n    });\n  });\n}\n\nfunction setConfig(config) {\n  return new Promise((resolve) => {\n    chrome.runtime.sendMessage({ type: 'SET_CONFIG', config }, (response) => {\n      resolve(response && response.success);\n    });\n  });\n}\n\nfunction updateUI(config) {\n  // 更新开关状态\n  const enableAI = document.getElementById('enableAI');\n  if (enableAI) {\n    enableAI.checked = config.ai?.enableYAMNet !== false;\n  }\n  \n  const enableLabels = document.getElementById('enableLabels');\n  if (enableLabels) {\n    enableLabels.checked = config.ui?.showLabels !== false;\n  }\n  \n  const enableVisualization = document.getElementById('enableVisualization');\n  if (enableVisualization) {\n    enableVisualization.checked = config.ui?.enableVisualization !== false;\n  }\n}\n\nfunction setupEventListeners() {\n  // AI开关\n  const enableAI = document.getElementById('enableAI');\n  if (enableAI) {\n    enableAI.addEventListener('change', async (e) => {\n      const config = await getConfig();\n      config.ai = config.ai || {};\n      config.ai.enableYAMNet = e.target.checked;\n      await setConfig(config);\n      showSuccess('AI设置已更新');\n    });\n  }\n  \n  // 标签开关\n  const enableLabels = document.getElementById('enableLabels');\n  if (enableLabels) {\n    enableLabels.addEventListener('change', async (e) => {\n      const config = await getConfig();\n      config.ui = config.ui || {};\n      config.ui.showLabels = e.target.checked;\n      await setConfig(config);\n      showSuccess('标签设置已更新');\n    });\n  }\n  \n  // 可视化开关\n  const enableVisualization = document.getElementById('enableVisualization');\n  if (enableVisualization) {\n    enableVisualization.addEventListener('change', async (e) => {\n      const config = await getConfig();\n      config.ui = config.ui || {};\n      config.ui.enableVisualization = e.target.checked;\n      await setConfig(config);\n      showSuccess('可视化设置已更新');\n    });\n  }\n}\n\nfunction showSuccess(message) {\n  showMessage(message, 'success');\n}\n\nfunction showError(message) {\n  showMessage(message, 'error');\n}\n\nfunction showMessage(message, type) {\n  const messageDiv = document.getElementById('message');\n  if (messageDiv) {\n    messageDiv.textContent = message;\n    messageDiv.className = `message ${type}`;\n    messageDiv.style.display = 'block';\n    \n    setTimeout(() => {\n      messageDiv.style.display = 'none';\n    }, 3000);\n  }\n}\n"], "names": ["getConfig", "Promise", "resolve", "chrome", "runtime", "sendMessage", "type", "response", "success", "config", "setConfig", "showSuccess", "message", "showMessage", "messageDiv", "document", "getElementById", "textContent", "className", "style", "display", "setTimeout", "addEventListener", "async", "enableAI", "_config$ai", "checked", "ai", "enableYAMNet", "enableLabels", "_config$ui", "ui", "showLabels", "enableVisualization", "_config$ui2", "updateUI", "e", "target", "setupEventListeners", "error", "initializePopup"], "sourceRoot": ""}