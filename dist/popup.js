(()=>{function e(){return new Promise(e=>{chrome.runtime.sendMessage({type:"GET_CONFIG"},n=>{n&&n.success?e(n.config):e({})})})}function n(e){return new Promise(n=>{chrome.runtime.sendMessage({type:"SET_CONFIG",config:e},e=>{n(e&&e.success)})})}function t(e){a(e,"success")}function a(e,n){const t=document.getElementById("message");t&&(t.textContent=e,t.className=`message ${n}`,t.style.display="block",setTimeout(()=>{t.style.display="none"},3e3))}document.addEventListener("DOMContentLoaded",function(){!async function(){try{!function(e){const n=document.getElementById("enableAI");var t;n&&(n.checked=!1!==(null===(t=e.ai)||void 0===t?void 0:t.enableYAMNet));const a=document.getElementById("enableLabels");var c;a&&(a.checked=!1!==(null===(c=e.ui)||void 0===c?void 0:c.showLabels));const i=document.getElementById("enableVisualization");var s;i&&(i.checked=!1!==(null===(s=e.ui)||void 0===s?void 0:s.enableVisualization))}(await e()),function(){const a=document.getElementById("enableAI");a&&a.addEventListener("change",async a=>{const c=await e();c.ai=c.ai||{},c.ai.enableYAMNet=a.target.checked,await n(c),t("AI设置已更新")});const c=document.getElementById("enableLabels");c&&c.addEventListener("change",async a=>{const c=await e();c.ui=c.ui||{},c.ui.showLabels=a.target.checked,await n(c),t("标签设置已更新")});const i=document.getElementById("enableVisualization");i&&i.addEventListener("change",async a=>{const c=await e();c.ui=c.ui||{},c.ui.enableVisualization=a.target.checked,await n(c),t("可视化设置已更新")})}()}catch(c){a("初始化失败","error")}}()})})();
//# sourceMappingURL=popup.js.map