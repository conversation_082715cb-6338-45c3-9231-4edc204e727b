chrome.runtime.onInstalled.addListener(e=>{"install"===e.reason||e.reason}),chrome.runtime.onMessage.addListener((e,s,c)=>{switch(e.type){case"GET_CONFIG":return chrome.storage.sync.get(null,e=>{c({success:!0,config:e})}),!0;case"SET_CONFIG":return chrome.storage.sync.set(e.config,()=>{c({success:!0})}),!0;case"LOG_ERROR":c({success:!0});break;default:c({success:!1,error:"Unknown message type"})}}),chrome.tabs.onUpdated.addListener((e,s,c)=>{"complete"===s.status&&c.url&&c.url.includes("youtube.com/watch")});
//# sourceMappingURL=background.js.map