(()=>{var e,t={551:()=>{},1234:()=>{},2627:(e,t,i)=>{"use strict";i(3266);const s=new class{constructor(){this.events=new Map,this.maxListeners=100}on(e,t,i={}){this.events.has(e)||this.events.set(e,[]);const s=this.events.get(e);if(s.length>=this.maxListeners)return;const a={callback:t,once:i.once||!1,priority:i.priority||0};s.push(a),s.sort((e,t)=>t.priority-e.priority)}once(e,t){this.on(e,t,{once:!0})}off(e,t){if(!this.events.has(e))return;const i=this.events.get(e),s=i.findIndex(e=>e.callback===t);-1!==s&&i.splice(s,1),0===i.length&&this.events.delete(e)}emit(e,t){if(!this.events.has(e))return;const i=this.events.get(e),s=[];for(let n=0;n<i.length;n++){const e=i[n];try{e.callback(t),e.once&&s.push(n)}catch(a){}}for(let n=s.length-1;n>=0;n--)i.splice(s[n],1)}clear(){this.events.clear()}getStats(){const e={};for(const[t,i]of this.events)e[t]=i.length;return e}},a="audio:initialized",n="audio:started",o="audio:stopped",r="audio:data-available",h="audio:error",c="ai:model-loaded",l="ai:model-error",m="ai:classification-result",d="ai:processing-start",u="ai:processing-end",g="ui:performance-warning",p="system:ready",f="system:error",y=s;class w{constructor(e={}){this.config={sampleRate:16e3,frameLength:1024,hopLength:512,melBins:64,fftSize:2048,minFreq:125,maxFreq:7500,...e},this.melFilterBank=null,this.isInitialized=!1}async initialize(){try{this.melFilterBank=this.createMelFilterBank(),this.isInitialized=!0}catch(e){throw e}}async extract(e){if(!this.isInitialized)throw new Error("FeatureExtractor not initialized");try{const t={},i=this.computeSTFT(e.timeData);t.stft=i;const s=this.computePowerSpectrum(i);t.powerSpectrum=s;const a=this.computeMelSpectrogram(s);t.melSpectrogram=a;const n=this.computeLogMelSpectrogram(a);t.logMelSpectrogram=n;const o=this.computeMFCC(n);return t.mfcc=o,t.spectralCentroid=this.computeSpectralCentroid(s),t.spectralRolloff=this.computeSpectralRolloff(s),t.zeroCrossingRate=this.computeZeroCrossingRate(e.timeData),t}catch(t){throw t}}computeSTFT(e){const t=this.config.frameLength,i=this.config.hopLength,s=Math.floor((e.length-t)/i)+1,a=[];for(let n=0;n<s;n++){const s=n*i,o=(Math.min(s+t,e.length),new Float32Array(t));for(let i=0;i<t;i++)o[i]=s+i<e.length?e[s+i]:0;const r=this.applyHannWindow(o),h=this.computeFFT(r);a.push(h)}return a}applyHannWindow(e){const t=new Float32Array(e.length);for(let i=0;i<e.length;i++){const s=.5*(1-Math.cos(2*Math.PI*i/(e.length-1)));t[i]=e[i]*s}return t}computeFFT(e){const t=e.length,i=new Float32Array(t),s=new Float32Array(t);for(let a=0;a<t/2;a++){let n=0,o=0;for(let i=0;i<t;i++){const s=-2*Math.PI*a*i/t;n+=e[i]*Math.cos(s),o+=e[i]*Math.sin(s)}i[a]=n,s[a]=o}return{real:i,imag:s}}computePowerSpectrum(e){return e.map(e=>{const t=new Float32Array(e.real.length);for(let i=0;i<e.real.length;i++)t[i]=e.real[i]*e.real[i]+e.imag[i]*e.imag[i];return t})}createMelFilterBank(){const e=this.config.melBins,t=this.config.fftSize/2+1,i=this.config.sampleRate,s=this.config.minFreq,a=this.config.maxFreq,n=this.hzToMel(s),o=this.hzToMel(a),r=[];for(let l=0;l<=e+1;l++)r.push(n+(o-n)*l/(e+1));const h=r.map(e=>this.melToHz(e)).map(e=>Math.floor(e*this.config.fftSize/i)),c=[];for(let l=0;l<e;l++){const e=new Float32Array(t),i=h[l],s=h[l+1],a=h[l+2];for(let t=i;t<s;t++)s>i&&(e[t]=(t-i)/(s-i));for(let t=s;t<a;t++)a>s&&(e[t]=(a-t)/(a-s));c.push(e)}return c}hzToMel(e){return 2595*Math.log10(1+e/700)}melToHz(e){return 700*(Math.pow(10,e/2595)-1)}computeMelSpectrogram(e){return e.map(e=>{const t=new Float32Array(this.config.melBins);for(let i=0;i<this.config.melBins;i++){let s=0;const a=this.melFilterBank[i];for(let t=0;t<Math.min(e.length,a.length);t++)s+=e[t]*a[t];t[i]=s}return t})}computeLogMelSpectrogram(e){return e.map(e=>{const t=new Float32Array(e.length);for(let i=0;i<e.length;i++)t[i]=Math.log(Math.max(e[i],1e-10));return t})}computeMFCC(e){return e.map(e=>{const t=new Float32Array(13);for(let i=0;i<13;i++){let s=0;for(let t=0;t<e.length;t++)s+=e[t]*Math.cos(Math.PI*i*(t+.5)/e.length);t[i]=s}return t})}computeSpectralCentroid(e){return e.map(e=>{let t=0,i=0;for(let s=0;s<e.length;s++){t+=s*this.config.sampleRate/(2*e.length)*e[s],i+=e[s]}return i>0?t/i:0})}computeSpectralRolloff(e,t=.85){return e.map(e=>{const i=e.reduce((e,t)=>e+t,0)*t;let s=0;for(let t=0;t<e.length;t++)if(s+=e[t],s>=i)return t*this.config.sampleRate/(2*e.length);return this.config.sampleRate/2})}computeZeroCrossingRate(e){let t=0;for(let i=1;i<e.length;i++)e[i]>=0!=e[i-1]>=0&&t++;return t/(e.length-1)}getStatus(){return{isInitialized:this.isInitialized,config:this.config,melFilterBankSize:this.melFilterBank?this.melFilterBank.length:0}}dispose(){this.melFilterBank=null,this.isInitialized=!1}}class C{constructor(e,t={}){this.audioContext=e,this.config={sampleRate:16e3,frameLength:1024,hopLength:512,windowType:"hann",melBins:64,fftSize:2048,...t},this.featureExtractor=new w(this.config),this.audioBuffer=[],this.isInitialized=!1}async initialize(){try{await this.featureExtractor.initialize(),this.isInitialized=!0}catch(e){throw e}}async process(e){if(!this.isInitialized)throw new Error("AudioProcessor not initialized");try{const t=this.preprocess(e),i=await this.featureExtractor.extract(t);return{raw:e,preprocessed:t,features:this.postprocess(i),timestamp:Date.now()}}catch(t){throw t}}preprocess(e){const t=this.convertToTimeData(e),i=this.resample(t,e.sampleRate,this.config.sampleRate),s=this.normalize(i),a=this.applyWindow(s);return{timeData:this.preemphasis(a),sampleRate:this.config.sampleRate,frameLength:this.config.frameLength,hopLength:this.config.hopLength}}convertToTimeData(e){if(e.timeDomain){const t=new Float32Array(e.timeDomain.length);for(let i=0;i<e.timeDomain.length;i++)t[i]=(e.timeDomain[i]-128)/128;return t}const t=e.frequency,i=new Float32Array(t.length);for(let s=0;s<t.length;s++)i[s]=(t[s]-128)/128;return i}resample(e,t,i){if(t===i)return e;const s=t/i,a=Math.floor(e.length/s),n=new Float32Array(a);for(let o=0;o<a;o++){const t=o*s,i=Math.floor(t),a=Math.min(i+1,e.length-1),r=t-i;n[o]=e[i]*(1-r)+e[a]*r}return n}normalize(e){let t=0;for(let a=0;a<e.length;a++)t+=e[a]*e[a];if(t=Math.sqrt(t/e.length),t<1e-8)return e;const i=new Float32Array(e.length),s=.95/t;for(let a=0;a<e.length;a++)i[a]=Math.max(-1,Math.min(1,e[a]*s));return i}applyWindow(e){const t=new Float32Array(e.length);switch(this.config.windowType){case"hann":for(let i=0;i<e.length;i++){const s=.5*(1-Math.cos(2*Math.PI*i/(e.length-1)));t[i]=e[i]*s}break;case"hamming":for(let i=0;i<e.length;i++){const s=.54-.46*Math.cos(2*Math.PI*i/(e.length-1));t[i]=e[i]*s}break;default:return e}return t}preemphasis(e,t=.97){const i=new Float32Array(e.length);i[0]=e[0];for(let s=1;s<e.length;s++)i[s]=e[s]-t*e[s-1];return i}postprocess(e){const t=this.normalizeFeatures(e),i=this.smoothFeatures(t);return this.addContext(i)}normalizeFeatures(e){const t={...e};if(e.melSpectrogram){const i=e.melSpectrogram,s=i.reduce((e,t)=>e+t,0)/i.length,a=i.reduce((e,t)=>e+(t-s)**2,0)/i.length,n=Math.sqrt(a);n>1e-8&&(t.melSpectrogram=i.map(e=>(e-s)/n))}return t}smoothFeatures(e){const t={...e};if(e.melSpectrogram){const i=3,s=e.melSpectrogram,a=new Float32Array(s.length);for(let e=0;e<s.length;e++){let t=0,n=0;for(let a=Math.max(0,e-i);a<=Math.min(s.length-1,e+i);a++)t+=s[a],n++;a[e]=t/n}t.melSpectrogram=a}return t}addContext(e){return{...e,context:{sampleRate:this.config.sampleRate,frameLength:this.config.frameLength,hopLength:this.config.hopLength,timestamp:Date.now()}}}getStatus(){return{isInitialized:this.isInitialized,config:this.config,bufferSize:this.audioBuffer.length}}dispose(){this.featureExtractor&&this.featureExtractor.dispose(),this.audioBuffer=[],this.isInitialized=!1}}class v{constructor(){this.isRunning=!1,this.startTime=null,this.metrics={audioProcessing:{totalCalls:0,totalTime:0,averageTime:0,maxTime:0,minTime:1/0},aiInference:{totalCalls:0,totalTime:0,averageTime:0,maxTime:0,minTime:1/0},rendering:{totalFrames:0,totalTime:0,averageTime:0,fps:0,maxTime:0,minTime:1/0},memory:{current:0,peak:0,average:0,samples:[]},cpu:{current:0,peak:0,average:0,samples:[]}},this.config={sampleInterval:1e3,maxSamples:60,enableDetailedLogging:!1,warningThresholds:{audioProcessingTime:50,aiInferenceTime:200,renderingTime:16.67,memoryUsage:104857600,cpuUsage:80}},this.monitoringInterval=null,this.lastCpuTime=null}start(){this.isRunning||(this.isRunning=!0,this.startTime=performance.now(),this.monitoringInterval=setInterval(()=>{this.sampleMetrics()},this.config.sampleInterval),this.sampleMetrics())}stop(){this.isRunning&&(this.isRunning=!1,this.monitoringInterval&&(clearInterval(this.monitoringInterval),this.monitoringInterval=null))}recordAudioProcessing(e=0){this.updateMetric("audioProcessing",e),e>this.config.warningThresholds.audioProcessingTime&&this.emitWarning("audioProcessing",e)}recordAIInference(e=0){this.updateMetric("aiInference",e),e>this.config.warningThresholds.aiInferenceTime&&this.emitWarning("aiInference",e)}recordRendering(e=0){this.updateMetric("rendering",e),e>0&&(this.metrics.rendering.fps=1e3/e),e>this.config.warningThresholds.renderingTime&&this.emitWarning("rendering",e)}updateMetric(e,t){const i=this.metrics[e];i&&(i.totalCalls++,i.totalTime+=t,i.averageTime=i.totalTime/i.totalCalls,i.maxTime=Math.max(i.maxTime,t),i.minTime=Math.min(i.minTime,t),this.config.enableDetailedLogging)}sampleMetrics(){this.sampleMemoryUsage(),this.sampleCpuUsage(),this.checkWarningThresholds()}sampleMemoryUsage(){let e=0;performance.memory?e=performance.memory.usedJSHeapSize:navigator.deviceMemory&&(e=1024*navigator.deviceMemory*1024*.1);const t=this.metrics.memory;t.current=e,t.peak=Math.max(t.peak,e),t.samples.push({timestamp:Date.now(),value:e}),t.samples.length>this.config.maxSamples&&t.samples.shift(),t.average=t.samples.reduce((e,t)=>e+t.value,0)/t.samples.length}sampleCpuUsage(){const e=performance.now();if(this.lastCpuTime){const t=e-this.lastCpuTime,i=.8*this.config.sampleInterval,s=Math.max(0,Math.min(100,100*(1-i/t))),a=this.metrics.cpu;a.current=s,a.peak=Math.max(a.peak,s),a.samples.push({timestamp:Date.now(),value:s}),a.samples.length>this.config.maxSamples&&a.samples.shift(),a.average=a.samples.reduce((e,t)=>e+t.value,0)/a.samples.length}this.lastCpuTime=e}checkWarningThresholds(){const e=this.config.warningThresholds;this.metrics.memory.current>e.memoryUsage&&this.emitWarning("memory",this.metrics.memory.current),this.metrics.cpu.current>e.cpuUsage&&this.emitWarning("cpu",this.metrics.cpu.current)}emitWarning(e,t){const i={type:e,value:t,threshold:this.config.warningThresholds[e],timestamp:Date.now()};"undefined"!=typeof window&&window.eventBus&&window.eventBus.emit("UI_PERFORMANCE_WARNING",i)}getStats(){return{isRunning:this.isRunning,uptime:this.startTime?performance.now()-this.startTime:0,metrics:JSON.parse(JSON.stringify(this.metrics)),summary:this.getSummary()}}getSummary(){return{audioProcessing:{averageTime:this.metrics.audioProcessing.averageTime,callsPerSecond:this.getCallsPerSecond("audioProcessing")},aiInference:{averageTime:this.metrics.aiInference.averageTime,callsPerSecond:this.getCallsPerSecond("aiInference")},rendering:{fps:this.metrics.rendering.fps,averageTime:this.metrics.rendering.averageTime},memory:{current:this.formatBytes(this.metrics.memory.current),peak:this.formatBytes(this.metrics.memory.peak),average:this.formatBytes(this.metrics.memory.average)},cpu:{current:`${this.metrics.cpu.current.toFixed(1)}%`,peak:`${this.metrics.cpu.peak.toFixed(1)}%`,average:`${this.metrics.cpu.average.toFixed(1)}%`}}}getCallsPerSecond(e){const t=this.metrics[e],i=this.startTime?(performance.now()-this.startTime)/1e3:1;return t.totalCalls/i}formatBytes(e){if(0===e)return"0 B";const t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["B","KB","MB","GB"][t]}reset(){Object.keys(this.metrics).forEach(e=>{const t=this.metrics[e];void 0!==t.totalCalls&&(t.totalCalls=0,t.totalTime=0,t.averageTime=0,t.maxTime=0,t.minTime=1/0),t.samples&&(t.samples=[])}),this.startTime=performance.now()}updateConfig(e){this.config={...this.config,...e}}dispose(){this.stop()}}class b{constructor(){this.audioContext=null,this.analyser=null,this.sourceNode=null,this.audioProcessor=null,this.isInitialized=!1,this.isProcessing=!1,this.performanceMonitor=new v,this.config={sampleRate:16e3,fftSize:2048,smoothingTimeConstant:.8,processingInterval:100},this.handleAudioData=this.handleAudioData.bind(this)}async initialize(e){try{if(this.isInitialized)return;this.audioContext=new(window.AudioContext||window.webkitAudioContext)({sampleRate:this.config.sampleRate});try{this.sourceNode=this.audioContext.createMediaElementSource(e)}catch(t){throw new Error("Failed to create audio source")}this.analyser=this.audioContext.createAnalyser(),this.analyser.fftSize=this.config.fftSize,this.analyser.smoothingTimeConstant=this.config.smoothingTimeConstant,this.audioProcessor=new C(this.audioContext,this.config),await this.audioProcessor.initialize(),this.sourceNode.connect(this.analyser),this.analyser.connect(this.audioContext.destination),this.setupVideoEventListeners(e),this.isInitialized=!0,y.emit(a,{sampleRate:this.audioContext.sampleRate,bufferSize:this.analyser.frequencyBinCount})}catch(t){throw y.emit(h,{error:t.message}),t}}setupVideoEventListeners(e){e.addEventListener("play",()=>{"suspended"===this.audioContext.state&&this.audioContext.resume(),this.startProcessing()}),e.addEventListener("pause",()=>{this.stopProcessing()}),e.addEventListener("ended",()=>{this.stopProcessing()})}startProcessing(){this.isInitialized&&(this.isProcessing||(this.isProcessing=!0,this.processingLoop(),y.emit(n)))}stopProcessing(){this.isProcessing&&(this.isProcessing=!1,y.emit(o))}async processingLoop(){if(this.isProcessing)try{const e=performance.now(),t=this.getAudioData();if(this.hasValidAudio(t)){const e=await this.audioProcessor.process(t);y.emit(r,{raw:t,processed:e,timestamp:Date.now()})}const i=performance.now()-e;this.performanceMonitor.recordProcessingTime(i),setTimeout(()=>this.processingLoop(),this.config.processingInterval)}catch(e){y.emit(h,{error:e.message}),setTimeout(()=>this.processingLoop(),2*this.config.processingInterval)}}getAudioData(){const e=this.analyser.frequencyBinCount,t=new Uint8Array(e),i=new Uint8Array(e);return this.analyser.getByteTimeDomainData(t),this.analyser.getByteFrequencyData(i),{timeDomain:t,frequency:i,sampleRate:this.audioContext.sampleRate,bufferLength:e}}hasValidAudio(e){const t=Math.max(...e.frequency),i=e.frequency.reduce((e,t)=>e+t,0)/e.frequency.length;return t>0&&i>1}handleAudioData(e){y.emit(r,e)}getAudioContextInfo(){return this.audioContext?{state:this.audioContext.state,sampleRate:this.audioContext.sampleRate,currentTime:this.audioContext.currentTime,baseLatency:this.audioContext.baseLatency||0,outputLatency:this.audioContext.outputLatency||0}:null}getPerformanceStats(){return this.performanceMonitor.getStats()}updateConfig(e){this.config={...this.config,...e},this.analyser&&e.fftSize&&(this.analyser.fftSize=e.fftSize),this.analyser&&void 0!==e.smoothingTimeConstant&&(this.analyser.smoothingTimeConstant=e.smoothingTimeConstant)}dispose(){this.stopProcessing(),this.audioProcessor&&this.audioProcessor.dispose(),this.audioContext&&this.audioContext.close(),this.isInitialized=!1}}var M=i(6293);i(2742);class S{constructor(){this.model=null,this.classNames=null,this.isInitialized=!1,this.config={modelUrl:"https://tfhub.dev/google/tfjs-model/yamnet/tfjs/1",classMapUrl:"https://raw.githubusercontent.com/tensorflow/models/master/research/audioset/yamnet/yamnet_class_map.csv",sampleRate:16e3,patchWindowSeconds:.96,patchHopSeconds:.48,confidenceThreshold:.3,topK:5},this.inputSpecs={sampleRate:16e3,expectedSamples:15600,melBins:64,patchFrames:96},this.stats={totalPredictions:0,averageInferenceTime:0,lastInferenceTime:0}}async initialize(){try{await this.loadModel(),await this.loadClassMap(),await this.warmupModel(),this.isInitialized=!0}catch(e){throw e}}async loadModel(){try{this.model=await M.loadGraphModel(this.config.modelUrl)}catch(e){throw new Error(`Failed to load YAMNet model: ${e.message}`)}}async loadClassMap(){try{const e=await fetch(this.config.classMapUrl);if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const t=await e.text();this.classNames=this.parseClassMap(t)}catch(e){this.classNames=this.getDefaultClassMap()}}parseClassMap(e){const t=e.trim().split("\n"),i=[];for(let s=1;s<t.length;s++){const e=t[s].trim();if(e){const t=e.split(",");if(t.length>=3){const e=t[2].replace(/"/g,"").trim();i.push(e)}}}return i}getDefaultClassMap(){return["Speech","Music","Laughter","Applause","Crying","Footsteps","Door","Knock","Glass","Gunshot","Siren","Car","Dog","Cat","Bird","Water","Wind","Rain","Thunder","Fire","Explosion","Whistle","Bell","Horn","Alarm","Phone","Computer","Television","Radio","Microwave","Vacuum","Washing machine","Dishwasher"]}async warmupModel(){try{const e=M.zeros([this.inputSpecs.expectedSamples]),t=performance.now(),i=await this.model.predict(e);performance.now();e.dispose(),Array.isArray(i)?i.forEach(e=>e.dispose()):i.dispose()}catch(e){}}async classify(e){if(!this.isInitialized)throw new Error("YAMNetClassifier not initialized");const t=performance.now();try{const i=this.preprocessAudio(e),s=await this.model.predict(i),a=await this.postprocessPredictions(s);i.dispose(),Array.isArray(s)?s.forEach(e=>e.dispose()):s.dispose();const n=performance.now()-t;return this.updateStats(n),{predictions:a,inferenceTime:n,timestamp:Date.now(),modelType:"yamnet"}}catch(i){throw i}}preprocessAudio(e){if(e.timeData)return this.preprocessTimeData(e.timeData);if(e.melSpectrogram)return this.preprocessMelSpectrogram(e.melSpectrogram);if(e.frequency){const t=this.reconstructTimeData(e.frequency);return this.preprocessTimeData(t)}throw new Error("No suitable audio data found for YAMNet input")}preprocessTimeData(e){let t;if(e.length>this.inputSpecs.expectedSamples){const i=Math.floor((e.length-this.inputSpecs.expectedSamples)/2);t=e.slice(i,i+this.inputSpecs.expectedSamples)}else e.length<this.inputSpecs.expectedSamples?(t=new Float32Array(this.inputSpecs.expectedSamples),t.set(e)):t=e;return M.tensor1d(t)}preprocessMelSpectrogram(e){const t=e.flat(),i=this.inputSpecs.expectedSamples,s=new Float32Array(i),a=t.length/i;for(let n=0;n<i;n++){const e=Math.floor(n*a);s[n]=t[e]||0}return M.tensor1d(s)}reconstructTimeData(e){const t=new Float32Array(e.length);for(let i=0;i<e.length;i++)t[i]=(e[i]-128)/128;return t}async postprocessPredictions(e){let t;t=Array.isArray(e)?e[0]:e;const i=await t.data(),s=[];for(let a=0;a<i.length&&a<this.classNames.length;a++)i[a]>=this.config.confidenceThreshold&&s.push({className:this.classNames[a]||`Class_${a}`,confidence:i[a],classIndex:a});return s.sort((e,t)=>t.confidence-e.confidence).slice(0,this.config.topK)}updateStats(e){this.stats.totalPredictions++,this.stats.lastInferenceTime=e;this.stats.averageInferenceTime=.9*this.stats.averageInferenceTime+.1*e}getInfo(){return{isInitialized:this.isInitialized,modelType:"YAMNet",classCount:this.classNames?this.classNames.length:0,config:this.config,inputSpecs:this.inputSpecs,stats:this.stats}}updateConfig(e){this.config={...this.config,...e}}getClassNames(){return this.classNames?[...this.classNames]:[]}getStats(){return{...this.stats}}dispose(){this.model&&(this.model.dispose(),this.model=null),this.classNames=null,this.isInitialized=!1}}class T{constructor(){this.model=null,this.commands=null,this.isInitialized=!1,this.config={modelUrl:"https://storage.googleapis.com/tfjs-models/tfjs/speech-commands/v0.4/browser_fft/18w/model.json",vocabularyUrl:"https://storage.googleapis.com/tfjs-models/tfjs/speech-commands/v0.4/browser_fft/18w/metadata.json",sampleRate:44100,fftSize:1024,confidenceThreshold:.7,topK:3,enableCustomCommands:!0},this.customCommands={是:"yes",否:"no",不:"no",开始:"go",停止:"stop",暂停:"stop",上:"up",下:"down",左:"left",右:"right"},this.stats={totalDetections:0,averageInferenceTime:0,lastInferenceTime:0,commandCounts:{}}}async initialize(){try{await this.loadModel(),await this.loadVocabulary(),await this.warmupModel(),this.isInitialized=!0}catch(e){throw e}}async loadModel(){try{this.model=await M.loadLayersModel(this.config.modelUrl)}catch(e){throw new Error(`Failed to load Speech Commands model: ${e.message}`)}}async loadVocabulary(){try{const e=await fetch(this.config.vocabularyUrl);if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const t=await e.json();this.commands=t.words||this.getDefaultCommands()}catch(e){this.commands=this.getDefaultCommands()}}getDefaultCommands(){return["_silence_","_unknown_","yes","no","up","down","left","right","on","off","stop","go","zero","one","two","three","four","five","six","seven","eight","nine"]}async warmupModel(){try{const e=this.model.inputs[0].shape,t=M.zeros(e.slice(1)),i=t.expandDims(0),s=performance.now(),a=this.model.predict(i);performance.now();t.dispose(),i.dispose(),a.dispose()}catch(e){}}async detect(e){if(!this.isInitialized)throw new Error("SpeechCommandDetector not initialized");const t=performance.now();try{const i=this.preprocessAudio(e),s=this.model.predict(i),a=await this.postprocessPredictions(s);i.dispose(),s.dispose();const n=performance.now()-t;return this.updateStats(n,a),{commands:a,inferenceTime:n,timestamp:Date.now(),modelType:"speech_commands"}}catch(i){throw i}}preprocessAudio(e){if(e.melSpectrogram)return this.preprocessSpectrogram(e.melSpectrogram);if(e.stft)return this.preprocessSTFT(e.stft);if(e.frequency)return this.preprocessFrequencyData(e.frequency);throw new Error("No suitable audio data found for speech command detection")}preprocessSpectrogram(e){const t=this.model.inputs[0].shape,[,i,s,a]=t,n=new Float32Array(i*s*a);for(let o=0;o<Math.min(e.length,i);o++){const t=e[o];for(let e=0;e<Math.min(t.length,s);e++){const i=o*s*a+e*a;n[i]=t[e]||0;for(let e=1;e<a;e++)n[i+e]=n[i]}}return M.tensor3d(n,[i,s,a]).expandDims(0)}preprocessSTFT(e){const t=e.map(e=>{const t=new Float32Array(e.real.length);for(let i=0;i<e.real.length;i++)t[i]=e.real[i]*e.real[i]+e.imag[i]*e.imag[i];return t});return this.preprocessSpectrogram(t)}preprocessFrequencyData(e){const t=this.model.inputs[0].shape,[,i,s,a]=t,n=new Float32Array(i*s*a);for(let o=0;o<i;o++)for(let t=0;t<s;t++){const i=(e[Math.floor(t*e.length/s)]||0)/255,r=o*s*a+t*a;for(let e=0;e<a;e++)n[r+e]=i}return M.tensor3d(n,[i,s,a]).expandDims(0)}async postprocessPredictions(e){const t=await e.data(),i=[];for(let s=0;s<t.length&&s<this.commands.length;s++){const e=t[s];if(e>=this.config.confidenceThreshold){const t=this.commands[s];if(("_silence_"===t||"_unknown_"===t)&&e<.9)continue;i.push({command:t,confidence:e,commandIndex:s,className:t,classIndex:s})}}return i.sort((e,t)=>t.confidence-e.confidence).slice(0,this.config.topK)}updateStats(e,t){this.stats.totalDetections++,this.stats.lastInferenceTime=e;this.stats.averageInferenceTime=.9*this.stats.averageInferenceTime+.1*e,t.forEach(e=>{const t=e.command;this.stats.commandCounts[t]=(this.stats.commandCounts[t]||0)+1})}getInfo(){return{isInitialized:this.isInitialized,modelType:"SpeechCommands",commandCount:this.commands?this.commands.length:0,config:this.config,stats:this.stats}}getClassCount(){return this.commands?this.commands.length:0}updateConfig(e){this.config={...this.config,...e}}getCommands(){return this.commands?[...this.commands]:[]}getStats(){return{...this.stats}}dispose(){this.model&&(this.model.dispose(),this.model=null),this.commands=null,this.isInitialized=!1}}class x{constructor(){this.models=new Map,this.modelCache=new Map,this.isInitialized=!1,this.config={enableCaching:!0,maxCacheSize:3,enablePreloading:!0,modelTimeout:3e4,retryAttempts:3},this.modelRegistry={yamnet:{url:"https://tfhub.dev/google/tfjs-model/yamnet/tfjs/1",type:"graph",size:"~5MB",description:"YAMNet audio event classification"},speechCommands:{url:"https://storage.googleapis.com/tfjs-models/tfjs/speech-commands/v0.4/browser_fft/18w/model.json",type:"layers",size:"~2MB",description:"Speech commands recognition"}},this.stats={modelsLoaded:0,totalLoadTime:0,cacheHits:0,cacheMisses:0,errors:0}}async initialize(){try{await M.ready(),this.setupMemoryManagement(),this.config.enablePreloading&&await this.preloadModels(),this.isInitialized=!0}catch(e){throw e}}setupMemoryManagement(){M.env().set("WEBGL_DELETE_TEXTURE_THRESHOLD",0),M.env().set("WEBGL_FORCE_F16_TEXTURES",!1),this.memoryMonitorInterval=setInterval(()=>{M.memory().numBytes>104857600&&this.cleanupUnusedModels()},1e4)}async preloadModels(){try{await this.loadModel("yamnet")}catch(e){}}async loadModel(e,t={}){try{if(this.config.enableCaching&&this.modelCache.has(e))return this.stats.cacheHits++,this.modelCache.get(e);this.stats.cacheMisses++;const i=this.modelRegistry[e];if(!i)throw new Error(`Unknown model: ${e}`);const s=performance.now(),a=await this.loadModelWithRetry(i,t),n=performance.now()-s;return this.config.enableCaching&&this.cacheModel(e,a),this.stats.modelsLoaded++,this.stats.totalLoadTime+=n,a}catch(i){throw this.stats.errors++,i}}async loadModelWithRetry(e,t){let i;for(let a=1;a<=this.config.retryAttempts;a++)try{const i=this.loadModelFromUrl(e,t),s=new Promise((e,t)=>{setTimeout(()=>t(new Error("Model load timeout")),this.config.modelTimeout)});return await Promise.race([i,s])}catch(s){if(i=s,a<this.config.retryAttempts){const e=1e3*Math.pow(2,a);await new Promise(t=>setTimeout(t,e))}}throw i}async loadModelFromUrl(e,t){const i={...t,onProgress:e=>{}};if("graph"===e.type)return await M.loadGraphModel(e.url,i);if("layers"===e.type)return await M.loadLayersModel(e.url,i);throw new Error(`Unsupported model type: ${e.type}`)}cacheModel(e,t){if(this.modelCache.size>=this.config.maxCacheSize){const e=this.modelCache.keys().next().value;this.removeFromCache(e)}this.modelCache.set(e,t)}removeFromCache(e){if(this.modelCache.has(e)){this.modelCache.get(e).dispose(),this.modelCache.delete(e)}}cleanupUnusedModels(){M.memory()}getModelInfo(e){const t=this.modelRegistry[e];return t?{...t,cached:this.modelCache.has(e),loaded:this.models.has(e)}:null}getAvailableModels(){return Object.keys(this.modelRegistry).map(e=>({name:e,...this.getModelInfo(e)}))}getCacheStatus(){return{size:this.modelCache.size,maxSize:this.config.maxCacheSize,models:Array.from(this.modelCache.keys()),hitRate:this.stats.cacheHits/(this.stats.cacheHits+this.stats.cacheMisses)||0}}getStats(){return{...this.stats,averageLoadTime:this.stats.modelsLoaded>0?this.stats.totalLoadTime/this.stats.modelsLoaded:0,memoryUsage:M.memory(),cacheStatus:this.getCacheStatus()}}async warmupModel(e){try{const t=await this.loadModel(e),i=t.inputs[0].shape,s=M.zeros(i.slice(1)),a=s.expandDims(0),n=t.predict(a);s.dispose(),a.dispose(),Array.isArray(n)?n.forEach(e=>e.dispose()):n.dispose()}catch(t){}}dispose(){for(const[e,t]of this.modelCache)t.dispose();this.modelCache.clear(),this.models.clear(),this.memoryMonitorInterval&&clearInterval(this.memoryMonitorInterval),this.isInitialized=!1}}class z{constructor(){this.modelManager=new x,this.yamnetClassifier=null,this.speechCommandDetector=null,this.isInitialized=!1,this.isProcessing=!1,this.config={enableYAMNet:!0,enableSpeechCommands:!0,confidenceThreshold:.5,maxPredictions:5,processingMode:"realtime",enableGPU:!0},this.stats={totalInferences:0,averageInferenceTime:0,lastInferenceTime:0,errorCount:0}}async initialize(){try{await this.setupTensorFlowBackend(),await this.modelManager.initialize(),await this.loadModels(),this.isInitialized=!0,y.emit(c,{models:this.getLoadedModels(),backend:M.getBackend(),memory:M.memory()})}catch(e){throw y.emit(l,{error:e.message}),e}}async setupTensorFlowBackend(){try{if(this.config.enableGPU&&await M.ready()){M.engine().backendNames().includes("webgl")?await M.setBackend("webgl"):await M.setBackend("cpu")}else await M.setBackend("cpu");await M.ready(),M.env().set("WEBGL_DELETE_TEXTURE_THRESHOLD",0),M.env().set("WEBGL_FORCE_F16_TEXTURES",!1)}catch(e){throw e}}async loadModels(){const e=[];this.config.enableYAMNet&&e.push(this.loadYAMNetModel()),this.config.enableSpeechCommands&&e.push(this.loadSpeechCommandModel()),await Promise.all(e)}async loadYAMNetModel(){try{this.yamnetClassifier=new S,await this.yamnetClassifier.initialize()}catch(e){throw this.config.enableYAMNet=!1,e}}async loadSpeechCommandModel(){try{this.speechCommandDetector=new T,await this.speechCommandDetector.initialize()}catch(e){this.config.enableSpeechCommands=!1}}async classify(e){if(!this.isInitialized)throw new Error("AIClassifier not initialized");const t=performance.now();try{y.emit(d);const i={timestamp:Date.now(),predictions:[],audioEvents:[],speechCommands:[],confidence:0,processingTime:0},s=[];this.yamnetClassifier&&this.config.enableYAMNet&&s.push(this.yamnetClassifier.classify(e).then(e=>({type:"yamnet",result:e})).catch(e=>({type:"yamnet",error:e}))),this.speechCommandDetector&&this.config.enableSpeechCommands&&s.push(this.speechCommandDetector.detect(e).then(e=>({type:"speech",result:e})).catch(e=>({type:"speech",error:e})));const a=await Promise.all(s);for(const e of a)e.error?this.stats.errorCount++:("yamnet"===e.type&&e.result&&(i.audioEvents=e.result.predictions||[],i.predictions.push(...e.result.predictions||[])),"speech"===e.type&&e.result&&(i.speechCommands=e.result.commands||[],i.predictions.push(...e.result.commands||[])));i.predictions=this.filterAndSortPredictions(i.predictions),i.confidence=i.predictions.length>0?i.predictions[0].confidence:0;const n=performance.now();return i.processingTime=n-t,this.updateStats(i.processingTime),y.emit(m,i),y.emit(u),i}catch(i){throw this.stats.errorCount++,y.emit(l,{error:i.message}),i}}filterAndSortPredictions(e){return e.filter(e=>e.confidence>=this.config.confidenceThreshold).sort((e,t)=>t.confidence-e.confidence).slice(0,this.config.maxPredictions)}updateStats(e){this.stats.totalInferences++,this.stats.lastInferenceTime=e;this.stats.averageInferenceTime=.9*this.stats.averageInferenceTime+.1*e}getLoadedModels(){const e=[];return this.yamnetClassifier&&e.push({name:"YAMNet",type:"audio_classification",classes:521,status:"loaded"}),this.speechCommandDetector&&e.push({name:"SpeechCommands",type:"speech_recognition",classes:this.speechCommandDetector.getClassCount(),status:"loaded"}),e}getModelInfo(){return{isInitialized:this.isInitialized,backend:M.getBackend(),memory:M.memory(),models:this.getLoadedModels(),config:this.config,stats:this.stats}}updateConfig(e){this.config={...this.config,...e},this.yamnetClassifier&&this.yamnetClassifier.updateConfig(e),this.speechCommandDetector&&this.speechCommandDetector.updateConfig(e)}getStats(){return{...this.stats,memoryUsage:M.memory(),backend:M.getBackend()}}cleanupMemory(){M.disposeVariables(),window.gc&&window.gc()}dispose(){this.yamnetClassifier&&this.yamnetClassifier.dispose(),this.speechCommandDetector&&this.speechCommandDetector.dispose(),this.modelManager&&this.modelManager.dispose(),this.cleanupMemory(),this.isInitialized=!1}}class I{constructor(){this.labels=new Map,this.container=null,this.isInitialized=!1,this.config={maxLabels:5,labelDuration:3e3,fadeInDuration:300,fadeOutDuration:500,position:"top-right",fontSize:"medium",enableAnimations:!0},this.styles={container:{position:"fixed",zIndex:"10000",pointerEvents:"none",fontFamily:"Arial, sans-serif",fontSize:"14px"},label:{background:"rgba(0, 0, 0, 0.8)",color:"white",padding:"8px 12px",borderRadius:"4px",marginBottom:"4px",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.3)",transition:"all 0.3s ease",maxWidth:"200px",wordWrap:"break-word"}},this.labelIdCounter=0}initialize(){this.isInitialized||(this.createContainer(),this.isInitialized=!0)}createContainer(){this.container=document.createElement("div"),this.container.id="audio-labels-container",Object.assign(this.container.style,this.styles.container),this.updateContainerPosition(),document.body.appendChild(this.container)}updateContainerPosition(){if(this.container)switch(this.container.style.top="",this.container.style.bottom="",this.container.style.left="",this.container.style.right="",this.config.position){case"top-left":this.container.style.top="20px",this.container.style.left="20px";break;case"top-right":default:this.container.style.top="20px",this.container.style.right="20px";break;case"bottom-left":this.container.style.bottom="20px",this.container.style.left="20px";break;case"bottom-right":this.container.style.bottom="20px",this.container.style.right="20px"}}showLabel(e,t=1,i={}){this.isInitialized||this.initialize();const s="label-"+ ++this.labelIdCounter,a=this.createLabelElement(e,t,i);return this.enforceMaxLabels(),this.container.appendChild(a),this.labels.set(s,{element:a,text:e,confidence:t,timestamp:Date.now(),options:i}),this.config.enableAnimations&&this.animateIn(a),setTimeout(()=>{this.hideLabel(s)},this.config.labelDuration),s}createLabelElement(e,t,i){const s=document.createElement("div");s.className="audio-event-label",Object.assign(s.style,this.styles.label);const a=Math.max(.6,t);s.style.background=`rgba(0, 0, 0, ${.8*a})`;const n=this.getEventColor(e);n&&(s.style.borderLeft=`4px solid ${n}`);const o=t<1?` (${(100*t).toFixed(0)}%)`:"";return s.innerHTML=`\n      <div style="font-weight: bold; margin-bottom: 2px;">\n        ${this.formatEventText(e)}\n      </div>\n      ${o?`<div style="font-size: 0.8em; opacity: 0.8;">${o}</div>`:""}\n    `,i.style&&Object.assign(s.style,i.style),s}formatEventText(e){return`${{Speech:"🗣️",Music:"🎵",Laughter:"😄",Applause:"👏",Crying:"😢",Footsteps:"👣",Door:"🚪",Knock:"✊",Glass:"🥃",Gunshot:"💥",Siren:"🚨",Car:"🚗",Dog:"🐕",Cat:"🐱",Bird:"🐦",Water:"💧",Wind:"💨",Rain:"🌧️",Thunder:"⛈️",Fire:"🔥",Explosion:"💥",Whistle:"🎵",Bell:"🔔",Horn:"📯",Alarm:"⏰",Phone:"📞"}[e]||"🔊"} ${e}`}getEventColor(e){return{Speech:"#4CAF50",Music:"#2196F3",Gunshot:"#F44336",Explosion:"#FF5722",Siren:"#FF9800",Alarm:"#FF9800",Crying:"#9C27B0",Laughter:"#FFEB3B",Applause:"#4CAF50"}[e]||null}animateIn(e){e.style.opacity="0",e.style.transform="translateX(20px)",e.offsetHeight,e.style.transition=`all ${this.config.fadeInDuration}ms ease`,e.style.opacity="1",e.style.transform="translateX(0)"}animateOut(e){return new Promise(t=>{e.style.transition=`all ${this.config.fadeOutDuration}ms ease`,e.style.opacity="0",e.style.transform="translateX(20px)",setTimeout(()=>{t()},this.config.fadeOutDuration)})}async hideLabel(e){const t=this.labels.get(e);t&&(this.config.enableAnimations&&await this.animateOut(t.element),t.element.parentNode&&t.element.parentNode.removeChild(t.element),this.labels.delete(e))}enforceMaxLabels(){if(this.labels.size>=this.config.maxLabels){const e=this.labels.keys().next().value;this.hideLabel(e)}}clearAllLabels(){Array.from(this.labels.keys()).forEach(e=>{this.hideLabel(e)})}updateLabel(e,t){const i=this.labels.get(e);if(i){if(void 0!==t.text&&(i.text=t.text,i.element.querySelector("div").textContent=this.formatEventText(t.text)),void 0!==t.confidence){i.confidence=t.confidence;const e=i.element.querySelector("div:last-child");e&&(e.textContent=`(${(100*t.confidence).toFixed(0)}%)`)}t.style&&Object.assign(i.element.style,t.style)}}getCurrentLabels(){return Array.from(this.labels.entries()).map(([e,t])=>({id:e,text:t.text,confidence:t.confidence,timestamp:t.timestamp}))}updateConfig(e){if(this.config={...this.config,...e},e.position&&this.updateContainerPosition(),e.fontSize&&this.container){const t={small:"12px",medium:"14px",large:"16px"};this.container.style.fontSize=t[e.fontSize]||"14px"}}getStats(){return{currentLabels:this.labels.size,maxLabels:this.config.maxLabels,totalLabelsShown:this.labelIdCounter}}dispose(){this.clearAllLabels(),this.container&&this.container.parentNode&&this.container.parentNode.removeChild(this.container),this.container=null,this.labels.clear(),this.isInitialized=!1}}class F{constructor(){this.canvas=null,this.canvasCtx=null,this.videoElement=null,this.isInitialized=!1,this.isRunning=!1,this.animationFrameId=null,this.config={enableFrequencyDomain:!0,enableTimeDomain:!0,enableHighFrequencyWarning:!0,highFrequencyThreshold:4e3,opacity:.8,showDebugInfo:!1,colorScheme:"default"},this.colorSchemes={default:{lowFreq:{r:50,g:150,b:255},midFreq:{r:50,g:255,b:150},highFreq:{r:255,g:255,b:0},warning:{r:255,g:50,b:50},waveform:{r:255,g:255,b:255}},accessibility:{lowFreq:{r:0,g:100,b:200},midFreq:{r:0,g:200,b:100},highFreq:{r:255,g:150,b:0},warning:{r:200,g:0,b:0},waveform:{r:255,g:255,b:255}}},this.currentAudioData=null,this.stats={framesRendered:0,averageRenderTime:0,lastRenderTime:0}}async initialize(e){try{if(this.isInitialized)return;this.videoElement=e,await this.createCanvas(),this.setupResponsiveListeners(),this.isInitialized=!0}catch(t){throw t}}async createCanvas(){const e=this.findVideoContainer();if(!e)throw new Error("Could not find suitable video container");this.canvas=document.createElement("canvas"),this.canvas.id="audio-visualizer-canvas-v2",this.canvas.style.cssText=`\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      pointer-events: none;\n      opacity: ${this.config.opacity};\n      z-index: 1000;\n    `,this.canvasCtx=this.canvas.getContext("2d"),e.appendChild(this.canvas),this.updateCanvasSize()}findVideoContainer(){const e=["#movie_player",".html5-video-container",".video-stream",".ytp-player-content"];for(const t of e){const e=document.querySelector(t);if(e&&e.contains(this.videoElement))return e}return this.videoElement.parentElement}setupResponsiveListeners(){if(window.addEventListener("resize",()=>{this.updateCanvasSize()}),window.ResizeObserver){const e=new ResizeObserver(()=>{this.updateCanvasSize()});this.canvas.parentElement&&e.observe(this.canvas.parentElement)}}updateCanvasSize(){if(!this.canvas||!this.canvas.parentElement)return;const e=this.canvas.parentElement.getBoundingClientRect();this.canvas.width=e.width,this.canvas.height=e.height}start(){this.isInitialized&&!this.isRunning&&(this.isRunning=!0,this.renderLoop())}pause(){this.isRunning=!1,this.animationFrameId&&(cancelAnimationFrame(this.animationFrameId),this.animationFrameId=null)}stop(){this.pause(),this.canvasCtx&&this.canvasCtx.clearRect(0,0,this.canvas.width,this.canvas.height)}renderLoop(){if(!this.isRunning)return;const e=performance.now();try{this.canvasCtx.clearRect(0,0,this.canvas.width,this.canvas.height),this.currentAudioData?this.renderVisualization(this.currentAudioData):this.renderWaitingState(),this.config.showDebugInfo&&this.renderDebugInfo();const t=performance.now()-e;this.updateStats(t)}catch(t){}this.animationFrameId=requestAnimationFrame(()=>this.renderLoop())}renderVisualization(e){this.config.enableFrequencyDomain&&e.raw&&e.raw.frequency&&this.renderFrequencyDomain(e.raw.frequency,e.raw.sampleRate),this.config.enableTimeDomain&&e.raw&&e.raw.timeDomain&&this.renderTimeDomain(e.raw.timeDomain)}renderFrequencyDomain(e,t){const i=e.length,s=this.canvas.width/i*1.5,a=t/2,n=this.colorSchemes[this.config.colorScheme];let o=0;for(let r=0;r<i;r++){const t=e[r]/255*this.canvas.height*.5,h=r*a/i,c=e[r]/255;let l;l=this.config.enableHighFrequencyWarning&&h>this.config.highFrequencyThreshold?this.interpolateColor(n.highFreq,n.warning,c):h>1e3?this.interpolateColor(n.lowFreq,n.midFreq,c):this.interpolateColor({r:0,g:0,b:0},n.lowFreq,c),this.canvasCtx.fillStyle=`rgba(${l.r}, ${l.g}, ${l.b}, ${.8*c})`,this.canvasCtx.fillRect(o,this.canvas.height-t,s,t),o+=s+1}}renderTimeDomain(e){const t=this.colorSchemes[this.config.colorScheme];this.canvasCtx.lineWidth=2,this.canvasCtx.strokeStyle=`rgba(${t.waveform.r}, ${t.waveform.g}, ${t.waveform.b}, 0.7)`,this.canvasCtx.beginPath();const i=this.canvas.width/e.length;let s=0;for(let a=0;a<e.length;a++){const t=e[a]/128*this.canvas.height/2;0===a?this.canvasCtx.moveTo(s,t):this.canvasCtx.lineTo(s,t),s+=i}this.canvasCtx.lineTo(this.canvas.width,this.canvas.height/2),this.canvasCtx.stroke()}renderWaitingState(){this.canvasCtx.fillStyle="rgba(255, 255, 255, 0.1)",this.canvasCtx.fillRect(10,10,200,60),this.canvasCtx.fillStyle="rgba(255, 255, 255, 0.8)",this.canvasCtx.font="16px Arial",this.canvasCtx.fillText("🎵 AI Audio Analysis Ready",20,35),this.canvasCtx.fillText("Waiting for audio...",20,55)}renderDebugInfo(){this.canvasCtx.fillStyle="rgba(0, 0, 0, 0.7)",this.canvasCtx.fillRect(10,this.canvas.height-120,300,110),this.canvasCtx.fillStyle="white",this.canvasCtx.font="12px Arial";["🎨 Visualization Renderer v2.0",`Canvas: ${this.canvas.width}x${this.canvas.height}`,`Frames: ${this.stats.framesRendered}`,`Avg Render: ${this.stats.averageRenderTime.toFixed(2)}ms`,`Last Render: ${this.stats.lastRenderTime.toFixed(2)}ms`,`Color Scheme: ${this.config.colorScheme}`].forEach((e,t)=>{this.canvasCtx.fillText(e,20,this.canvas.height-100+15*t)})}interpolateColor(e,t,i){return{r:Math.round(e.r+(t.r-e.r)*i),g:Math.round(e.g+(t.g-e.g)*i),b:Math.round(e.b+(t.b-e.b)*i)}}updateVisualization(e){this.currentAudioData=e}updateStats(e){this.stats.framesRendered++,this.stats.lastRenderTime=e;this.stats.averageRenderTime=.9*this.stats.averageRenderTime+.1*e}updateConfig(e){this.config={...this.config,...e},void 0!==e.opacity&&this.canvas&&(this.canvas.style.opacity=e.opacity)}getStats(){return{...this.stats}}dispose(){this.stop(),this.canvas&&this.canvas.parentElement&&this.canvas.parentElement.removeChild(this.canvas),this.canvas=null,this.canvasCtx=null,this.videoElement=null,this.currentAudioData=null,this.isInitialized=!1}}class E{constructor(){this.config=null,this.defaultConfig=this.getDefaultConfig(),this.storageKey="youtube-aural-visual-bridge-config",this.isInitialized=!1,this.changeListeners=new Set}getDefaultConfig(){return{ai:{enableYAMNet:!0,enableSpeechCommands:!0,yamnetConfidenceThreshold:.5,speechCommandsConfidenceThreshold:.7,maxPredictions:5,enableLocalProcessing:!0,enableCloudAPI:!1,apiEndpoint:"",apiKey:""},ui:{showLabels:!0,labelDuration:3e3,maxLabelsOnScreen:5,labelPosition:"top-right",enableSoundDictionary:!0,enableVisualization:!0,visualizationOpacity:.8,fontSize:"medium",theme:"auto"},performance:{processingInterval:100,enableGPUAcceleration:!0,maxMemoryUsage:104857600,enablePerformanceMonitoring:!0,autoOptimization:!0},audio:{sampleRate:16e3,fftSize:2048,smoothingTimeConstant:.8,enablePreemphasis:!0,windowType:"hann"},visualization:{enableFrequencyDomain:!0,enableTimeDomain:!0,enableHighFrequencyWarning:!0,highFrequencyThreshold:4e3,colorScheme:"default",customColors:{lowFreq:"#0066cc",midFreq:"#00cc66",highFreq:"#cc6600",warning:"#cc0000"}},accessibility:{enableHighContrast:!1,enableLargeText:!1,enableScreenReader:!1,enableKeyboardNavigation:!0,reduceMotion:!1},debug:{enableLogging:!1,enableDetailedStats:!1,enableModelInfo:!1,logLevel:"warn"}}}async loadConfig(){try{const e=await this.loadFromStorage();this.config=this.mergeConfigs(this.defaultConfig,e),this.validateConfig(),this.isInitialized=!0}catch(e){this.config={...this.defaultConfig},this.isInitialized=!0}}async loadFromStorage(){try{return"undefined"!=typeof chrome&&chrome.storage?await this.loadFromChromeStorage():this.loadFromLocalStorage()}catch(e){return{}}}async loadFromChromeStorage(){return new Promise((e,t)=>{chrome.storage.sync.get([this.storageKey],i=>{chrome.runtime.lastError?t(chrome.runtime.lastError):e(i[this.storageKey]||{})})})}loadFromLocalStorage(){const e=localStorage.getItem(this.storageKey);return e?JSON.parse(e):{}}async saveConfig(){try{await this.saveToStorage(this.config),this.notifyConfigChange()}catch(e){throw e}}async saveToStorage(e){try{"undefined"!=typeof chrome&&chrome.storage?await this.saveToChromeStorage(e):this.saveToLocalStorage(e)}catch(t){this.saveToLocalStorage(e)}}async saveToChromeStorage(e){return new Promise((t,i)=>{chrome.storage.sync.set({[this.storageKey]:e},()=>{chrome.runtime.lastError?i(chrome.runtime.lastError):t()})})}saveToLocalStorage(e){localStorage.setItem(this.storageKey,JSON.stringify(e))}mergeConfigs(e,t){const i={};for(const s in e)"object"!=typeof e[s]||Array.isArray(e[s])?i[s]=void 0!==t[s]?t[s]:e[s]:i[s]=this.mergeConfigs(e[s],t[s]||{});return i}validateConfig(){this.config.ai.yamnetConfidenceThreshold=Math.max(0,Math.min(1,this.config.ai.yamnetConfidenceThreshold)),this.config.ai.speechCommandsConfidenceThreshold=Math.max(0,Math.min(1,this.config.ai.speechCommandsConfidenceThreshold)),this.config.ui.labelDuration=Math.max(1e3,Math.min(1e4,this.config.ui.labelDuration)),this.config.ui.maxLabelsOnScreen=Math.max(1,Math.min(10,this.config.ui.maxLabelsOnScreen)),this.config.performance.processingInterval=Math.max(50,Math.min(1e3,this.config.performance.processingInterval));["top-left","top-right","bottom-left","bottom-right"].includes(this.config.ui.labelPosition)||(this.config.ui.labelPosition="top-right");["light","dark","auto"].includes(this.config.ui.theme)||(this.config.ui.theme="auto");["small","medium","large"].includes(this.config.ui.fontSize)||(this.config.ui.fontSize="medium")}getConfig(e=null){return this.isInitialized?e?this.getNestedValue(this.config,e):this.config:e?this.getNestedValue(this.defaultConfig,e):this.defaultConfig}async setConfig(e,t){if(!this.isInitialized)throw new Error("ConfigManager not initialized");this.setNestedValue(this.config,e,t),this.validateConfig(),await this.saveConfig()}async updateConfig(e){if(!this.isInitialized)throw new Error("ConfigManager not initialized");this.config=this.mergeConfigs(this.config,e),this.validateConfig(),await this.saveConfig()}getNestedValue(e,t){return t.split(".").reduce((e,t)=>e&&e[t],e)}setNestedValue(e,t,i){const s=t.split("."),a=s.pop();s.reduce((e,t)=>(e[t]&&"object"==typeof e[t]||(e[t]={}),e[t]),e)[a]=i}async resetConfig(){this.config={...this.defaultConfig},await this.saveConfig()}exportConfig(){return JSON.stringify(this.config,null,2)}async importConfig(e){try{const t=JSON.parse(e);this.config=this.mergeConfigs(this.defaultConfig,t),this.validateConfig(),await this.saveConfig()}catch(t){throw t}}addChangeListener(e){this.changeListeners.add(e)}removeChangeListener(e){this.changeListeners.delete(e)}notifyConfigChange(){this.changeListeners.forEach(e=>{try{e(this.config)}catch(t){}})}getConfigSummary(){return{aiEnabled:this.config.ai.enableYAMNet||this.config.ai.enableSpeechCommands,uiEnabled:this.config.ui.showLabels||this.config.ui.enableVisualization,performanceOptimized:this.config.performance.enableGPUAcceleration&&this.config.performance.autoOptimization,accessibilityEnabled:Object.values(this.config.accessibility).some(e=>!0===e)}}dispose(){this.changeListeners.clear(),this.isInitialized=!1}}const L=new class{constructor(){this.audioManager=null,this.aiClassifier=null,this.labelManager=null,this.visualizationRenderer=null,this.performanceMonitor=new v,this.configManager=new E,this.isInitialized=!1,this.isRunning=!1,this.currentVideoElement=null,this.initializedVideos=new WeakSet,this.handleMutations=this.handleMutations.bind(this),this.setupVideoElement=this.setupVideoElement.bind(this),this.handleAudioData=this.handleAudioData.bind(this),this.handleClassificationResult=this.handleClassificationResult.bind(this)}async initialize(){try{await this.configManager.loadConfig(),this.performanceMonitor.start(),this.setupEventListeners(),await this.initializeComponents(),this.setupMutationObserver(),await this.checkExistingVideos(),this.isInitialized=!0,y.emit(p)}catch(e){throw y.emit(f,{error:e.message}),e}}async initializeComponents(){this.audioManager=new b,this.aiClassifier=new z,await this.aiClassifier.initialize(),this.labelManager=new I,this.visualizationRenderer=new F}setupEventListeners(){y.on(r,this.handleAudioData),y.on(m,this.handleClassificationResult),y.on(f,e=>{this.handleSystemError(e.error)}),y.on(g,e=>{this.handlePerformanceWarning(e)})}setupMutationObserver(){new MutationObserver(this.handleMutations).observe(document.body,{childList:!0,subtree:!0})}handleMutations(e){for(const t of e)"childList"===t.type&&t.addedNodes.forEach(e=>{if(1===e.nodeType){let t=null;t="VIDEO"===e.tagName?e:e.querySelector("video"),t&&!this.initializedVideos.has(t)&&this.setupVideoElement(t)}})}async checkExistingVideos(){const e=document.querySelector("video");e&&!this.initializedVideos.has(e)&&await this.setupVideoElement(e)}async setupVideoElement(e){try{if(this.initializedVideos.has(e))return;this.initializedVideos.add(e),this.currentVideoElement=e,e.readyState<3&&await new Promise(t=>{e.addEventListener("canplay",t,{once:!0})}),await this.audioManager.initialize(e),await this.visualizationRenderer.initialize(e),this.setupVideoEventListeners(e)}catch(t){y.emit(f,{error:t.message})}}setupVideoEventListeners(e){e.addEventListener("play",()=>{this.start()}),e.addEventListener("pause",()=>{this.pause()}),e.addEventListener("ended",()=>{this.stop()})}start(){this.isInitialized&&!this.isRunning&&(this.isRunning=!0,this.audioManager.startProcessing(),this.visualizationRenderer.start(),y.emit(n))}pause(){this.isRunning&&(this.audioManager.stopProcessing(),this.visualizationRenderer.pause())}stop(){this.isRunning&&(this.isRunning=!1,this.audioManager.stopProcessing(),this.visualizationRenderer.stop(),this.labelManager.clearAllLabels(),y.emit(o))}async handleAudioData(e){try{this.visualizationRenderer.updateVisualization(e),this.aiClassifier&&e.processed&&await this.aiClassifier.classify(e.processed),this.performanceMonitor.recordAudioProcessing()}catch(t){}}handleClassificationResult(e){try{if(e.predictions&&e.predictions.length>0){const t=e.predictions[0];t.confidence>.5&&this.labelManager.showLabel(t.className,t.confidence,{x:50,y:50})}this.performanceMonitor.recordAIInference()}catch(t){}}handleSystemError(e){this.labelManager.showLabel("系统错误",1,{x:10,y:10}),setTimeout(()=>{!this.isRunning&&this.currentVideoElement&&this.setupVideoElement(this.currentVideoElement)},5e3)}handlePerformanceWarning(e){e.cpuUsage>80&&this.audioManager.updateConfig({processingInterval:200})}getStatus(){var e;return{isInitialized:this.isInitialized,isRunning:this.isRunning,audioManager:null===(e=this.audioManager)||void 0===e?void 0:e.getAudioContextInfo(),performance:this.performanceMonitor.getStats(),config:this.configManager.getConfig()}}dispose(){this.stop(),this.audioManager&&this.audioManager.dispose(),this.aiClassifier&&this.aiClassifier.dispose(),this.visualizationRenderer&&this.visualizationRenderer.dispose(),this.labelManager&&this.labelManager.dispose(),y.clear()}};"loading"===document.readyState?document.addEventListener("DOMContentLoaded",()=>{L.initialize().catch(e=>{})}):L.initialize().catch(e=>{}),window.YouTubeAuralVisualBridge=L},4530:()=>{},5817:()=>{},8108:()=>{},8590:()=>{}},i={};function s(e){var a=i[e];if(void 0!==a)return a.exports;var n=i[e]={id:e,loaded:!1,exports:{}};return t[e].call(n.exports,n,n.exports,s),n.loaded=!0,n.exports}s.m=t,s.amdD=function(){throw new Error("define cannot be used indirect")},s.amdO={},e=[],s.O=(t,i,a,n)=>{if(!i){var o=1/0;for(l=0;l<e.length;l++){for(var[i,a,n]=e[l],r=!0,h=0;h<i.length;h++)(!1&n||o>=n)&&Object.keys(s.O).every(e=>s.O[e](i[h]))?i.splice(h--,1):(r=!1,n<o&&(o=n));if(r){e.splice(l--,1);var c=a();void 0!==c&&(t=c)}}return t}n=n||0;for(var l=e.length;l>0&&e[l-1][2]>n;l--)e[l]=e[l-1];e[l]=[i,a,n]},s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var i in t)s.o(t,i)&&!s.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},s.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),s.j=854,(()=>{var e={854:0};s.O.j=t=>0===e[t];var t=(t,i)=>{var a,n,[o,r,h]=i,c=0;if(o.some(t=>0!==e[t])){for(a in r)s.o(r,a)&&(s.m[a]=r[a]);if(h)var l=h(s)}for(t&&t(i);c<o.length;c++)n=o[c],s.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return s.O(l)},i=self.webpackChunkyoutube_aural_visual_bridge=self.webpackChunkyoutube_aural_visual_bridge||[];i.forEach(t.bind(null,0)),i.push=t.bind(null,i.push.bind(i))})();var a=s.O(void 0,[340,121],()=>s(2627));a=s.O(a)})();
//# sourceMappingURL=content.js.map