(()=>{"use strict";var e,t={3996:(e,t,r)=>{r(3266);let n=!1,a={sampleRate:16e3,frameLength:1024,hopLength:512,melBins:64,fftSize:2048};function o(e){const t={},r=function(e){const t=a.frameLength,r=a.hopLength,n=Math.floor((e.length-t)/r)+1,o=[];for(let a=0;a<n;a++){const n=a*r,i=e.slice(n,n+t),s=new Float32Array(t/2),l=new Float32Array(t/2);for(let e=0;e<t/2;e++)for(let r=0;r<t;r++){const n=-2*Math.PI*e*r/t;s[e]+=i[r]*Math.cos(n),l[e]+=i[r]*Math.sin(n)}o.push({real:s,imag:l})}return o}(e.timeData||e);t.stft=r;const n=function(e){return e.map(e=>{const t=new Float32Array(e.real.length);for(let r=0;r<e.real.length;r++)t[r]=e.real[r]*e.real[r]+e.imag[r]*e.imag[r];return t})}(r);t.powerSpectrum=n;const o=function(e){return e.map(e=>{const t=new Float32Array(a.melBins),r=e.length/a.melBins;for(let n=0;n<a.melBins;n++){const a=Math.floor(n*r),o=Math.floor((n+1)*r);let i=0;for(let t=a;t<o&&t<e.length;t++)i+=e[t];t[n]=i/(o-a)}return t})}(n);return t.melSpectrogram=o,t.spectralCentroid=function(e){return e.map(e=>{let t=0,r=0;for(let n=0;n<e.length;n++){t+=n*a.sampleRate/(2*e.length)*e[n],r+=e[n]}return r>0?t/r:0})}(n),t.zeroCrossingRate=function(e){let t=0;for(let r=1;r<e.length;r++)e[r]>=0!=e[r-1]>=0&&t++;return t/(e.length-1)}(e.timeData||e),t}function i(e){let t=0;for(let a=0;a<e.length;a++)t+=e[a]*e[a];if(t=Math.sqrt(t/e.length),t<1e-8)return e;const r=new Float32Array(e.length),n=.95/t;for(let a=0;a<e.length;a++)r[a]=Math.max(-1,Math.min(1,e[a]*n));return r}self.addEventListener("message",async function(e){const{type:t,data:r,id:s}=e.data;try{let e;switch(t){case"INITIALIZE":e=await async function(e={}){if(n)return{message:"Already initialized"};try{return a={...a,...e},await async function(){}(),n=!0,{message:"Audio Worker initialized successfully",config:a}}catch(t){throw t}}(r);break;case"PROCESS_AUDIO":e=await async function(e){if(!n)throw new Error("Audio Worker not initialized");const t=performance.now();try{const r=function(e){const t=function(e){if(e.timeDomain){const t=new Float32Array(e.timeDomain.length);for(let r=0;r<e.timeDomain.length;r++)t[r]=(e.timeDomain[r]-128)/128;return t}if(e.frequency){const t=new Float32Array(e.frequency.length);for(let r=0;r<e.frequency.length;r++)t[r]=(e.frequency[r]-128)/128;return t}throw new Error("No suitable audio data found")}(e),r=function(e,t,r){if(t===r)return e;const n=t/r,a=Math.floor(e.length/n),o=new Float32Array(a);for(let i=0;i<a;i++){const t=i*n,r=Math.floor(t),a=Math.min(r+1,e.length-1),s=t-r;o[i]=e[r]*(1-s)+e[a]*s}return o}(t,e.sampleRate||44100,a.sampleRate);return{timeData:i(r),sampleRate:a.sampleRate,frameLength:a.frameLength,hopLength:a.hopLength}}(e),n=o(r);return{raw:e,preprocessed:r,features:n,processingTime:performance.now()-t,timestamp:Date.now()}}catch(r){throw r}}(r);break;case"EXTRACT_FEATURES":e=await async function(e){if(!n)throw new Error("Audio Worker not initialized");const t=performance.now();try{const r=o(e);return{features:r,extractionTime:performance.now()-t,timestamp:Date.now()}}catch(r){throw r}}(r);break;case"UPDATE_CONFIG":l=r,a={...a,...l},e={message:"Config updated",config:a};break;case"GET_STATUS":e={isInitialized:n,config:a};break;default:throw new Error(`Unknown message type: ${t}`)}self.postMessage({id:s,success:!0,result:e})}catch(c){self.postMessage({id:s,success:!1,error:c.message})}var l})}},r={};function n(e){var a=r[e];if(void 0!==a)return a.exports;var o=r[e]={id:e,loaded:!1,exports:{}};return t[e].call(o.exports,o,o.exports,n),o.loaded=!0,o.exports}n.m=t,n.amdD=function(){throw new Error("define cannot be used indirect")},n.amdO={},e=[],n.O=(t,r,a,o)=>{if(!r){var i=1/0;for(f=0;f<e.length;f++){for(var[r,a,o]=e[f],s=!0,l=0;l<r.length;l++)(!1&o||i>=o)&&Object.keys(n.O).every(e=>n.O[e](r[l]))?r.splice(l--,1):(s=!1,o<i&&(i=o));if(s){e.splice(f--,1);var c=a();void 0!==c&&(t=c)}}return t}o=o||0;for(var f=e.length;f>0&&e[f-1][2]>o;f--)e[f]=e[f-1];e[f]=[r,a,o]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{var e={614:0};n.O.j=t=>0===e[t];var t=(t,r)=>{var a,o,[i,s,l]=r,c=0;if(i.some(t=>0!==e[t])){for(a in s)n.o(s,a)&&(n.m[a]=s[a]);if(l)var f=l(n)}for(t&&t(r);c<i.length;c++)o=i[c],n.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return n.O(f)},r=self.webpackChunkyoutube_aural_visual_bridge=self.webpackChunkyoutube_aural_visual_bridge||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var a=n.O(void 0,[121],()=>n(3996));a=n.O(a)})();
//# sourceMappingURL=audio-worker.js.map