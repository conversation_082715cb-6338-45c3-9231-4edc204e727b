{"version": 3, "file": "audio-worker.js", "mappings": "uBAAIA,E,0BCKJ,IAAIC,GAAgB,EAChBC,EAAS,CACXC,WAAY,KACZC,YAAa,KACbC,UAAW,IACXC,QAAS,GACTC,QAAS,MA8KX,SAASC,EAAqBC,GAC5B,MAAMC,EAAW,CAAC,EAGZC,EA8FR,SAAqBC,GAGnB,MAAMR,EAAcF,EAAOE,YACrBC,EAAYH,EAAOG,UACnBQ,EAAYC,KAAKC,OAAOH,EAASI,OAASZ,GAAeC,GAAa,EAEtEM,EAAO,GAEb,IAAK,IAAIM,EAAQ,EAAGA,EAAQJ,EAAWI,IAAS,CAC9C,MAAMC,EAAQD,EAAQZ,EAChBc,EAAYP,EAASQ,MAAMF,EAAOA,EAAQd,GAG1CiB,EAAO,IAAIC,aAAalB,EAAc,GACtCmB,EAAO,IAAID,aAAalB,EAAc,GAE5C,IAAK,IAAIoB,EAAI,EAAGA,EAAIpB,EAAc,EAAGoB,IACnC,IAAK,IAAIC,EAAI,EAAGA,EAAIrB,EAAaqB,IAAK,CACpC,MAAMC,GAAS,EAAIZ,KAAKa,GAAKH,EAAIC,EAAIrB,EACrCiB,EAAKG,IAAML,EAAUM,GAAKX,KAAKc,IAAIF,GACnCH,EAAKC,IAAML,EAAUM,GAAKX,KAAKe,IAAIH,EACrC,CAGFf,EAAKmB,KAAK,CAAET,OAAME,QACpB,CAEA,OAAOZ,CACT,CA3HeoB,CAAYtB,EAAUG,UAAYH,GAC/CC,EAASC,KAAOA,EAGhB,MAAMqB,EA4HR,SAA8BrB,GAC5B,OAAOA,EAAKsB,IAAIhB,IACd,MAAMiB,EAAQ,IAAIZ,aAAaL,EAAMI,KAAKL,QAC1C,IAAK,IAAImB,EAAI,EAAGA,EAAIlB,EAAMI,KAAKL,OAAQmB,IACrCD,EAAMC,GAAKlB,EAAMI,KAAKc,GAAKlB,EAAMI,KAAKc,GAAKlB,EAAMM,KAAKY,GAAKlB,EAAMM,KAAKY,GAExE,OAAOD,GAEX,CApIwBE,CAAqBzB,GAC3CD,EAASsB,cAAgBA,EAGzB,MAAMK,EAqIR,SAA+BL,GAE7B,OAAOA,EAAcC,IAAIhB,IACvB,MAAMqB,EAAW,IAAIhB,aAAapB,EAAOI,SACnCiC,EAAUtB,EAAMD,OAASd,EAAOI,QAEtC,IAAK,IAAI6B,EAAI,EAAGA,EAAIjC,EAAOI,QAAS6B,IAAK,CACvC,MAAMjB,EAAQJ,KAAKC,MAAMoB,EAAII,GACvBC,EAAM1B,KAAKC,OAAOoB,EAAI,GAAKI,GACjC,IAAIE,EAAM,EAEV,IAAK,IAAIC,EAAIxB,EAAOwB,EAAIF,GAAOE,EAAIzB,EAAMD,OAAQ0B,IAC/CD,GAAOxB,EAAMyB,GAGfJ,EAASH,GAAKM,GAAOD,EAAMtB,EAC7B,CAEA,OAAOoB,GAEX,CAzJyBK,CAAsBX,GAO7C,OANAtB,EAAS2B,eAAiBA,EAG1B3B,EAASkC,iBA0JX,SAAiCZ,GAC/B,OAAOA,EAAcC,IAAIhB,IACvB,IAAI4B,EAAc,EACdC,EAAa,EAEjB,IAAK,IAAIX,EAAI,EAAGA,EAAIlB,EAAMD,OAAQmB,IAAK,CAErCU,GADaV,EAAIjC,EAAOC,YAAc,EAAIc,EAAMD,QAC1BC,EAAMkB,GAC5BW,GAAc7B,EAAMkB,EACtB,CAEA,OAAOW,EAAa,EAAID,EAAcC,EAAa,GAEvD,CAvK8BC,CAAwBf,GACpDtB,EAASsC,iBA2KX,SAAiCpC,GAC/B,IAAIqC,EAAY,EAChB,IAAK,IAAId,EAAI,EAAGA,EAAIvB,EAASI,OAAQmB,IAC9BvB,EAASuB,IAAM,GAAQvB,EAASuB,EAAI,IAAM,GAC7Cc,IAGJ,OAAOA,GAAarC,EAASI,OAAS,EACxC,CAnL8BkC,CAAwBzC,EAAUG,UAAYH,GAEnEC,CACT,CAsDA,SAASyC,EAAUC,GACjB,IAAIC,EAAM,EACV,IAAK,IAAIlB,EAAI,EAAGA,EAAIiB,EAAKpC,OAAQmB,IAC/BkB,GAAOD,EAAKjB,GAAKiB,EAAKjB,GAIxB,GAFAkB,EAAMvC,KAAKwC,KAAKD,EAAMD,EAAKpC,QAEvBqC,EAAM,KACR,OAAOD,EAGT,MAAMG,EAAa,IAAIjC,aAAa8B,EAAKpC,QACnCwC,EAAQ,IAAOH,EAErB,IAAK,IAAIlB,EAAI,EAAGA,EAAIiB,EAAKpC,OAAQmB,IAC/BoB,EAAWpB,GAAKrB,KAAK2C,KAAK,EAAG3C,KAAK4C,IAAI,EAAGN,EAAKjB,GAAKqB,IAGrD,OAAOD,CACT,CAvQAI,KAAKC,iBAAiB,UAAWC,eAAeC,GAC9C,MAAM,KAAEC,EAAI,KAAEX,EAAI,GAAEY,GAAOF,EAAEV,KAE7B,IACE,IAAIa,EAEJ,OAAQF,GACN,IAAK,aACHE,QA2CRJ,eAA0BK,EAAe,CAAC,GACxC,GAAIjE,EACF,MAAO,CAAEkE,QAAS,uBAKpB,IASE,OAPAjE,EAAS,IAAKA,KAAWgE,SAqB7BL,iBAIA,CAtBUO,GAENnE,GAAgB,EAET,CACLkE,QAAS,wCACTjE,SAGJ,CAAE,MAAOmE,GAEP,MAAMA,CACR,CACF,CApEuBC,CAAWlB,GAC1B,MAEF,IAAK,gBACHa,QA8ERJ,eAA4BpD,GAC1B,IAAKR,EACH,MAAM,IAAIsE,MAAM,gCAGlB,MAAMC,EAAYC,YAAYC,MAE9B,IAEE,MAAMC,EAkDV,SAAyBlE,GAEvB,MAAMG,EA4CR,SAA2BH,GACzB,GAAIA,EAAUmE,WAAY,CAExB,MAAMhE,EAAW,IAAIU,aAAab,EAAUmE,WAAW5D,QACvD,IAAK,IAAImB,EAAI,EAAGA,EAAI1B,EAAUmE,WAAW5D,OAAQmB,IAC/CvB,EAASuB,IAAM1B,EAAUmE,WAAWzC,GAAK,KAAO,IAElD,OAAOvB,CACT,CAEA,GAAIH,EAAUoE,UAAW,CAEvB,MAAMjE,EAAW,IAAIU,aAAab,EAAUoE,UAAU7D,QACtD,IAAK,IAAImB,EAAI,EAAGA,EAAI1B,EAAUoE,UAAU7D,OAAQmB,IAC9CvB,EAASuB,IAAM1B,EAAUoE,UAAU1C,GAAK,KAAO,IAEjD,OAAOvB,CACT,CAEA,MAAM,IAAI2D,MAAM,+BAClB,CAhEmBO,CAAkBrE,GAG7BsE,EAkER,SAAkB3B,EAAM4B,EAAWC,GACjC,GAAID,IAAcC,EAChB,OAAO7B,EAGT,MAAM8B,EAAQF,EAAYC,EACpBE,EAAerE,KAAKC,MAAMqC,EAAKpC,OAASkE,GACxCE,EAAS,IAAI9D,aAAa6D,GAEhC,IAAK,IAAIhD,EAAI,EAAGA,EAAIgD,EAAchD,IAAK,CACrC,MAAMkD,EAAWlD,EAAI+C,EACfI,EAAgBxE,KAAKC,MAAMsE,GAC3BE,EAAezE,KAAK4C,IAAI4B,EAAgB,EAAGlC,EAAKpC,OAAS,GACzDwE,EAAWH,EAAWC,EAE5BF,EAAOjD,GAAKiB,EAAKkC,IAAkB,EAAIE,GAAYpC,EAAKmC,GAAgBC,CAC1E,CAEA,OAAOJ,CACT,CArFoBK,CAAS7E,EAAUH,EAAUN,YAAc,MAAOD,EAAOC,YAK3E,MAAO,CACLS,SAHiBuC,EAAU4B,GAI3B5E,WAAYD,EAAOC,WACnBC,YAAaF,EAAOE,YACpBC,UAAWH,EAAOG,UAEtB,CAlEyBqF,CAAgBjF,GAG/BC,EAAWF,EAAqBmE,GAItC,MAAO,CACLgB,IAAKlF,EACLkE,eACAjE,WACAkF,eANqBnB,YAAYC,MAAQF,EAOzCqB,UAAWC,KAAKpB,MAGpB,CAAE,MAAOL,GAEP,MAAMA,CACR,CACF,CA1GuB0B,CAAa3C,GAC5B,MAEF,IAAK,mBACHa,QA2GRJ,eAA+BpD,GAC7B,IAAKR,EACH,MAAM,IAAIsE,MAAM,gCAGlB,MAAMC,EAAYC,YAAYC,MAE9B,IACE,MAAMhE,EAAWF,EAAqBC,GAGtC,MAAO,CACLC,WACAsF,eAJqBvB,YAAYC,MAAQF,EAKzCqB,UAAWC,KAAKpB,MAGpB,CAAE,MAAOL,GAEP,MAAMA,CACR,CACF,CAhIuB4B,CAAgB7C,GAC/B,MAEF,IAAK,gBAgWW8C,EA/VQ9C,EAgW5BlD,EAAS,IAAKA,KAAWgG,GAhWnBjC,EAiWC,CAAEE,QAAS,iBAAkBjE,UAhW9B,MAEF,IAAK,aACH+D,EAoWC,CACLhE,gBACAC,UArWI,MAEF,QACE,MAAM,IAAIqE,MAAM,yBAAyBR,KAI7CJ,KAAKwC,YAAY,CACfnC,KACAoC,SAAS,EACTnC,UAGJ,CAAE,MAAOI,GAEPV,KAAKwC,YAAY,CACfnC,KACAoC,SAAS,EACT/B,MAAOA,EAAMF,SAEjB,CAsUF,IAAsB+B,CArUtB,E,GC5DIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaE,QAGrB,IAAIC,EAASN,EAAyBE,GAAY,CACjDvC,GAAIuC,EACJK,QAAQ,EACRF,QAAS,CAAC,GAUX,OANAG,EAAoBN,GAAUO,KAAKH,EAAOD,QAASC,EAAQA,EAAOD,QAASJ,GAG3EK,EAAOC,QAAS,EAGTD,EAAOD,OACf,CAGAJ,EAAoBS,EAAIF,EC5BxBP,EAAoBU,KAAO,WAC1B,MAAM,IAAIzC,MAAM,iCACjB,ECFA+B,EAAoBW,KAAO,CAAC,EJAxBjH,EAAW,GACfsG,EAAoBY,EAAI,CAACjD,EAAQkD,EAAUC,EAAIC,KAC9C,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAASpF,EAAI,EAAGA,EAAInC,EAASgB,OAAQmB,IAAK,CAGzC,IAFA,IAAKgF,EAAUC,EAAIC,GAAYrH,EAASmC,GACpCqF,GAAY,EACP9E,EAAI,EAAGA,EAAIyE,EAASnG,OAAQ0B,MACpB,EAAX2E,GAAsBC,GAAgBD,IAAaI,OAAOC,KAAKpB,EAAoBY,GAAGS,MAAOC,GAAStB,EAAoBY,EAAEU,GAAKT,EAASzE,KAC9IyE,EAASU,OAAOnF,IAAK,IAErB8E,GAAY,EACTH,EAAWC,IAAcA,EAAeD,IAG7C,GAAGG,EAAW,CACbxH,EAAS6H,OAAO1F,IAAK,GACrB,IAAI2F,EAAIV,SACEX,IAANqB,IAAiB7D,EAAS6D,EAC/B,CACD,CACA,OAAO7D,CAnBP,CAJCoD,EAAWA,GAAY,EACvB,IAAI,IAAIlF,EAAInC,EAASgB,OAAQmB,EAAI,GAAKnC,EAASmC,EAAI,GAAG,GAAKkF,EAAUlF,IAAKnC,EAASmC,GAAKnC,EAASmC,EAAI,GACrGnC,EAASmC,GAAK,CAACgF,EAAUC,EAAIC,IKJ/Bf,EAAoB7E,EAAKkF,IACxB,IAAIoB,EAASpB,GAAUA,EAAOqB,WAC7B,IAAOrB,EAAiB,QACxB,IAAM,EAEP,OADAL,EAAoB2B,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRzB,EAAoB2B,EAAI,CAACvB,EAASyB,KACjC,IAAI,IAAIP,KAAOO,EACX7B,EAAoB8B,EAAED,EAAYP,KAAStB,EAAoB8B,EAAE1B,EAASkB,IAC5EH,OAAOY,eAAe3B,EAASkB,EAAK,CAAEU,YAAY,EAAMC,IAAKJ,EAAWP,MCJ3EtB,EAAoBkC,EAAI,WACvB,GAA0B,iBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOC,MAAQ,IAAIC,SAAS,cAAb,EAChB,CAAE,MAAO7E,GACR,GAAsB,iBAAX8E,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxBtC,EAAoB8B,EAAI,CAACS,EAAKC,IAAUrB,OAAOsB,UAAUC,eAAelC,KAAK+B,EAAKC,GCAlFxC,EAAoB2C,IAAOtC,IAC1BA,EAAOuC,MAAQ,GACVvC,EAAOwC,WAAUxC,EAAOwC,SAAW,IACjCxC,G,MCER,IAAIyC,EAAkB,CACrB,IAAK,GAaN9C,EAAoBY,EAAExE,EAAK2G,GAA0C,IAA7BD,EAAgBC,GAGxD,IAAIC,EAAuB,CAACC,EAA4BnG,KACvD,IAGImD,EAAU8C,GAHTlC,EAAUqC,EAAaC,GAAWrG,EAGhBjB,EAAI,EAC3B,GAAGgF,EAASuC,KAAM1F,GAAgC,IAAxBoF,EAAgBpF,IAAa,CACtD,IAAIuC,KAAYiD,EACZlD,EAAoB8B,EAAEoB,EAAajD,KACrCD,EAAoBS,EAAER,GAAYiD,EAAYjD,IAGhD,GAAGkD,EAAS,IAAIxF,EAASwF,EAAQnD,EAClC,CAEA,IADGiD,GAA4BA,EAA2BnG,GACrDjB,EAAIgF,EAASnG,OAAQmB,IACzBkH,EAAUlC,EAAShF,GAChBmE,EAAoB8B,EAAEgB,EAAiBC,IAAYD,EAAgBC,IACrED,EAAgBC,GAAS,KAE1BD,EAAgBC,GAAW,EAE5B,OAAO/C,EAAoBY,EAAEjD,IAG1B0F,EAAqBhG,KAA8C,wCAAIA,KAA8C,yCAAK,GAC9HgG,EAAmBC,QAAQN,EAAqBO,KAAK,KAAM,IAC3DF,EAAmB7H,KAAOwH,EAAqBO,KAAK,KAAMF,EAAmB7H,KAAK+H,KAAKF,G,KC7CvF,IAAIG,EAAsBxD,EAAoBY,OAAET,EAAW,CAAC,KAAM,IAAOH,EAAoB,OAC7FwD,EAAsBxD,EAAoBY,EAAE4C,E", "sources": ["webpack://youtube-aural-visual-bridge/webpack/runtime/chunk loaded", "webpack://youtube-aural-visual-bridge/./src/audio/workers/audio-worker.js", "webpack://youtube-aural-visual-bridge/webpack/bootstrap", "webpack://youtube-aural-visual-bridge/webpack/runtime/amd define", "webpack://youtube-aural-visual-bridge/webpack/runtime/amd options", "webpack://youtube-aural-visual-bridge/webpack/runtime/compat get default export", "webpack://youtube-aural-visual-bridge/webpack/runtime/define property getters", "webpack://youtube-aural-visual-bridge/webpack/runtime/global", "webpack://youtube-aural-visual-bridge/webpack/runtime/hasOwnProperty shorthand", "webpack://youtube-aural-visual-bridge/webpack/runtime/node module decorator", "webpack://youtube-aural-visual-bridge/webpack/runtime/jsonp chunk loading", "webpack://youtube-aural-visual-bridge/webpack/startup"], "sourcesContent": ["var deferred = [];\n__webpack_require__.O = (result, chunkIds, fn, priority) => {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar [chunkIds, fn, priority] = deferred[i];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "/**\n * 音频处理Web Worker\n * 在后台线程中执行音频特征提取和预处理\n */\n\nlet isInitialized = false;\nlet config = {\n  sampleRate: 16000,\n  frameLength: 1024,\n  hopLength: 512,\n  melBins: 64,\n  fftSize: 2048\n};\n\n// 监听主线程消息\nself.addEventListener('message', async function(e) {\n  const { type, data, id } = e.data;\n  \n  try {\n    let result;\n    \n    switch (type) {\n      case 'INITIALIZE':\n        result = await initialize(data);\n        break;\n        \n      case 'PROCESS_AUDIO':\n        result = await processAudio(data);\n        break;\n        \n      case 'EXTRACT_FEATURES':\n        result = await extractFeatures(data);\n        break;\n        \n      case 'UPDATE_CONFIG':\n        result = updateConfig(data);\n        break;\n        \n      case 'GET_STATUS':\n        result = getStatus();\n        break;\n        \n      default:\n        throw new Error(`Unknown message type: ${type}`);\n    }\n    \n    // 发送成功响应\n    self.postMessage({\n      id,\n      success: true,\n      result\n    });\n    \n  } catch (error) {\n    // 发送错误响应\n    self.postMessage({\n      id,\n      success: false,\n      error: error.message\n    });\n  }\n});\n\n/**\n * 初始化音频Worker\n */\nasync function initialize(workerConfig = {}) {\n  if (isInitialized) {\n    return { message: 'Already initialized' };\n  }\n  \n  console.log('Audio Worker: Initializing...');\n  \n  try {\n    // 更新配置\n    config = { ...config, ...workerConfig };\n    \n    // 初始化音频处理组件\n    await initializeAudioProcessing();\n    \n    isInitialized = true;\n    \n    return {\n      message: 'Audio Worker initialized successfully',\n      config\n    };\n    \n  } catch (error) {\n    console.error('Audio Worker initialization failed:', error);\n    throw error;\n  }\n}\n\n/**\n * 初始化音频处理\n */\nasync function initializeAudioProcessing() {\n  // 这里可以初始化音频处理所需的资源\n  // 例如预计算的滤波器组、窗函数等\n  console.log('Audio processing components initialized');\n}\n\n/**\n * 处理音频数据\n */\nasync function processAudio(audioData) {\n  if (!isInitialized) {\n    throw new Error('Audio Worker not initialized');\n  }\n  \n  const startTime = performance.now();\n  \n  try {\n    // 1. 预处理音频数据\n    const preprocessed = preprocessAudio(audioData);\n    \n    // 2. 提取特征\n    const features = extractAudioFeatures(preprocessed);\n    \n    const processingTime = performance.now() - startTime;\n    \n    return {\n      raw: audioData,\n      preprocessed,\n      features,\n      processingTime,\n      timestamp: Date.now()\n    };\n    \n  } catch (error) {\n    console.error('Audio processing error:', error);\n    throw error;\n  }\n}\n\n/**\n * 提取音频特征\n */\nasync function extractFeatures(audioData) {\n  if (!isInitialized) {\n    throw new Error('Audio Worker not initialized');\n  }\n  \n  const startTime = performance.now();\n  \n  try {\n    const features = extractAudioFeatures(audioData);\n    const extractionTime = performance.now() - startTime;\n    \n    return {\n      features,\n      extractionTime,\n      timestamp: Date.now()\n    };\n    \n  } catch (error) {\n    console.error('Feature extraction error:', error);\n    throw error;\n  }\n}\n\n/**\n * 预处理音频数据\n */\nfunction preprocessAudio(audioData) {\n  // 转换频域数据为时域数据\n  const timeData = convertToTimeData(audioData);\n  \n  // 重采样到目标采样率\n  const resampled = resample(timeData, audioData.sampleRate || 44100, config.sampleRate);\n  \n  // 归一化\n  const normalized = normalize(resampled);\n  \n  return {\n    timeData: normalized,\n    sampleRate: config.sampleRate,\n    frameLength: config.frameLength,\n    hopLength: config.hopLength\n  };\n}\n\n/**\n * 提取音频特征\n */\nfunction extractAudioFeatures(audioData) {\n  const features = {};\n  \n  // 计算STFT\n  const stft = computeSTFT(audioData.timeData || audioData);\n  features.stft = stft;\n  \n  // 计算功率谱\n  const powerSpectrum = computePowerSpectrum(stft);\n  features.powerSpectrum = powerSpectrum;\n  \n  // 计算梅尔频谱图\n  const melSpectrogram = computeMelSpectrogram(powerSpectrum);\n  features.melSpectrogram = melSpectrogram;\n  \n  // 计算其他特征\n  features.spectralCentroid = computeSpectralCentroid(powerSpectrum);\n  features.zeroCrossingRate = computeZeroCrossingRate(audioData.timeData || audioData);\n  \n  return features;\n}\n\n/**\n * 转换频域数据为时域数据\n */\nfunction convertToTimeData(audioData) {\n  if (audioData.timeDomain) {\n    // 转换Uint8Array到Float32Array\n    const timeData = new Float32Array(audioData.timeDomain.length);\n    for (let i = 0; i < audioData.timeDomain.length; i++) {\n      timeData[i] = (audioData.timeDomain[i] - 128) / 128.0;\n    }\n    return timeData;\n  }\n  \n  if (audioData.frequency) {\n    // 简单的频域到时域转换\n    const timeData = new Float32Array(audioData.frequency.length);\n    for (let i = 0; i < audioData.frequency.length; i++) {\n      timeData[i] = (audioData.frequency[i] - 128) / 128.0;\n    }\n    return timeData;\n  }\n  \n  throw new Error('No suitable audio data found');\n}\n\n/**\n * 重采样音频数据\n */\nfunction resample(data, inputRate, outputRate) {\n  if (inputRate === outputRate) {\n    return data;\n  }\n  \n  const ratio = inputRate / outputRate;\n  const outputLength = Math.floor(data.length / ratio);\n  const output = new Float32Array(outputLength);\n  \n  for (let i = 0; i < outputLength; i++) {\n    const srcIndex = i * ratio;\n    const srcIndexFloor = Math.floor(srcIndex);\n    const srcIndexCeil = Math.min(srcIndexFloor + 1, data.length - 1);\n    const fraction = srcIndex - srcIndexFloor;\n    \n    output[i] = data[srcIndexFloor] * (1 - fraction) + data[srcIndexCeil] * fraction;\n  }\n  \n  return output;\n}\n\n/**\n * 归一化音频数据\n */\nfunction normalize(data) {\n  let rms = 0;\n  for (let i = 0; i < data.length; i++) {\n    rms += data[i] * data[i];\n  }\n  rms = Math.sqrt(rms / data.length);\n  \n  if (rms < 1e-8) {\n    return data;\n  }\n  \n  const normalized = new Float32Array(data.length);\n  const scale = 0.95 / rms;\n  \n  for (let i = 0; i < data.length; i++) {\n    normalized[i] = Math.max(-1, Math.min(1, data[i] * scale));\n  }\n  \n  return normalized;\n}\n\n/**\n * 计算STFT（简化版本）\n */\nfunction computeSTFT(timeData) {\n  // 这是一个简化的STFT实现\n  // 实际项目中应该使用更高效的FFT算法\n  const frameLength = config.frameLength;\n  const hopLength = config.hopLength;\n  const numFrames = Math.floor((timeData.length - frameLength) / hopLength) + 1;\n  \n  const stft = [];\n  \n  for (let frame = 0; frame < numFrames; frame++) {\n    const start = frame * hopLength;\n    const frameData = timeData.slice(start, start + frameLength);\n    \n    // 简化的DFT\n    const real = new Float32Array(frameLength / 2);\n    const imag = new Float32Array(frameLength / 2);\n    \n    for (let k = 0; k < frameLength / 2; k++) {\n      for (let n = 0; n < frameLength; n++) {\n        const angle = -2 * Math.PI * k * n / frameLength;\n        real[k] += frameData[n] * Math.cos(angle);\n        imag[k] += frameData[n] * Math.sin(angle);\n      }\n    }\n    \n    stft.push({ real, imag });\n  }\n  \n  return stft;\n}\n\n/**\n * 计算功率谱\n */\nfunction computePowerSpectrum(stft) {\n  return stft.map(frame => {\n    const power = new Float32Array(frame.real.length);\n    for (let i = 0; i < frame.real.length; i++) {\n      power[i] = frame.real[i] * frame.real[i] + frame.imag[i] * frame.imag[i];\n    }\n    return power;\n  });\n}\n\n/**\n * 计算梅尔频谱图（简化版本）\n */\nfunction computeMelSpectrogram(powerSpectrum) {\n  // 简化的梅尔频谱图计算\n  return powerSpectrum.map(frame => {\n    const melFrame = new Float32Array(config.melBins);\n    const binSize = frame.length / config.melBins;\n    \n    for (let i = 0; i < config.melBins; i++) {\n      const start = Math.floor(i * binSize);\n      const end = Math.floor((i + 1) * binSize);\n      let sum = 0;\n      \n      for (let j = start; j < end && j < frame.length; j++) {\n        sum += frame[j];\n      }\n      \n      melFrame[i] = sum / (end - start);\n    }\n    \n    return melFrame;\n  });\n}\n\n/**\n * 计算频谱质心\n */\nfunction computeSpectralCentroid(powerSpectrum) {\n  return powerSpectrum.map(frame => {\n    let weightedSum = 0;\n    let totalPower = 0;\n    \n    for (let i = 0; i < frame.length; i++) {\n      const freq = i * config.sampleRate / (2 * frame.length);\n      weightedSum += freq * frame[i];\n      totalPower += frame[i];\n    }\n    \n    return totalPower > 0 ? weightedSum / totalPower : 0;\n  });\n}\n\n/**\n * 计算过零率\n */\nfunction computeZeroCrossingRate(timeData) {\n  let crossings = 0;\n  for (let i = 1; i < timeData.length; i++) {\n    if ((timeData[i] >= 0) !== (timeData[i - 1] >= 0)) {\n      crossings++;\n    }\n  }\n  return crossings / (timeData.length - 1);\n}\n\n/**\n * 更新配置\n */\nfunction updateConfig(newConfig) {\n  config = { ...config, ...newConfig };\n  return { message: 'Config updated', config };\n}\n\n/**\n * 获取Worker状态\n */\nfunction getStatus() {\n  return {\n    isInitialized,\n    config\n  };\n}\n\nconsole.log('Audio Worker loaded and ready');\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "__webpack_require__.amdD = function () {\n\tthrow new Error('define cannot be used indirect');\n};", "__webpack_require__.amdO = {};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "__webpack_require__.nmd = (module) => {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t614: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkyoutube_aural_visual_bridge\"] = self[\"webpackChunkyoutube_aural_visual_bridge\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [121], () => (__webpack_require__(3996)))\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["deferred", "isInitialized", "config", "sampleRate", "frameLength", "<PERSON><PERSON><PERSON><PERSON>", "mel<PERSON><PERSON>", "fftSize", "extractAudioFeatures", "audioData", "features", "stft", "timeData", "numFrames", "Math", "floor", "length", "frame", "start", "frameData", "slice", "real", "Float32Array", "imag", "k", "n", "angle", "PI", "cos", "sin", "push", "computeSTFT", "powerSpectrum", "map", "power", "i", "computePowerSpectrum", "melSpectrogram", "melFrame", "binSize", "end", "sum", "j", "computeMelSpectrogram", "spectralCentroid", "weightedSum", "totalPower", "computeSpectralCentroid", "zeroCrossingRate", "crossings", "computeZeroCrossingRate", "normalize", "data", "rms", "sqrt", "normalized", "scale", "max", "min", "self", "addEventListener", "async", "e", "type", "id", "result", "workerConfig", "message", "initializeAudioProcessing", "error", "initialize", "Error", "startTime", "performance", "now", "preprocessed", "timeDomain", "frequency", "convertToTimeData", "resampled", "inputRate", "outputRate", "ratio", "outputLength", "output", "srcIndex", "srcIndexFloor", "srcIndexCeil", "fraction", "resample", "preprocessAudio", "raw", "processingTime", "timestamp", "Date", "processAudio", "extractionTime", "extractFeatures", "newConfig", "postMessage", "success", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "exports", "module", "loaded", "__webpack_modules__", "call", "m", "amdD", "amdO", "O", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "fulfilled", "Object", "keys", "every", "key", "splice", "r", "getter", "__esModule", "d", "a", "definition", "o", "defineProperty", "enumerable", "get", "g", "globalThis", "this", "Function", "window", "obj", "prop", "prototype", "hasOwnProperty", "nmd", "paths", "children", "installedChunks", "chunkId", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "some", "chunkLoadingGlobal", "for<PERSON>ach", "bind", "__webpack_exports__"], "sourceRoot": ""}