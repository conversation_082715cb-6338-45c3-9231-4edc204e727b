{"version": 3, "file": "background.js", "mappings": "AAKAA,OAAOC,QAAQC,YAAYC,YAAaC,IAGf,YAAnBA,EAAQC,QAGDD,EAAQC,SAOrBL,OAAOC,QAAQK,UAAUH,YAAY,CAACI,EAASC,EAAQC,KAGrD,OAAQF,EAAQG,MACd,IAAK,aAKH,OAHAV,OAAOW,QAAQC,KAAKC,IAAI,KAAOC,IAC7BL,EAAa,CAAEM,SAAS,EAAMD,cAEzB,EAET,IAAK,aAKH,OAHAd,OAAOW,QAAQC,KAAKI,IAAIT,EAAQO,OAAQ,KACtCL,EAAa,CAAEM,SAAS,OAEnB,EAET,IAAK,YAGHN,EAAa,CAAEM,SAAS,IACxB,MAEF,QACEN,EAAa,CAAEM,SAAS,EAAOE,MAAO,4BAK5CjB,OAAOkB,KAAKC,UAAUhB,YAAY,CAACiB,EAAOC,EAAYC,KAE1B,aAAtBD,EAAWE,QAAyBD,EAAIE,KAAOF,EAAIE,IAAIC,SAAS", "sources": ["webpack://youtube-aural-visual-bridge/./src/background.js"], "sourcesContent": ["/**\n * Chrome扩展后台脚本\n */\n\n// 扩展安装时的初始化\nchrome.runtime.onInstalled.addListener((details) => {\n  console.log('YouTube Aural-Visual Bridge v2.0 installed');\n  \n  if (details.reason === 'install') {\n    // 首次安装\n    console.log('First time installation');\n  } else if (details.reason === 'update') {\n    // 更新\n    console.log('Extension updated');\n  }\n});\n\n// 处理来自content script的消息\nchrome.runtime.onMessage.addListener((request, sender, sendResponse) => {\n  console.log('Background received message:', request);\n  \n  switch (request.type) {\n    case 'GET_CONFIG':\n      // 获取配置\n      chrome.storage.sync.get(null, (config) => {\n        sendResponse({ success: true, config });\n      });\n      return true; // 异步响应\n      \n    case 'SET_CONFIG':\n      // 保存配置\n      chrome.storage.sync.set(request.config, () => {\n        sendResponse({ success: true });\n      });\n      return true; // 异步响应\n      \n    case 'LOG_ERROR':\n      // 记录错误\n      console.error('Content script error:', request.error);\n      sendResponse({ success: true });\n      break;\n      \n    default:\n      sendResponse({ success: false, error: 'Unknown message type' });\n  }\n});\n\n// 监听标签页更新\nchrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {\n  // 检查是否是YouTube页面\n  if (changeInfo.status === 'complete' && tab.url && tab.url.includes('youtube.com/watch')) {\n    console.log('YouTube video page loaded:', tab.url);\n  }\n});\n\nconsole.log('YouTube Aural-Visual Bridge v2.0 background script loaded');\n"], "names": ["chrome", "runtime", "onInstalled", "addListener", "details", "reason", "onMessage", "request", "sender", "sendResponse", "type", "storage", "sync", "get", "config", "success", "set", "error", "tabs", "onUpdated", "tabId", "changeInfo", "tab", "status", "url", "includes"], "sourceRoot": ""}