{"version": 3, "file": "ai-worker.js", "mappings": "UAAIA,E,uDCMJC,cAAc,uEAEd,IAAIC,GAAgB,EAKpBC,KAAKC,iBAAiB,UAAWC,eAAeC,GAC9C,MAAM,KAAEC,EAAI,KAAEC,EAAI,GAAEC,GAAOH,EAAEE,KAE7B,IACE,IAAIE,EAEJ,OAAQH,GACN,IAAK,aACHG,QAuCRL,eAA0BM,EAAS,CAAC,GAClC,GAAIT,EACF,MAAO,CAAEU,QAAS,uBAKpB,IAmBE,aAjBMC,EAAGC,QAGLH,EAAOI,aAMPJ,EAAOK,qBAMXd,GAAgB,EAET,CACLU,QAAS,qCACTK,QAASJ,EAAGK,aACZC,OAAQN,EAAGM,SAGf,CAAE,MAAOC,GAEP,MAAMA,CACR,CACF,CA3EuBC,CAAWb,GAC1B,MAEF,IAAK,iBACHE,QA4ERL,iBACE,IAAKH,EACH,MAAM,IAAIoB,MAAM,6BAGlB,MAAMC,EAAYC,YAAYC,MAE9B,IAEE,MAAMC,EA8DV,WACE,MAAMC,EAAc,CAClB,SAAU,QAAS,WAAY,WAAY,SAAU,YACrD,OAAQ,QAAS,QAAS,UAAW,QAAS,MAAO,MAAO,MAC5D,OAAQ,QAAS,OAAQ,OAAQ,UAAW,QAGxCC,EAAc,GACdC,EAAiBC,KAAKC,MAAsB,EAAhBD,KAAKE,UAAgB,EAEvD,IAAK,IAAIC,EAAI,EAAGA,EAAIJ,EAAgBI,IAAK,CACvC,MAAMC,EAAaJ,KAAKC,MAAMD,KAAKE,SAAWL,EAAYQ,QACpDC,EAA6B,GAAhBN,KAAKE,SAAiB,GAEzCJ,EAAYS,KAAK,CACfC,UAAWX,EAAYO,GACvBE,aACAG,WAAYL,GAEhB,CAEA,OAAON,EAAYY,KAAK,CAACC,EAAGC,IAAMA,EAAEN,WAAaK,EAAEL,WACrD,CApF4BO,GAIxB,MAAO,CACLf,YAAaF,EACbkB,cAJoBpB,YAAYC,MAAQF,EAKxCsB,UAAWC,KAAKrB,MAChBsB,UAAW,SAGf,CAAE,MAAO3B,GAEP,MAAMA,CACR,CACF,CApGuB4B,GACf,MAEF,IAAK,gBACHtC,QAqGRL,iBACE,IAAKH,EACH,MAAM,IAAIoB,MAAM,6BAGlB,MAAMC,EAAYC,YAAYC,MAE9B,IAEE,MAAMwB,EA4DV,WACE,MAAMC,EAAW,CAAC,MAAO,KAAM,KAAM,OAAQ,OAAQ,QAAS,KAAM,QAGpE,GAAIpB,KAAKE,SAAW,GAAK,CACvB,MAAMmB,EAAerB,KAAKC,MAAMD,KAAKE,SAAWkB,EAASf,QACnDC,EAA6B,GAAhBN,KAAKE,SAAiB,GAEzC,MAAO,CAAC,CACNoB,QAASF,EAASC,GAClBf,aACAe,eACAb,UAAWY,EAASC,GACpBZ,WAAYY,GAEhB,CAEA,MAAO,EACT,CA9EyBE,GAIrB,MAAO,CACLH,SAAUD,EACVL,cAJoBpB,YAAYC,MAAQF,EAKxCsB,UAAWC,KAAKrB,MAChBsB,UAAW,kBAGf,CAAE,MAAO3B,GAEP,MAAMA,CACR,CACF,CA7HuBkC,GACf,MAEF,IAAK,aACH5C,EA+HC,CACLR,gBACAqD,WAAWC,EACXC,mBAAmBC,EACnBzC,QAASf,EAAgBW,EAAGK,aAAe,KAC3CC,OAAQjB,EAAgBW,EAAGM,SAAW,MAnIlC,MAEF,QACE,MAAM,IAAIG,MAAM,yBAAyBf,KAI7CJ,KAAKwD,YAAY,CACflD,KACAmD,SAAS,EACTlD,UAGJ,CAAE,MAAOU,GAEPjB,KAAKwD,YAAY,CACflD,KACAmD,SAAS,EACTxC,MAAOA,EAAMR,SAEjB,CACF,E,mDCtDIiD,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaE,QAGrB,IAAIC,EAASN,EAAyBE,GAAY,CACjDtD,GAAIsD,EACJK,QAAQ,EACRF,QAAS,CAAC,GAUX,OANAG,EAAoBN,GAAUO,KAAKH,EAAOD,QAASC,EAAQA,EAAOD,QAASJ,GAG3EK,EAAOC,QAAS,EAGTD,EAAOD,OACf,CAGAJ,EAAoBS,EAAIF,EC5BxBP,EAAoBU,KAAO,WAC1B,MAAM,IAAIlD,MAAM,iCACjB,ECFAwC,EAAoBW,KAAO,CAAC,EJAxBzE,EAAW,GACf8D,EAAoBY,EAAI,CAAChE,EAAQiE,EAAUC,EAAIC,KAC9C,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAAS9C,EAAI,EAAGA,EAAIjC,EAASmC,OAAQF,IAAK,CAGzC,IAFA,IAAK0C,EAAUC,EAAIC,GAAY7E,EAASiC,GACpC+C,GAAY,EACPC,EAAI,EAAGA,EAAIN,EAASxC,OAAQ8C,MACpB,EAAXJ,GAAsBC,GAAgBD,IAAaK,OAAOC,KAAKrB,EAAoBY,GAAGU,MAAOC,GAASvB,EAAoBY,EAAEW,GAAKV,EAASM,KAC9IN,EAASW,OAAOL,IAAK,IAErBD,GAAY,EACTH,EAAWC,IAAcA,EAAeD,IAG7C,GAAGG,EAAW,CACbhF,EAASsF,OAAOrD,IAAK,GACrB,IAAIsD,EAAIX,SACEX,IAANsB,IAAiB7E,EAAS6E,EAC/B,CACD,CACA,OAAO7E,CAnBP,CAJCmE,EAAWA,GAAY,EACvB,IAAI,IAAI5C,EAAIjC,EAASmC,OAAQF,EAAI,GAAKjC,EAASiC,EAAI,GAAG,GAAK4C,EAAU5C,IAAKjC,EAASiC,GAAKjC,EAASiC,EAAI,GACrGjC,EAASiC,GAAK,CAAC0C,EAAUC,EAAIC,IKJ/Bf,EAAoB0B,EAAKrB,IACxB,IAAIsB,EAAStB,GAAUA,EAAOuB,WAC7B,IAAOvB,EAAiB,QACxB,IAAM,EAEP,OADAL,EAAoB6B,EAAEF,EAAQ,CAAEhD,EAAGgD,IAC5BA,GCLR3B,EAAoB6B,EAAI,CAACzB,EAAS0B,KACjC,IAAI,IAAIP,KAAOO,EACX9B,EAAoB+B,EAAED,EAAYP,KAASvB,EAAoB+B,EAAE3B,EAASmB,IAC5EH,OAAOY,eAAe5B,EAASmB,EAAK,CAAEU,YAAY,EAAMC,IAAKJ,EAAWP,MCJ3EvB,EAAoBmC,EAAI,WACvB,GAA0B,iBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOC,MAAQ,IAAIC,SAAS,cAAb,EAChB,CAAE,MAAO9F,GACR,GAAsB,iBAAX+F,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxBvC,EAAoB+B,EAAI,CAACS,EAAKC,IAAUrB,OAAOsB,UAAUC,eAAenC,KAAKgC,EAAKC,GCClFzC,EAAoByB,EAAKrB,IACH,oBAAXwC,QAA0BA,OAAOC,aAC1CzB,OAAOY,eAAe5B,EAASwC,OAAOC,YAAa,CAAEC,MAAO,WAE7D1B,OAAOY,eAAe5B,EAAS,aAAc,CAAE0C,OAAO,KCLvD9C,EAAoB+C,IAAO1C,IAC1BA,EAAO2C,MAAQ,GACV3C,EAAO4C,WAAU5C,EAAO4C,SAAW,IACjC5C,GCHRL,EAAoBmB,EAAI,I,MCKxB,IAAI+B,EAAkB,CACrB,IAAK,GAaNlD,EAAoBY,EAAEO,EAAKgC,GAA0C,IAA7BD,EAAgBC,GAGxD,IAAIC,EAAuB,CAACC,EAA4B3G,KACvD,IAGIuD,EAAUkD,GAHTtC,EAAUyC,EAAaC,GAAW7G,EAGhByB,EAAI,EAC3B,GAAG0C,EAAS2C,KAAM7G,GAAgC,IAAxBuG,EAAgBvG,IAAa,CACtD,IAAIsD,KAAYqD,EACZtD,EAAoB+B,EAAEuB,EAAarD,KACrCD,EAAoBS,EAAER,GAAYqD,EAAYrD,IAGhD,GAAGsD,EAAS,IAAI3G,EAAS2G,EAAQvD,EAClC,CAEA,IADGqD,GAA4BA,EAA2B3G,GACrDyB,EAAI0C,EAASxC,OAAQF,IACzBgF,EAAUtC,EAAS1C,GAChB6B,EAAoB+B,EAAEmB,EAAiBC,IAAYD,EAAgBC,IACrED,EAAgBC,GAAS,KAE1BD,EAAgBC,GAAW,EAE5B,OAAOnD,EAAoBY,EAAEhE,IAG1B6G,EAAqBpH,KAA8C,wCAAIA,KAA8C,yCAAK,GAC9HoH,EAAmBC,QAAQN,EAAqBO,KAAK,KAAM,IAC3DF,EAAmBlF,KAAO6E,EAAqBO,KAAK,KAAMF,EAAmBlF,KAAKoF,KAAKF,G,KC7CvF,IAAIG,EAAsB5D,EAAoBY,OAAET,EAAW,CAAC,IAAI,KAAM,IAAOH,EAAoB,OACjG4D,EAAsB5D,EAAoBY,EAAEgD,E", "sources": ["webpack://youtube-aural-visual-bridge/webpack/runtime/chunk loaded", "webpack://youtube-aural-visual-bridge/./src/ai/workers/ai-worker.js", "webpack://youtube-aural-visual-bridge/webpack/bootstrap", "webpack://youtube-aural-visual-bridge/webpack/runtime/amd define", "webpack://youtube-aural-visual-bridge/webpack/runtime/amd options", "webpack://youtube-aural-visual-bridge/webpack/runtime/compat get default export", "webpack://youtube-aural-visual-bridge/webpack/runtime/define property getters", "webpack://youtube-aural-visual-bridge/webpack/runtime/global", "webpack://youtube-aural-visual-bridge/webpack/runtime/hasOwnProperty shorthand", "webpack://youtube-aural-visual-bridge/webpack/runtime/make namespace object", "webpack://youtube-aural-visual-bridge/webpack/runtime/node module decorator", "webpack://youtube-aural-visual-bridge/webpack/runtime/runtimeId", "webpack://youtube-aural-visual-bridge/webpack/runtime/jsonp chunk loading", "webpack://youtube-aural-visual-bridge/webpack/startup"], "sourcesContent": ["var deferred = [];\n__webpack_require__.O = (result, chunkIds, fn, priority) => {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar [chunkIds, fn, priority] = deferred[i];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "/**\n * AI推理Web Worker\n * 在后台线程中执行AI模型推理，避免阻塞主线程\n */\n\n// 导入TensorFlow.js（在Worker中）\nimportScripts('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.15.0/dist/tf.min.js');\n\nlet isInitialized = false;\nlet yamnetModel = null;\nlet speechCommandsModel = null;\n\n// 监听主线程消息\nself.addEventListener('message', async function(e) {\n  const { type, data, id } = e.data;\n  \n  try {\n    let result;\n    \n    switch (type) {\n      case 'INITIALIZE':\n        result = await initialize(data);\n        break;\n        \n      case 'CLASSIFY_AUDIO':\n        result = await classifyAudio(data);\n        break;\n        \n      case 'DETECT_SPEECH':\n        result = await detectSpeech(data);\n        break;\n        \n      case 'GET_STATUS':\n        result = getStatus();\n        break;\n        \n      default:\n        throw new Error(`Unknown message type: ${type}`);\n    }\n    \n    // 发送成功响应\n    self.postMessage({\n      id,\n      success: true,\n      result\n    });\n    \n  } catch (error) {\n    // 发送错误响应\n    self.postMessage({\n      id,\n      success: false,\n      error: error.message\n    });\n  }\n});\n\n/**\n * 初始化AI Worker\n */\nasync function initialize(config = {}) {\n  if (isInitialized) {\n    return { message: 'Already initialized' };\n  }\n  \n  console.log('AI Worker: Initializing...');\n  \n  try {\n    // 设置TensorFlow.js后端\n    await tf.ready();\n    \n    // 加载模型（简化版本，实际项目中需要完整实现）\n    if (config.enableYAMNet) {\n      // 这里应该加载真实的YAMNet模型\n      // yamnetModel = await tf.loadGraphModel('path/to/yamnet/model');\n      console.log('YAMNet model loaded (mock)');\n    }\n    \n    if (config.enableSpeechCommands) {\n      // 这里应该加载真实的语音指令模型\n      // speechCommandsModel = await tf.loadLayersModel('path/to/speech/model');\n      console.log('Speech Commands model loaded (mock)');\n    }\n    \n    isInitialized = true;\n    \n    return {\n      message: 'AI Worker initialized successfully',\n      backend: tf.getBackend(),\n      memory: tf.memory()\n    };\n    \n  } catch (error) {\n    console.error('AI Worker initialization failed:', error);\n    throw error;\n  }\n}\n\n/**\n * 音频分类\n */\nasync function classifyAudio(audioFeatures) {\n  if (!isInitialized) {\n    throw new Error('AI Worker not initialized');\n  }\n  \n  const startTime = performance.now();\n  \n  try {\n    // 模拟YAMNet推理\n    const mockPredictions = generateMockPredictions();\n    \n    const inferenceTime = performance.now() - startTime;\n    \n    return {\n      predictions: mockPredictions,\n      inferenceTime,\n      timestamp: Date.now(),\n      modelType: 'yamnet'\n    };\n    \n  } catch (error) {\n    console.error('Audio classification error:', error);\n    throw error;\n  }\n}\n\n/**\n * 语音指令检测\n */\nasync function detectSpeech(audioFeatures) {\n  if (!isInitialized) {\n    throw new Error('AI Worker not initialized');\n  }\n  \n  const startTime = performance.now();\n  \n  try {\n    // 模拟语音指令检测\n    const mockCommands = generateMockCommands();\n    \n    const inferenceTime = performance.now() - startTime;\n    \n    return {\n      commands: mockCommands,\n      inferenceTime,\n      timestamp: Date.now(),\n      modelType: 'speech_commands'\n    };\n    \n  } catch (error) {\n    console.error('Speech detection error:', error);\n    throw error;\n  }\n}\n\n/**\n * 获取Worker状态\n */\nfunction getStatus() {\n  return {\n    isInitialized,\n    hasYAMNet: yamnetModel !== null,\n    hasSpeechCommands: speechCommandsModel !== null,\n    backend: isInitialized ? tf.getBackend() : null,\n    memory: isInitialized ? tf.memory() : null\n  };\n}\n\n/**\n * 生成模拟的YAMNet预测结果\n */\nfunction generateMockPredictions() {\n  const audioEvents = [\n    'Speech', 'Music', 'Laughter', 'Applause', 'Crying', 'Footsteps',\n    'Door', 'Knock', 'Glass', 'Gunshot', 'Siren', 'Car', 'Dog', 'Cat',\n    'Bird', 'Water', 'Wind', 'Rain', 'Thunder', 'Fire'\n  ];\n  \n  const predictions = [];\n  const numPredictions = Math.floor(Math.random() * 3) + 1; // 1-3个预测\n  \n  for (let i = 0; i < numPredictions; i++) {\n    const eventIndex = Math.floor(Math.random() * audioEvents.length);\n    const confidence = Math.random() * 0.4 + 0.6; // 0.6-1.0的置信度\n    \n    predictions.push({\n      className: audioEvents[eventIndex],\n      confidence,\n      classIndex: eventIndex\n    });\n  }\n  \n  return predictions.sort((a, b) => b.confidence - a.confidence);\n}\n\n/**\n * 生成模拟的语音指令结果\n */\nfunction generateMockCommands() {\n  const commands = ['yes', 'no', 'up', 'down', 'left', 'right', 'go', 'stop'];\n  \n  // 随机决定是否检测到指令\n  if (Math.random() < 0.3) { // 30%概率检测到指令\n    const commandIndex = Math.floor(Math.random() * commands.length);\n    const confidence = Math.random() * 0.3 + 0.7; // 0.7-1.0的置信度\n    \n    return [{\n      command: commands[commandIndex],\n      confidence,\n      commandIndex,\n      className: commands[commandIndex],\n      classIndex: commandIndex\n    }];\n  }\n  \n  return [];\n}\n\nconsole.log('AI Worker loaded and ready');\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "__webpack_require__.amdD = function () {\n\tthrow new Error('define cannot be used indirect');\n};", "__webpack_require__.amdO = {};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.nmd = (module) => {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "__webpack_require__.j = 672;", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t672: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkyoutube_aural_visual_bridge\"] = self[\"webpackChunkyoutube_aural_visual_bridge\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [340,121], () => (__webpack_require__(2110)))\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["deferred", "importScripts", "isInitialized", "self", "addEventListener", "async", "e", "type", "data", "id", "result", "config", "message", "tf", "ready", "enableYAMNet", "enableSpeechCommands", "backend", "getBackend", "memory", "error", "initialize", "Error", "startTime", "performance", "now", "mockPredictions", "audioEvents", "predictions", "numPredictions", "Math", "floor", "random", "i", "eventIndex", "length", "confidence", "push", "className", "classIndex", "sort", "a", "b", "generateMockPredictions", "inferenceTime", "timestamp", "Date", "modelType", "classifyAudio", "mockCommands", "commands", "commandIndex", "command", "generateMockCommands", "detectSpeech", "hasYAMNet", "yamnetModel", "hasSpeechCommands", "speechCommandsModel", "postMessage", "success", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "exports", "module", "loaded", "__webpack_modules__", "call", "m", "amdD", "amdO", "O", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "fulfilled", "j", "Object", "keys", "every", "key", "splice", "r", "n", "getter", "__esModule", "d", "definition", "o", "defineProperty", "enumerable", "get", "g", "globalThis", "this", "Function", "window", "obj", "prop", "prototype", "hasOwnProperty", "Symbol", "toStringTag", "value", "nmd", "paths", "children", "installedChunks", "chunkId", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "some", "chunkLoadingGlobal", "for<PERSON>ach", "bind", "__webpack_exports__"], "sourceRoot": ""}