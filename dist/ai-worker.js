(()=>{var e,n={551:()=>{},1234:()=>{},2110:(e,n,r)=>{var t=r(6293);importScripts("https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.15.0/dist/tf.min.js");let o=!1;self.addEventListener("message",async function(e){const{type:n,data:r,id:a}=e.data;try{let e;switch(n){case"INITIALIZE":e=await async function(e={}){if(o)return{message:"Already initialized"};try{return await t.ready(),e.enableYAMNet,e.enableSpeechCommands,o=!0,{message:"AI Worker initialized successfully",backend:t.getBackend(),memory:t.memory()}}catch(n){throw n}}(r);break;case"CLASSIFY_AUDIO":e=await async function(){if(!o)throw new Error("AI Worker not initialized");const e=performance.now();try{const n=function(){const e=["Speech","Music","Laughter","Applause","Crying","Footsteps","Door","Knock","Glass","Gunshot","Siren","Car","Dog","Cat","Bird","Water","Wind","Rain","Thunder","Fire"],n=[],r=Math.floor(3*Math.random())+1;for(let t=0;t<r;t++){const r=Math.floor(Math.random()*e.length),t=.4*Math.random()+.6;n.push({className:e[r],confidence:t,classIndex:r})}return n.sort((e,n)=>n.confidence-e.confidence)}();return{predictions:n,inferenceTime:performance.now()-e,timestamp:Date.now(),modelType:"yamnet"}}catch(n){throw n}}();break;case"DETECT_SPEECH":e=await async function(){if(!o)throw new Error("AI Worker not initialized");const e=performance.now();try{const n=function(){const e=["yes","no","up","down","left","right","go","stop"];if(Math.random()<.3){const n=Math.floor(Math.random()*e.length),r=.3*Math.random()+.7;return[{command:e[n],confidence:r,commandIndex:n,className:e[n],classIndex:n}]}return[]}();return{commands:n,inferenceTime:performance.now()-e,timestamp:Date.now(),modelType:"speech_commands"}}catch(n){throw n}}();break;case"GET_STATUS":e={isInitialized:o,hasYAMNet:!1,hasSpeechCommands:!1,backend:o?t.getBackend():null,memory:o?t.memory():null};break;default:throw new Error(`Unknown message type: ${n}`)}self.postMessage({id:a,success:!0,result:e})}catch(s){self.postMessage({id:a,success:!1,error:s.message})}})},4530:()=>{},5817:()=>{},8108:()=>{},8590:()=>{}},r={};function t(e){var o=r[e];if(void 0!==o)return o.exports;var a=r[e]={id:e,loaded:!1,exports:{}};return n[e].call(a.exports,a,a.exports,t),a.loaded=!0,a.exports}t.m=n,t.amdD=function(){throw new Error("define cannot be used indirect")},t.amdO={},e=[],t.O=(n,r,o,a)=>{if(!r){var s=1/0;for(l=0;l<e.length;l++){for(var[r,o,a]=e[l],i=!0,c=0;c<r.length;c++)(!1&a||s>=a)&&Object.keys(t.O).every(e=>t.O[e](r[c]))?r.splice(c--,1):(i=!1,a<s&&(s=a));if(i){e.splice(l--,1);var d=o();void 0!==d&&(n=d)}}return n}a=a||0;for(var l=e.length;l>0&&e[l-1][2]>a;l--)e[l]=e[l-1];e[l]=[r,o,a]},t.n=e=>{var n=e&&e.__esModule?()=>e.default:()=>e;return t.d(n,{a:n}),n},t.d=(e,n)=>{for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},t.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),t.o=(e,n)=>Object.prototype.hasOwnProperty.call(e,n),t.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),t.j=672,(()=>{var e={672:0};t.O.j=n=>0===e[n];var n=(n,r)=>{var o,a,[s,i,c]=r,d=0;if(s.some(n=>0!==e[n])){for(o in i)t.o(i,o)&&(t.m[o]=i[o]);if(c)var l=c(t)}for(n&&n(r);d<s.length;d++)a=s[d],t.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return t.O(l)},r=self.webpackChunkyoutube_aural_visual_bridge=self.webpackChunkyoutube_aural_visual_bridge||[];r.forEach(n.bind(null,0)),r.push=n.bind(null,r.push.bind(r))})();var o=t.O(void 0,[340,121],()=>t(2110));o=t.O(o)})();
//# sourceMappingURL=ai-worker.js.map