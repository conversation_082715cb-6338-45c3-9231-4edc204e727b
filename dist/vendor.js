(self.webpackChunkyoutube_aural_visual_bridge=self.webpackChunkyoutube_aural_visual_bridge||[]).push([[121],{2:(t,r,n)=>{var e=n(6926),i=n(9310);(t.exports=function(t,r){return i[t]||(i[t]=void 0!==r?r:{})})("versions",[]).push({version:"3.29.1",mode:e?"pure":"global",copyright:"© 2014-2023 <PERSON> (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.29.1/LICENSE",source:"https://github.com/zloirock/core-js"})},200:(t,r,n)=>{var e=function(t){return t&&t.Math==Math&&t};t.exports=e("object"==typeof globalThis&&globalThis)||e("object"==typeof window&&window)||e("object"==typeof self&&self)||e("object"==typeof n.g&&n.g)||function(){return this}()||Function("return this")()},281:(t,r,n)=>{var e=n(8823),i=Function.prototype,o=i.call,u=e&&i.bind.bind(o,o);t.exports=e?u:function(t){return function(){return o.apply(t,arguments)}}},665:(t,r,n)=>{var e=n(281),i=0,o=Math.random(),u=e(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+u(++i+o,36)}},874:(t,r,n)=>{var e=n(2368),i=n(5335),o=n(2328),u=n(6457),s=n(9751),a=n(1602),h=TypeError,f=a("toPrimitive");t.exports=function(t,r){if(!i(t)||o(t))return t;var n,a=u(t,f);if(a){if(void 0===r&&(r="default"),n=e(a,t,r),!i(n)||o(n))return n;throw h("Can't convert object to primitive value")}return void 0===r&&(r="number"),s(t,r)}},1229:(t,r,n)=>{var e=n(8406),i=TypeError;t.exports=function(t){if(e(t))throw i("Can't call method on "+t);return t}},1385:(t,r,n)=>{var e=n(281),i=n(4601);t.exports=function(t,r,n){try{return e(i(Object.getOwnPropertyDescriptor(t,r)[n]))}catch(o){}}},1602:(t,r,n)=>{var e=n(200),i=n(2),o=n(6490),u=n(665),s=n(2072),a=n(5225),h=e.Symbol,f=i("wks"),c=a?h.for||h:h&&h.withoutSetter||u;t.exports=function(t){return o(f,t)||(f[t]=s&&o(h,t)?h[t]:c("Symbol."+t)),f[t]}},2071:(t,r,n)=>{var e=n(5077),i=n(6490),o=Function.prototype,u=e&&Object.getOwnPropertyDescriptor,s=i(o,"name"),a=s&&"something"===function(){}.name,h=s&&(!e||e&&u(o,"name").configurable);t.exports={EXISTS:s,PROPER:a,CONFIGURABLE:h}},2072:(t,r,n)=>{var e=n(6845),i=n(2074);t.exports=!!Object.getOwnPropertySymbols&&!i(function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&e&&e<41})},2074:t=>{t.exports=function(t){try{return!!t()}catch(r){return!0}}},2328:(t,r,n)=>{var e=n(6492),i=n(8420),o=n(7658),u=n(5225),s=Object;t.exports=u?function(t){return"symbol"==typeof t}:function(t){var r=e("Symbol");return i(r)&&o(r.prototype,s(t))}},2368:(t,r,n)=>{var e=n(8823),i=Function.prototype.call;t.exports=e?i.bind(i):function(){return i.apply(i,arguments)}},2612:(t,r,n)=>{var e=n(1229),i=Object;t.exports=function(t){return i(e(t))}},3031:function(t,r,n){var e;!function(t,i){function o(t){var r=this,n="";r.next=function(){var t=r.x^r.x>>>2;return r.x=r.y,r.y=r.z,r.z=r.w,r.w=r.v,(r.d=r.d+362437|0)+(r.v=r.v^r.v<<4^t^t<<1)|0},r.x=0,r.y=0,r.z=0,r.w=0,r.v=0,t===(0|t)?r.x=t:n+=t;for(var e=0;e<n.length+64;e++)r.x^=0|n.charCodeAt(e),e==n.length&&(r.d=r.x<<10^r.x>>>4),r.next()}function u(t,r){return r.x=t.x,r.y=t.y,r.z=t.z,r.w=t.w,r.v=t.v,r.d=t.d,r}function s(t,r){var n=new o(t),e=r&&r.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var t=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},i.int32=n.next,i.quick=i,e&&("object"==typeof e&&u(e,n),i.state=function(){return u(n,{})}),i}i&&i.exports?i.exports=s:n.amdD&&n.amdO?void 0===(e=function(){return s}.call(r,n,r,i))||(i.exports=e):this.xorwow=s}(0,t=n.nmd(t),n.amdD)},3062:(t,r,n)=>{var e=n(3129),i=n(8420),o=n(8569),u=n(1602)("toStringTag"),s=Object,a="Arguments"==o(function(){return arguments}());t.exports=e?o:function(t){var r,n,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,r){try{return t[r]}catch(n){}}(r=s(t),u))?n:a?o(r):"Object"==(e=o(r))&&i(r.callee)?"Arguments":e}},3129:(t,r,n)=>{var e={};e[n(1602)("toStringTag")]="z",t.exports="[object z]"===String(e)},3181:function(t,r,n){var e;!function(t,i){function o(t){var r=this,n="";r.x=0,r.y=0,r.z=0,r.w=0,r.next=function(){var t=r.x^r.x<<11;return r.x=r.y,r.y=r.z,r.z=r.w,r.w^=r.w>>>19^t^t>>>8},t===(0|t)?r.x=t:n+=t;for(var e=0;e<n.length+64;e++)r.x^=0|n.charCodeAt(e),r.next()}function u(t,r){return r.x=t.x,r.y=t.y,r.z=t.z,r.w=t.w,r}function s(t,r){var n=new o(t),e=r&&r.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var t=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},i.int32=n.next,i.quick=i,e&&("object"==typeof e&&u(e,n),i.state=function(){return u(n,{})}),i}i&&i.exports?i.exports=s:n.amdD&&n.amdO?void 0===(e=function(){return s}.call(r,n,r,i))||(i.exports=e):this.xor128=s}(0,t=n.nmd(t),n.amdD)},3262:(t,r,n)=>{var e=n(200),i=n(5335),o=e.document,u=i(o)&&i(o.createElement);t.exports=function(t){return u?o.createElement(t):{}}},3266:(t,r,n)=>{"use strict";var e=n(200),i=n(2368),o=n(5343),u=n(3493),s=n(3720),a=n(2612),h=n(2074),f=e.RangeError,c=e.Int8Array,l=c&&c.prototype,g=l&&l.set,p=o.aTypedArray,v=o.exportTypedArrayMethod,d=!h(function(){var t=new Uint8ClampedArray(2);return i(g,t,{length:1,0:3},1),3!==t[1]}),y=d&&o.NATIVE_ARRAY_BUFFER_VIEWS&&h(function(){var t=new c(2);return t.set(1),t.set("2",1),0!==t[0]||2!==t[1]});v("set",function(t){p(this);var r=s(arguments.length>1?arguments[1]:void 0,1),n=a(t);if(d)return i(g,this,n,r);var e=this.length,o=u(n),h=0;if(o+r>e)throw f("Wrong length");for(;h<o;)this[r+h]=n[h++]},!d||y)},3493:(t,r,n)=>{var e=n(3747);t.exports=function(t){return e(t.length)}},3610:(t,r,n)=>{var e=n(5077),i=n(7694),o=n(4491),u=n(3938),s=n(6032),a=TypeError,h=Object.defineProperty,f=Object.getOwnPropertyDescriptor,c="enumerable",l="configurable",g="writable";r.f=e?o?function(t,r,n){if(u(t),r=s(r),u(n),"function"==typeof t&&"prototype"===r&&"value"in n&&g in n&&!n[g]){var e=f(t,r);e&&e[g]&&(t[r]=n.value,n={configurable:l in n?n[l]:e[l],enumerable:c in n?n[c]:e[c],writable:!1})}return h(t,r,n)}:h:function(t,r,n){if(u(t),r=s(r),u(n),i)try{return h(t,r,n)}catch(e){}if("get"in n||"set"in n)throw a("Accessors not supported");return"value"in n&&(t[r]=n.value),t}},3717:function(t,r,n){var e;!function(t,i){function o(t){var r=this,n="";r.next=function(){var t=r.b,n=r.c,e=r.d,i=r.a;return t=t<<25^t>>>7^n,n=n-e|0,e=e<<24^e>>>8^i,i=i-t|0,r.b=t=t<<20^t>>>12^n,r.c=n=n-e|0,r.d=e<<16^n>>>16^i,r.a=i-t|0},r.a=0,r.b=0,r.c=-1640531527,r.d=1367130551,t===Math.floor(t)?(r.a=t/4294967296|0,r.b=0|t):n+=t;for(var e=0;e<n.length+20;e++)r.b^=0|n.charCodeAt(e),r.next()}function u(t,r){return r.a=t.a,r.b=t.b,r.c=t.c,r.d=t.d,r}function s(t,r){var n=new o(t),e=r&&r.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var t=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},i.int32=n.next,i.quick=i,e&&("object"==typeof e&&u(e,n),i.state=function(){return u(n,{})}),i}i&&i.exports?i.exports=s:n.amdD&&n.amdO?void 0===(e=function(){return s}.call(r,n,r,i))||(i.exports=e):this.tychei=s}(0,t=n.nmd(t),n.amdD)},3720:(t,r,n)=>{var e=n(5955),i=RangeError;t.exports=function(t,r){var n=e(t);if(n%r)throw i("Wrong offset");return n}},3747:(t,r,n)=>{var e=n(9328),i=Math.min;t.exports=function(t){return t>0?i(e(t),9007199254740991):0}},3838:t=>{var r=String;t.exports=function(t){try{return r(t)}catch(n){return"Object"}}},3938:(t,r,n)=>{var e=n(5335),i=String,o=TypeError;t.exports=function(t){if(e(t))return t;throw o(i(t)+" is not an object")}},4491:(t,r,n)=>{var e=n(5077),i=n(2074);t.exports=e&&i(function(){return 42!=Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype})},4601:(t,r,n)=>{var e=n(8420),i=n(3838),o=TypeError;t.exports=function(t){if(e(t))return t;throw o(i(t)+" is not a function")}},4801:function(t,r,n){var e;!function(i,o,u){var s,a=256,h=u.pow(a,6),f=u.pow(2,52),c=2*f,l=255;function g(t,r,n){var e=[],l=y(d((r=1==r?{entropy:!0}:r||{}).entropy?[t,x(o)]:null==t?function(){try{var t;return s&&(t=s.randomBytes)?t=t(a):(t=new Uint8Array(a),(i.crypto||i.msCrypto).getRandomValues(t)),x(t)}catch(e){var r=i.navigator,n=r&&r.plugins;return[+new Date,i,n,i.screen,x(o)]}}():t,3),e),g=new p(e),w=function(){for(var t=g.g(6),r=h,n=0;t<f;)t=(t+n)*a,r*=a,n=g.g(1);for(;t>=c;)t/=2,r/=2,n>>>=1;return(t+n)/r};return w.int32=function(){return 0|g.g(4)},w.quick=function(){return g.g(4)/4294967296},w.double=w,y(x(g.S),o),(r.pass||n||function(t,r,n,e){return e&&(e.S&&v(e,g),t.state=function(){return v(g,{})}),n?(u.random=t,r):t})(w,l,"global"in r?r.global:this==u,r.state)}function p(t){var r,n=t.length,e=this,i=0,o=e.i=e.j=0,u=e.S=[];for(n||(t=[n++]);i<a;)u[i]=i++;for(i=0;i<a;i++)u[i]=u[o=l&o+t[i%n]+(r=u[i])],u[o]=r;(e.g=function(t){for(var r,n=0,i=e.i,o=e.j,u=e.S;t--;)r=u[i=l&i+1],n=n*a+u[l&(u[i]=u[o=l&o+r])+(u[o]=r)];return e.i=i,e.j=o,n})(a)}function v(t,r){return r.i=t.i,r.j=t.j,r.S=t.S.slice(),r}function d(t,r){var n,e=[],i=typeof t;if(r&&"object"==i)for(n in t)try{e.push(d(t[n],r-1))}catch(o){}return e.length?e:"string"==i?t:t+"\0"}function y(t,r){for(var n,e=t+"",i=0;i<e.length;)r[l&i]=l&(n^=19*r[l&i])+e.charCodeAt(i++);return x(r)}function x(t){return String.fromCharCode.apply(0,t)}if(y(u.random(),o),t.exports){t.exports=g;try{s=n(1234)}catch(w){}}else void 0===(e=function(){return g}.call(r,n,r,t))||(t.exports=e)}("undefined"!=typeof self?self:this,[],Math)},5077:(t,r,n)=>{var e=n(2074);t.exports=!e(function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})},5225:(t,r,n)=>{var e=n(2072);t.exports=e&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},5335:(t,r,n)=>{var e=n(8420),i=n(6568),o=i.all;t.exports=i.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:e(t)||t===o}:function(t){return"object"==typeof t?null!==t:e(t)}},5343:(t,r,n)=>{"use strict";var e,i,o,u=n(6004),s=n(5077),a=n(200),h=n(8420),f=n(5335),c=n(6490),l=n(3062),g=n(3838),p=n(7712),v=n(7485),d=n(6477),y=n(7658),x=n(7970),w=n(9686),b=n(1602),m=n(665),A=n(9206),E=A.enforce,O=A.get,_=a.Int8Array,S=_&&_.prototype,j=a.Uint8ClampedArray,T=j&&j.prototype,N=_&&x(_),q=S&&x(S),I=Object.prototype,D=a.TypeError,B=b("toStringTag"),M=m("TYPED_ARRAY_TAG"),R="TypedArrayConstructor",C=u&&!!w&&"Opera"!==l(a.opera),U=!1,P={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},L={BigInt64Array:8,BigUint64Array:8},k=function(t){var r=x(t);if(f(r)){var n=O(r);return n&&c(n,R)?n[R]:k(r)}},z=function(t){if(!f(t))return!1;var r=l(t);return c(P,r)||c(L,r)};for(e in P)(o=(i=a[e])&&i.prototype)?E(o)[R]=i:C=!1;for(e in L)(o=(i=a[e])&&i.prototype)&&(E(o)[R]=i);if((!C||!h(N)||N===Function.prototype)&&(N=function(){throw D("Incorrect invocation")},C))for(e in P)a[e]&&w(a[e],N);if((!C||!q||q===I)&&(q=N.prototype,C))for(e in P)a[e]&&w(a[e].prototype,q);if(C&&x(T)!==q&&w(T,q),s&&!c(q,B))for(e in U=!0,d(q,B,{configurable:!0,get:function(){return f(this)?this[M]:void 0}}),P)a[e]&&p(a[e],M,e);t.exports={NATIVE_ARRAY_BUFFER_VIEWS:C,TYPED_ARRAY_TAG:U&&M,aTypedArray:function(t){if(z(t))return t;throw D("Target is not a typed array")},aTypedArrayConstructor:function(t){if(h(t)&&(!w||y(N,t)))return t;throw D(g(t)+" is not a typed array constructor")},exportTypedArrayMethod:function(t,r,n,e){if(s){if(n)for(var i in P){var o=a[i];if(o&&c(o.prototype,t))try{delete o.prototype[t]}catch(u){try{o.prototype[t]=r}catch(h){}}}q[t]&&!n||v(q,t,n?r:C&&S[t]||r,e)}},exportTypedArrayStaticMethod:function(t,r,n){var e,i;if(s){if(w){if(n)for(e in P)if((i=a[e])&&c(i,t))try{delete i[t]}catch(o){}if(N[t]&&!n)return;try{return v(N,t,n?r:C&&N[t]||r)}catch(o){}}for(e in P)!(i=a[e])||i[t]&&!n||v(i,t,r)}},getTypedArrayConstructor:k,isView:function(t){if(!f(t))return!1;var r=l(t);return"DataView"===r||c(P,r)||c(L,r)},isTypedArray:z,TypedArray:N,TypedArrayPrototype:q}},5904:(t,r,n)=>{var e=n(2),i=n(665),o=e("keys");t.exports=function(t){return o[t]||(o[t]=i(t))}},5955:(t,r,n)=>{var e=n(9328),i=RangeError;t.exports=function(t){var r=e(t);if(r<0)throw i("The argument can't be less than 0");return r}},6004:t=>{t.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},6032:(t,r,n)=>{var e=n(874),i=n(2328);t.exports=function(t){var r=e(t,"string");return i(r)?r:r+""}},6457:(t,r,n)=>{var e=n(4601),i=n(8406);t.exports=function(t,r){var n=t[r];return i(n)?void 0:e(n)}},6477:(t,r,n)=>{var e=n(8218),i=n(3610);t.exports=function(t,r,n){return n.get&&e(n.get,r,{getter:!0}),n.set&&e(n.set,r,{setter:!0}),i.f(t,r,n)}},6490:(t,r,n)=>{var e=n(281),i=n(2612),o=e({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,r){return o(i(t),r)}},6492:(t,r,n)=>{var e=n(200),i=n(8420);t.exports=function(t,r){return arguments.length<2?(n=e[t],i(n)?n:void 0):e[t]&&e[t][r];var n}},6568:t=>{var r="object"==typeof document&&document.all,n=void 0===r&&void 0!==r;t.exports={all:r,IS_HTMLDDA:n}},6833:function(t,r,n){var e;!function(t,i){function o(t){var r=this;r.next=function(){var t,n,e=r.w,i=r.X,o=r.i;return r.w=e=e+1640531527|0,n=i[o+34&127],t=i[o=o+1&127],n^=n<<13,t^=t<<17,n^=n>>>15,t^=t>>>12,n=i[o]=n^t,r.i=o,n+(e^e>>>16)|0},function(t,r){var n,e,i,o,u,s=[],a=128;for(r===(0|r)?(e=r,r=null):(r+="\0",e=0,a=Math.max(a,r.length)),i=0,o=-32;o<a;++o)r&&(e^=r.charCodeAt((o+32)%r.length)),0===o&&(u=e),e^=e<<10,e^=e>>>15,e^=e<<4,e^=e>>>13,o>=0&&(u=u+1640531527|0,i=0==(n=s[127&o]^=e+u)?i+1:0);for(i>=128&&(s[127&(r&&r.length||0)]=-1),i=127,o=512;o>0;--o)e=s[i+34&127],n=s[i=i+1&127],e^=e<<13,n^=n<<17,e^=e>>>15,n^=n>>>12,s[i]=e^n;t.w=u,t.X=s,t.i=i}(r,t)}function u(t,r){return r.i=t.i,r.w=t.w,r.X=t.X.slice(),r}function s(t,r){null==t&&(t=+new Date);var n=new o(t),e=r&&r.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var t=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},i.int32=n.next,i.quick=i,e&&(e.X&&u(e,n),i.state=function(){return u(n,{})}),i}i&&i.exports?i.exports=s:n.amdD&&n.amdO?void 0===(e=function(){return s}.call(r,n,r,i))||(i.exports=e):this.xor4096=s}(0,t=n.nmd(t),n.amdD)},6843:t=>{t.exports=function(t,r){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:r}}},6845:(t,r,n)=>{var e,i,o=n(200),u=n(7061),s=o.process,a=o.Deno,h=s&&s.versions||a&&a.version,f=h&&h.v8;f&&(i=(e=f.split("."))[0]>0&&e[0]<4?1:+(e[0]+e[1])),!i&&u&&(!(e=u.match(/Edge\/(\d+)/))||e[1]>=74)&&(e=u.match(/Chrome\/(\d+)/))&&(i=+e[1]),t.exports=i},6926:t=>{t.exports=!1},7061:t=>{t.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},7168:(t,r,n)=>{var e=n(2074);t.exports=!e(function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})},7180:function(t,r,n){var e;!function(t,i){function o(t){var r=this,n=function(){var t=4022871197,r=function(r){r=String(r);for(var n=0;n<r.length;n++){var e=.02519603282416938*(t+=r.charCodeAt(n));e-=t=e>>>0,t=(e*=t)>>>0,t+=4294967296*(e-=t)}return 2.3283064365386963e-10*(t>>>0)};return r}();r.next=function(){var t=2091639*r.s0+2.3283064365386963e-10*r.c;return r.s0=r.s1,r.s1=r.s2,r.s2=t-(r.c=0|t)},r.c=1,r.s0=n(" "),r.s1=n(" "),r.s2=n(" "),r.s0-=n(t),r.s0<0&&(r.s0+=1),r.s1-=n(t),r.s1<0&&(r.s1+=1),r.s2-=n(t),r.s2<0&&(r.s2+=1),n=null}function u(t,r){return r.c=t.c,r.s0=t.s0,r.s1=t.s1,r.s2=t.s2,r}function s(t,r){var n=new o(t),e=r&&r.state,i=n.next;return i.int32=function(){return 4294967296*n.next()|0},i.double=function(){return i()+11102230246251565e-32*(2097152*i()|0)},i.quick=i,e&&("object"==typeof e&&u(e,n),i.state=function(){return u(n,{})}),i}i&&i.exports?i.exports=s:n.amdD&&n.amdO?void 0===(e=function(){return s}.call(r,n,r,i))||(i.exports=e):this.alea=s}(0,t=n.nmd(t),n.amdD)},7391:(t,r,n)=>{var e=n(7180),i=n(3181),o=n(3031),u=n(9067),s=n(6833),a=n(3717),h=n(4801);h.alea=e,h.xor128=i,h.xorwow=o,h.xorshift7=u,h.xor4096=s,h.tychei=a,t.exports=h},7473:(t,r,n)=>{var e=n(8420),i=String,o=TypeError;t.exports=function(t){if("object"==typeof t||e(t))return t;throw o("Can't set "+i(t)+" as a prototype")}},7485:(t,r,n)=>{var e=n(8420),i=n(3610),o=n(8218),u=n(9430);t.exports=function(t,r,n,s){s||(s={});var a=s.enumerable,h=void 0!==s.name?s.name:r;if(e(n)&&o(n,h,s),s.global)a?t[r]=n:u(r,n);else{try{s.unsafe?t[r]&&(a=!0):delete t[r]}catch(f){}a?t[r]=n:i.f(t,r,{value:n,enumerable:!1,configurable:!s.nonConfigurable,writable:!s.nonWritable})}return t}},7658:(t,r,n)=>{var e=n(281);t.exports=e({}.isPrototypeOf)},7694:(t,r,n)=>{var e=n(5077),i=n(2074),o=n(3262);t.exports=!e&&!i(function(){return 7!=Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a})},7708:t=>{t.exports={}},7712:(t,r,n)=>{var e=n(5077),i=n(3610),o=n(6843);t.exports=e?function(t,r,n){return i.f(t,r,o(1,n))}:function(t,r,n){return t[r]=n,t}},7970:(t,r,n)=>{var e=n(6490),i=n(8420),o=n(2612),u=n(5904),s=n(7168),a=u("IE_PROTO"),h=Object,f=h.prototype;t.exports=s?h.getPrototypeOf:function(t){var r=o(t);if(e(r,a))return r[a];var n=r.constructor;return i(n)&&r instanceof n?n.prototype:r instanceof h?f:null}},8218:(t,r,n)=>{var e=n(281),i=n(2074),o=n(8420),u=n(6490),s=n(5077),a=n(2071).CONFIGURABLE,h=n(9965),f=n(9206),c=f.enforce,l=f.get,g=String,p=Object.defineProperty,v=e("".slice),d=e("".replace),y=e([].join),x=s&&!i(function(){return 8!==p(function(){},"length",{value:8}).length}),w=String(String).split("String"),b=t.exports=function(t,r,n){"Symbol("===v(g(r),0,7)&&(r="["+d(g(r),/^Symbol\(([^)]*)\)/,"$1")+"]"),n&&n.getter&&(r="get "+r),n&&n.setter&&(r="set "+r),(!u(t,"name")||a&&t.name!==r)&&(s?p(t,"name",{value:r,configurable:!0}):t.name=r),x&&n&&u(n,"arity")&&t.length!==n.arity&&p(t,"length",{value:n.arity});try{n&&u(n,"constructor")&&n.constructor?s&&p(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(i){}var e=c(t);return u(e,"source")||(e.source=y(w,"string"==typeof r?r:"")),t};Function.prototype.toString=b(function(){return o(this)&&l(this).source||h(this)},"toString")},8369:(t,r,n)=>{var e=n(200),i=n(8420),o=e.WeakMap;t.exports=i(o)&&/native code/.test(String(o))},8406:t=>{t.exports=function(t){return null==t}},8420:(t,r,n)=>{var e=n(6568),i=e.all;t.exports=e.IS_HTMLDDA?function(t){return"function"==typeof t||t===i}:function(t){return"function"==typeof t}},8569:(t,r,n)=>{var e=n(281),i=e({}.toString),o=e("".slice);t.exports=function(t){return o(i(t),8,-1)}},8570:t=>{t.exports=n;var r=null;try{r=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch(_){}function n(t,r,n){this.low=0|t,this.high=0|r,this.unsigned=!!n}function e(t){return!0===(t&&t.__isLong__)}n.prototype.__isLong__,Object.defineProperty(n.prototype,"__isLong__",{value:!0}),n.isLong=e;var i={},o={};function u(t,r){var n,e,u;return r?(u=0<=(t>>>=0)&&t<256)&&(e=o[t])?e:(n=a(t,(0|t)<0?-1:0,!0),u&&(o[t]=n),n):(u=-128<=(t|=0)&&t<128)&&(e=i[t])?e:(n=a(t,t<0?-1:0,!1),u&&(i[t]=n),n)}function s(t,r){if(isNaN(t))return r?y:d;if(r){if(t<0)return y;if(t>=g)return A}else{if(t<=-p)return E;if(t+1>=p)return m}return t<0?s(-t,r).neg():a(t%l|0,t/l|0,r)}function a(t,r,e){return new n(t,r,e)}n.fromInt=u,n.fromNumber=s,n.fromBits=a;var h=Math.pow;function f(t,r,n){if(0===t.length)throw Error("empty string");if("NaN"===t||"Infinity"===t||"+Infinity"===t||"-Infinity"===t)return d;if("number"==typeof r?(n=r,r=!1):r=!!r,(n=n||10)<2||36<n)throw RangeError("radix");var e;if((e=t.indexOf("-"))>0)throw Error("interior hyphen");if(0===e)return f(t.substring(1),r,n).neg();for(var i=s(h(n,8)),o=d,u=0;u<t.length;u+=8){var a=Math.min(8,t.length-u),c=parseInt(t.substring(u,u+a),n);if(a<8){var l=s(h(n,a));o=o.mul(l).add(s(c))}else o=(o=o.mul(i)).add(s(c))}return o.unsigned=r,o}function c(t,r){return"number"==typeof t?s(t,r):"string"==typeof t?f(t,r):a(t.low,t.high,"boolean"==typeof r?r:t.unsigned)}n.fromString=f,n.fromValue=c;var l=4294967296,g=l*l,p=g/2,v=u(1<<24),d=u(0);n.ZERO=d;var y=u(0,!0);n.UZERO=y;var x=u(1);n.ONE=x;var w=u(1,!0);n.UONE=w;var b=u(-1);n.NEG_ONE=b;var m=a(-1,2147483647,!1);n.MAX_VALUE=m;var A=a(-1,-1,!0);n.MAX_UNSIGNED_VALUE=A;var E=a(0,-2147483648,!1);n.MIN_VALUE=E;var O=n.prototype;O.toInt=function(){return this.unsigned?this.low>>>0:this.low},O.toNumber=function(){return this.unsigned?(this.high>>>0)*l+(this.low>>>0):this.high*l+(this.low>>>0)},O.toString=function(t){if((t=t||10)<2||36<t)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(this.eq(E)){var r=s(t),n=this.div(r),e=n.mul(r).sub(this);return n.toString(t)+e.toInt().toString(t)}return"-"+this.neg().toString(t)}for(var i=s(h(t,6),this.unsigned),o=this,u="";;){var a=o.div(i),f=(o.sub(a.mul(i)).toInt()>>>0).toString(t);if((o=a).isZero())return f+u;for(;f.length<6;)f="0"+f;u=""+f+u}},O.getHighBits=function(){return this.high},O.getHighBitsUnsigned=function(){return this.high>>>0},O.getLowBits=function(){return this.low},O.getLowBitsUnsigned=function(){return this.low>>>0},O.getNumBitsAbs=function(){if(this.isNegative())return this.eq(E)?64:this.neg().getNumBitsAbs();for(var t=0!=this.high?this.high:this.low,r=31;r>0&&!(t&1<<r);r--);return 0!=this.high?r+33:r+1},O.isZero=function(){return 0===this.high&&0===this.low},O.eqz=O.isZero,O.isNegative=function(){return!this.unsigned&&this.high<0},O.isPositive=function(){return this.unsigned||this.high>=0},O.isOdd=function(){return!(1&~this.low)},O.isEven=function(){return!(1&this.low)},O.equals=function(t){return e(t)||(t=c(t)),(this.unsigned===t.unsigned||this.high>>>31!=1||t.high>>>31!=1)&&(this.high===t.high&&this.low===t.low)},O.eq=O.equals,O.notEquals=function(t){return!this.eq(t)},O.neq=O.notEquals,O.ne=O.notEquals,O.lessThan=function(t){return this.comp(t)<0},O.lt=O.lessThan,O.lessThanOrEqual=function(t){return this.comp(t)<=0},O.lte=O.lessThanOrEqual,O.le=O.lessThanOrEqual,O.greaterThan=function(t){return this.comp(t)>0},O.gt=O.greaterThan,O.greaterThanOrEqual=function(t){return this.comp(t)>=0},O.gte=O.greaterThanOrEqual,O.ge=O.greaterThanOrEqual,O.compare=function(t){if(e(t)||(t=c(t)),this.eq(t))return 0;var r=this.isNegative(),n=t.isNegative();return r&&!n?-1:!r&&n?1:this.unsigned?t.high>>>0>this.high>>>0||t.high===this.high&&t.low>>>0>this.low>>>0?-1:1:this.sub(t).isNegative()?-1:1},O.comp=O.compare,O.negate=function(){return!this.unsigned&&this.eq(E)?E:this.not().add(x)},O.neg=O.negate,O.add=function(t){e(t)||(t=c(t));var r=this.high>>>16,n=65535&this.high,i=this.low>>>16,o=65535&this.low,u=t.high>>>16,s=65535&t.high,h=t.low>>>16,f=0,l=0,g=0,p=0;return g+=(p+=o+(65535&t.low))>>>16,l+=(g+=i+h)>>>16,f+=(l+=n+s)>>>16,f+=r+u,a((g&=65535)<<16|(p&=65535),(f&=65535)<<16|(l&=65535),this.unsigned)},O.subtract=function(t){return e(t)||(t=c(t)),this.add(t.neg())},O.sub=O.subtract,O.multiply=function(t){if(this.isZero())return d;if(e(t)||(t=c(t)),r)return a(r.mul(this.low,this.high,t.low,t.high),r.get_high(),this.unsigned);if(t.isZero())return d;if(this.eq(E))return t.isOdd()?E:d;if(t.eq(E))return this.isOdd()?E:d;if(this.isNegative())return t.isNegative()?this.neg().mul(t.neg()):this.neg().mul(t).neg();if(t.isNegative())return this.mul(t.neg()).neg();if(this.lt(v)&&t.lt(v))return s(this.toNumber()*t.toNumber(),this.unsigned);var n=this.high>>>16,i=65535&this.high,o=this.low>>>16,u=65535&this.low,h=t.high>>>16,f=65535&t.high,l=t.low>>>16,g=65535&t.low,p=0,y=0,x=0,w=0;return x+=(w+=u*g)>>>16,y+=(x+=o*g)>>>16,x&=65535,y+=(x+=u*l)>>>16,p+=(y+=i*g)>>>16,y&=65535,p+=(y+=o*l)>>>16,y&=65535,p+=(y+=u*f)>>>16,p+=n*g+i*l+o*f+u*h,a((x&=65535)<<16|(w&=65535),(p&=65535)<<16|(y&=65535),this.unsigned)},O.mul=O.multiply,O.divide=function(t){if(e(t)||(t=c(t)),t.isZero())throw Error("division by zero");var n,i,o;if(r)return this.unsigned||-2147483648!==this.high||-1!==t.low||-1!==t.high?a((this.unsigned?r.div_u:r.div_s)(this.low,this.high,t.low,t.high),r.get_high(),this.unsigned):this;if(this.isZero())return this.unsigned?y:d;if(this.unsigned){if(t.unsigned||(t=t.toUnsigned()),t.gt(this))return y;if(t.gt(this.shru(1)))return w;o=y}else{if(this.eq(E))return t.eq(x)||t.eq(b)?E:t.eq(E)?x:(n=this.shr(1).div(t).shl(1)).eq(d)?t.isNegative()?x:b:(i=this.sub(t.mul(n)),o=n.add(i.div(t)));if(t.eq(E))return this.unsigned?y:d;if(this.isNegative())return t.isNegative()?this.neg().div(t.neg()):this.neg().div(t).neg();if(t.isNegative())return this.div(t.neg()).neg();o=d}for(i=this;i.gte(t);){n=Math.max(1,Math.floor(i.toNumber()/t.toNumber()));for(var u=Math.ceil(Math.log(n)/Math.LN2),f=u<=48?1:h(2,u-48),l=s(n),g=l.mul(t);g.isNegative()||g.gt(i);)g=(l=s(n-=f,this.unsigned)).mul(t);l.isZero()&&(l=x),o=o.add(l),i=i.sub(g)}return o},O.div=O.divide,O.modulo=function(t){return e(t)||(t=c(t)),r?a((this.unsigned?r.rem_u:r.rem_s)(this.low,this.high,t.low,t.high),r.get_high(),this.unsigned):this.sub(this.div(t).mul(t))},O.mod=O.modulo,O.rem=O.modulo,O.not=function(){return a(~this.low,~this.high,this.unsigned)},O.and=function(t){return e(t)||(t=c(t)),a(this.low&t.low,this.high&t.high,this.unsigned)},O.or=function(t){return e(t)||(t=c(t)),a(this.low|t.low,this.high|t.high,this.unsigned)},O.xor=function(t){return e(t)||(t=c(t)),a(this.low^t.low,this.high^t.high,this.unsigned)},O.shiftLeft=function(t){return e(t)&&(t=t.toInt()),0==(t&=63)?this:t<32?a(this.low<<t,this.high<<t|this.low>>>32-t,this.unsigned):a(0,this.low<<t-32,this.unsigned)},O.shl=O.shiftLeft,O.shiftRight=function(t){return e(t)&&(t=t.toInt()),0==(t&=63)?this:t<32?a(this.low>>>t|this.high<<32-t,this.high>>t,this.unsigned):a(this.high>>t-32,this.high>=0?0:-1,this.unsigned)},O.shr=O.shiftRight,O.shiftRightUnsigned=function(t){if(e(t)&&(t=t.toInt()),0===(t&=63))return this;var r=this.high;return t<32?a(this.low>>>t|r<<32-t,r>>>t,this.unsigned):a(32===t?r:r>>>t-32,0,this.unsigned)},O.shru=O.shiftRightUnsigned,O.shr_u=O.shiftRightUnsigned,O.toSigned=function(){return this.unsigned?a(this.low,this.high,!1):this},O.toUnsigned=function(){return this.unsigned?this:a(this.low,this.high,!0)},O.toBytes=function(t){return t?this.toBytesLE():this.toBytesBE()},O.toBytesLE=function(){var t=this.high,r=this.low;return[255&r,r>>>8&255,r>>>16&255,r>>>24,255&t,t>>>8&255,t>>>16&255,t>>>24]},O.toBytesBE=function(){var t=this.high,r=this.low;return[t>>>24,t>>>16&255,t>>>8&255,255&t,r>>>24,r>>>16&255,r>>>8&255,255&r]},n.fromBytes=function(t,r,e){return e?n.fromBytesLE(t,r):n.fromBytesBE(t,r)},n.fromBytesLE=function(t,r){return new n(t[0]|t[1]<<8|t[2]<<16|t[3]<<24,t[4]|t[5]<<8|t[6]<<16|t[7]<<24,r)},n.fromBytesBE=function(t,r){return new n(t[4]<<24|t[5]<<16|t[6]<<8|t[7],t[0]<<24|t[1]<<16|t[2]<<8|t[3],r)}},8823:(t,r,n)=>{var e=n(2074);t.exports=!e(function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})},9067:function(t,r,n){var e;!function(t,i){function o(t){var r=this;r.next=function(){var t,n,e=r.x,i=r.i;return t=e[i],n=(t^=t>>>7)^t<<24,n^=(t=e[i+1&7])^t>>>10,n^=(t=e[i+3&7])^t>>>3,n^=(t=e[i+4&7])^t<<7,t=e[i+7&7],n^=(t^=t<<13)^t<<9,e[i]=n,r.i=i+1&7,n},function(t,r){var n,e=[];if(r===(0|r))e[0]=r;else for(r=""+r,n=0;n<r.length;++n)e[7&n]=e[7&n]<<15^r.charCodeAt(n)+e[n+1&7]<<13;for(;e.length<8;)e.push(0);for(n=0;n<8&&0===e[n];++n);for(8==n?e[7]=-1:e[n],t.x=e,t.i=0,n=256;n>0;--n)t.next()}(r,t)}function u(t,r){return r.x=t.x.slice(),r.i=t.i,r}function s(t,r){null==t&&(t=+new Date);var n=new o(t),e=r&&r.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var t=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},i.int32=n.next,i.quick=i,e&&(e.x&&u(e,n),i.state=function(){return u(n,{})}),i}i&&i.exports?i.exports=s:n.amdD&&n.amdO?void 0===(e=function(){return s}.call(r,n,r,i))||(i.exports=e):this.xorshift7=s}(0,t=n.nmd(t),n.amdD)},9206:(t,r,n)=>{var e,i,o,u=n(8369),s=n(200),a=n(5335),h=n(7712),f=n(6490),c=n(9310),l=n(5904),g=n(7708),p="Object already initialized",v=s.TypeError,d=s.WeakMap;if(u||c.state){var y=c.state||(c.state=new d);y.get=y.get,y.has=y.has,y.set=y.set,e=function(t,r){if(y.has(t))throw v(p);return r.facade=t,y.set(t,r),r},i=function(t){return y.get(t)||{}},o=function(t){return y.has(t)}}else{var x=l("state");g[x]=!0,e=function(t,r){if(f(t,x))throw v(p);return r.facade=t,h(t,x,r),r},i=function(t){return f(t,x)?t[x]:{}},o=function(t){return f(t,x)}}t.exports={set:e,get:i,has:o,enforce:function(t){return o(t)?i(t):e(t,{})},getterFor:function(t){return function(r){var n;if(!a(r)||(n=i(r)).type!==t)throw v("Incompatible receiver, "+t+" required");return n}}}},9310:(t,r,n)=>{var e=n(200),i=n(9430),o="__core-js_shared__",u=e[o]||i(o,{});t.exports=u},9328:(t,r,n)=>{var e=n(9830);t.exports=function(t){var r=+t;return r!=r||0===r?0:e(r)}},9430:(t,r,n)=>{var e=n(200),i=Object.defineProperty;t.exports=function(t,r){try{i(e,t,{value:r,configurable:!0,writable:!0})}catch(n){e[t]=r}return r}},9686:(t,r,n)=>{var e=n(1385),i=n(3938),o=n(7473);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,r=!1,n={};try{(t=e(Object.prototype,"__proto__","set"))(n,[]),r=n instanceof Array}catch(u){}return function(n,e){return i(n),o(e),r?t(n,e):n.__proto__=e,n}}():void 0)},9751:(t,r,n)=>{var e=n(2368),i=n(8420),o=n(5335),u=TypeError;t.exports=function(t,r){var n,s;if("string"===r&&i(n=t.toString)&&!o(s=e(n,t)))return s;if(i(n=t.valueOf)&&!o(s=e(n,t)))return s;if("string"!==r&&i(n=t.toString)&&!o(s=e(n,t)))return s;throw u("Can't convert object to primitive value")}},9830:t=>{var r=Math.ceil,n=Math.floor;t.exports=Math.trunc||function(t){var e=+t;return(e>0?n:r)(e)}},9965:(t,r,n)=>{var e=n(281),i=n(8420),o=n(9310),u=e(Function.toString);i(o.inspectSource)||(o.inspectSource=function(t){return u(t)}),t.exports=o.inspectSource}}]);
//# sourceMappingURL=vendor.js.map