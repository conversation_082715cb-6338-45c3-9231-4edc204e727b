{"version": 3, "file": "content.js", "mappings": "UAAIA,E,8DCgIJ,MAAMC,EAAW,IA5HjB,MACEC,WAAAA,GACEC,KAAKC,OAAS,IAAIC,IAClBF,KAAKG,aAAe,GACtB,CAQAC,EAAAA,CAAGC,EAAOC,EAAUC,EAAU,CAAC,GACxBP,KAAKC,OAAOO,IAAIH,IACnBL,KAAKC,OAAOQ,IAAIJ,EAAO,IAGzB,MAAMK,EAAYV,KAAKC,OAAOU,IAAIN,GAGlC,GAAIK,EAAUE,QAAUZ,KAAKG,aAE3B,OAGF,MAAMU,EAAW,CACfP,WACAQ,KAAMP,EAAQO,OAAQ,EACtBC,SAAUR,EAAQQ,UAAY,GAGhCL,EAAUM,KAAKH,GAGfH,EAAUO,KAAK,CAACC,EAAGC,IAAMA,EAAEJ,SAAWG,EAAEH,SAC1C,CAOAD,IAAAA,CAAKT,EAAOC,GACVN,KAAKI,GAAGC,EAAOC,EAAU,CAAEQ,MAAM,GACnC,CAOAM,GAAAA,CAAIf,EAAOC,GACT,IAAKN,KAAKC,OAAOO,IAAIH,GACnB,OAGF,MAAMK,EAAYV,KAAKC,OAAOU,IAAIN,GAC5BgB,EAAQX,EAAUY,UAAUT,GAAYA,EAASP,WAAaA,IAErD,IAAXe,GACFX,EAAUa,OAAOF,EAAO,GAID,IAArBX,EAAUE,QACZZ,KAAKC,OAAOuB,OAAOnB,EAEvB,CAOAoB,IAAAA,CAAKpB,EAAOqB,GACV,IAAK1B,KAAKC,OAAOO,IAAIH,GACnB,OAGF,MAAMK,EAAYV,KAAKC,OAAOU,IAAIN,GAC5BsB,EAAW,GAGjB,IAAK,IAAIC,EAAI,EAAGA,EAAIlB,EAAUE,OAAQgB,IAAK,CACzC,MAAMf,EAAWH,EAAUkB,GAE3B,IACEf,EAASP,SAASoB,GAGdb,EAASC,MACXa,EAASX,KAAKY,EAElB,CAAE,MAAOC,GAET,CACF,CAGA,IAAK,IAAID,EAAID,EAASf,OAAS,EAAGgB,GAAK,EAAGA,IACxClB,EAAUa,OAAOI,EAASC,GAAI,EAElC,CAKAE,KAAAA,GACE9B,KAAKC,OAAO6B,OACd,CAKAC,QAAAA,GACE,MAAMC,EAAQ,CAAC,EACf,IAAK,MAAO3B,EAAOK,KAAcV,KAAKC,OACpC+B,EAAM3B,GAASK,EAAUE,OAE3B,OAAOoB,CACT,GAOWC,EAEQ,oBAFRA,EAGI,gBAHJA,EAII,gBAJJA,EAKW,uBALXA,EAME,cANFA,EASM,kBATNA,EAUK,iBAVLA,EAWe,2BAXfA,EAYU,sBAZVA,EAaQ,oBAbRA,EAmBa,yBAnBbA,EAsBG,eAtBHA,EAuBG,eAIhB,IC1JO,MAAMC,EACXnC,WAAAA,CAAYoC,EAAS,CAAC,GACpBnC,KAAKmC,OAAS,CACZC,WAAY,KACZC,YAAa,KACbC,UAAW,IACXC,QAAS,GACTC,QAAS,KACTC,QAAS,IACTC,QAAS,QACNP,GAGLnC,KAAK2C,cAAgB,KACrB3C,KAAK4C,eAAgB,CACvB,CAKA,gBAAMC,GACJ,IAEE7C,KAAK2C,cAAgB3C,KAAK8C,sBAC1B9C,KAAK4C,eAAgB,CAEvB,CAAE,MAAOf,GAEP,MAAMA,CACR,CACF,CAOA,aAAMkB,CAAQC,GACZ,IAAKhD,KAAK4C,cACR,MAAM,IAAIK,MAAM,oCAGlB,IACE,MAAMC,EAAW,CAAC,EAGZC,EAAOnD,KAAKoD,YAAYJ,EAAUK,UACxCH,EAASC,KAAOA,EAGhB,MAAMG,EAAgBtD,KAAKuD,qBAAqBJ,GAChDD,EAASI,cAAgBA,EAGzB,MAAME,EAAiBxD,KAAKyD,sBAAsBH,GAClDJ,EAASM,eAAiBA,EAG1B,MAAME,EAAoB1D,KAAK2D,yBAAyBH,GACxDN,EAASQ,kBAAoBA,EAG7B,MAAME,EAAO5D,KAAK6D,YAAYH,GAQ9B,OAPAR,EAASU,KAAOA,EAGhBV,EAASY,iBAAmB9D,KAAK+D,wBAAwBT,GACzDJ,EAASc,gBAAkBhE,KAAKiE,uBAAuBX,GACvDJ,EAASgB,iBAAmBlE,KAAKmE,wBAAwBnB,EAAUK,UAE5DH,CACT,CAAE,MAAOrB,GAEP,MAAMA,CACR,CACF,CAOAuB,WAAAA,CAAYC,GACV,MAAMhB,EAAcrC,KAAKmC,OAAOE,YAC1BC,EAAYtC,KAAKmC,OAAOG,UACxB8B,EAAYC,KAAKC,OAAOjB,EAASzC,OAASyB,GAAeC,GAAa,EAEtEa,EAAO,GAEb,IAAK,IAAIoB,EAAQ,EAAGA,EAAQH,EAAWG,IAAS,CAC9C,MAAMC,EAAQD,EAAQjC,EAIhBmC,GAHMJ,KAAKK,IAAIF,EAAQnC,EAAagB,EAASzC,QAGjC,IAAI+D,aAAatC,IACnC,IAAK,IAAIT,EAAI,EAAGA,EAAIS,EAAaT,IAC/B6C,EAAU7C,GAAM4C,EAAQ5C,EAAIyB,EAASzC,OAAUyC,EAASmB,EAAQ5C,GAAK,EAIvE,MAAMgD,EAAW5E,KAAK6E,gBAAgBJ,GAGhCK,EAAY9E,KAAK+E,WAAWH,GAClCzB,EAAKnC,KAAK8D,EACZ,CAEA,OAAO3B,CACT,CAOA0B,eAAAA,CAAgBnD,GACd,MAAMkD,EAAW,IAAID,aAAajD,EAAKd,QACvC,IAAK,IAAIgB,EAAI,EAAGA,EAAIF,EAAKd,OAAQgB,IAAK,CACpC,MAAMoD,EAAS,IAAO,EAAIX,KAAKY,IAAI,EAAIZ,KAAKa,GAAKtD,GAAKF,EAAKd,OAAS,KACpEgE,EAAShD,GAAKF,EAAKE,GAAKoD,CAC1B,CACA,OAAOJ,CACT,CAOAG,UAAAA,CAAWrD,GAGT,MAAMyD,EAAIzD,EAAKd,OACTwE,EAAO,IAAIT,aAAaQ,GACxBE,EAAO,IAAIV,aAAaQ,GAG9B,IAAK,IAAIG,EAAI,EAAGA,EAAIH,EAAI,EAAGG,IAAK,CAC9B,IAAIC,EAAU,EACVC,EAAU,EAEd,IAAK,IAAIC,EAAI,EAAGA,EAAIN,EAAGM,IAAK,CAC1B,MAAMC,GAAS,EAAIrB,KAAKa,GAAKI,EAAIG,EAAIN,EACrCI,GAAW7D,EAAK+D,GAAKpB,KAAKY,IAAIS,GAC9BF,GAAW9D,EAAK+D,GAAKpB,KAAKsB,IAAID,EAChC,CAEAN,EAAKE,GAAKC,EACVF,EAAKC,GAAKE,CACZ,CAEA,MAAO,CAAEJ,OAAMC,OACjB,CAOA9B,oBAAAA,CAAqBJ,GACnB,OAAOA,EAAKyC,IAAIrB,IACd,MAAMsB,EAAQ,IAAIlB,aAAaJ,EAAMa,KAAKxE,QAC1C,IAAK,IAAIgB,EAAI,EAAGA,EAAI2C,EAAMa,KAAKxE,OAAQgB,IACrCiE,EAAMjE,GAAK2C,EAAMa,KAAKxD,GAAK2C,EAAMa,KAAKxD,GAAK2C,EAAMc,KAAKzD,GAAK2C,EAAMc,KAAKzD,GAExE,OAAOiE,GAEX,CAMA/C,mBAAAA,GACE,MAAMP,EAAUvC,KAAKmC,OAAOI,QACtBuD,EAAU9F,KAAKmC,OAAOK,QAAU,EAAI,EACpCJ,EAAapC,KAAKmC,OAAOC,WACzBK,EAAUzC,KAAKmC,OAAOM,QACtBC,EAAU1C,KAAKmC,OAAOO,QAGtBqD,EAAS/F,KAAKgG,QAAQvD,GACtBwD,EAASjG,KAAKgG,QAAQtD,GAGtBwD,EAAY,GAClB,IAAK,IAAItE,EAAI,EAAGA,GAAKW,EAAU,EAAGX,IAChCsE,EAAUlF,KAAK+E,GAAUE,EAASF,GAAUnE,GAAKW,EAAU,IAI7D,MAGM4D,EAHWD,EAAUN,IAAIQ,GAAOpG,KAAKqG,QAAQD,IAGxBR,IAAIU,GAAMjC,KAAKC,MAAMgC,EAAKtG,KAAKmC,OAAOK,QAAUJ,IAGrEmE,EAAa,GACnB,IAAK,IAAI3E,EAAI,EAAGA,EAAIW,EAASX,IAAK,CAChC,MAAM4E,EAAS,IAAI7B,aAAamB,GAC1BW,EAAON,EAAUvE,GACjB8E,EAASP,EAAUvE,EAAI,GACvB+E,EAAQR,EAAUvE,EAAI,GAG5B,IAAK,IAAIgF,EAAIH,EAAMG,EAAIF,EAAQE,IACzBF,EAASD,IACXD,EAAOI,IAAMA,EAAIH,IAASC,EAASD,IAIvC,IAAK,IAAIG,EAAIF,EAAQE,EAAID,EAAOC,IAC1BD,EAAQD,IACVF,EAAOI,IAAMD,EAAQC,IAAMD,EAAQD,IAIvCH,EAAWvF,KAAKwF,EAClB,CAEA,OAAOD,CACT,CAOAP,OAAAA,CAAQM,GACN,OAAO,KAAOjC,KAAKwC,MAAM,EAAIP,EAAK,IACpC,CAOAD,OAAAA,CAAQD,GACN,OAAO,KAAO/B,KAAKyC,IAAI,GAAIV,EAAM,MAAQ,EAC3C,CAOA3C,qBAAAA,CAAsBH,GACpB,OAAOA,EAAcsC,IAAIrB,IACvB,MAAMwC,EAAW,IAAIpC,aAAa3E,KAAKmC,OAAOI,SAE9C,IAAK,IAAIX,EAAI,EAAGA,EAAI5B,KAAKmC,OAAOI,QAASX,IAAK,CAC5C,IAAIoF,EAAM,EACV,MAAMR,EAASxG,KAAK2C,cAAcf,GAElC,IAAK,IAAIgF,EAAI,EAAGA,EAAIvC,KAAKK,IAAIH,EAAM3D,OAAQ4F,EAAO5F,QAASgG,IACzDI,GAAOzC,EAAMqC,GAAKJ,EAAOI,GAG3BG,EAASnF,GAAKoF,CAChB,CAEA,OAAOD,GAEX,CAOApD,wBAAAA,CAAyBH,GACvB,OAAOA,EAAeoC,IAAIrB,IACxB,MAAM0C,EAAW,IAAItC,aAAaJ,EAAM3D,QACxC,IAAK,IAAIgB,EAAI,EAAGA,EAAI2C,EAAM3D,OAAQgB,IAEhCqF,EAASrF,GAAKyC,KAAK6C,IAAI7C,KAAK8C,IAAI5C,EAAM3C,GAAI,QAE5C,OAAOqF,GAEX,CAOApD,WAAAA,CAAYH,GAGV,OAAOA,EAAkBkC,IAAIrB,IAC3B,MAAMX,EAAO,IAAIe,aAHD,IAMhB,IAAK,IAAI/C,EAAI,EAAGA,EANA,GAMeA,IAAK,CAClC,IAAIoF,EAAM,EACV,IAAK,IAAIJ,EAAI,EAAGA,EAAIrC,EAAM3D,OAAQgG,IAChCI,GAAOzC,EAAMqC,GAAKvC,KAAKY,IAAIZ,KAAKa,GAAKtD,GAAKgF,EAAI,IAAOrC,EAAM3D,QAE7DgD,EAAKhC,GAAKoF,CACZ,CAEA,OAAOpD,GAEX,CAOAG,uBAAAA,CAAwBT,GACtB,OAAOA,EAAcsC,IAAIrB,IACvB,IAAI6C,EAAc,EACdC,EAAa,EAEjB,IAAK,IAAIzF,EAAI,EAAGA,EAAI2C,EAAM3D,OAAQgB,IAAK,CAErCwF,GADaxF,EAAI5B,KAAKmC,OAAOC,YAAc,EAAImC,EAAM3D,QAC/B2D,EAAM3C,GAC5ByF,GAAc9C,EAAM3C,EACtB,CAEA,OAAOyF,EAAa,EAAID,EAAcC,EAAa,GAEvD,CAOApD,sBAAAA,CAAuBX,EAAegE,EAAiB,KACrD,OAAOhE,EAAcsC,IAAIrB,IACvB,MACMgD,EADahD,EAAMiD,OAAO,CAACR,EAAKS,IAAQT,EAAMS,EAAK,GAC1BH,EAE/B,IAAII,EAAkB,EACtB,IAAK,IAAI9F,EAAI,EAAGA,EAAI2C,EAAM3D,OAAQgB,IAEhC,GADA8F,GAAmBnD,EAAM3C,GACrB8F,GAAmBH,EACrB,OAAO3F,EAAI5B,KAAKmC,OAAOC,YAAc,EAAImC,EAAM3D,QAInD,OAAOZ,KAAKmC,OAAOC,WAAa,GAEpC,CAOA+B,uBAAAA,CAAwBd,GACtB,IAAIsE,EAAY,EAChB,IAAK,IAAI/F,EAAI,EAAGA,EAAIyB,EAASzC,OAAQgB,IAC9ByB,EAASzB,IAAM,GAAQyB,EAASzB,EAAI,IAAM,GAC7C+F,IAGJ,OAAOA,GAAatE,EAASzC,OAAS,EACxC,CAMAgH,SAAAA,GACE,MAAO,CACLhF,cAAe5C,KAAK4C,cACpBT,OAAQnC,KAAKmC,OACb0F,kBAAmB7H,KAAK2C,cAAgB3C,KAAK2C,cAAc/B,OAAS,EAExE,CAKAkH,OAAAA,GACE9H,KAAK2C,cAAgB,KACrB3C,KAAK4C,eAAgB,CAEvB,EC3XK,MAAMmF,EACXhI,WAAAA,CAAYiI,EAAc7F,EAAS,CAAC,GAClCnC,KAAKgI,aAAeA,EACpBhI,KAAKmC,OAAS,CACZC,WAAY,KACZC,YAAa,KACbC,UAAW,IACX2F,WAAY,OACZ1F,QAAS,GACTC,QAAS,QACNL,GAGLnC,KAAKkI,iBAAmB,IAAIhG,EAAiBlC,KAAKmC,QAClDnC,KAAKmI,YAAc,GACnBnI,KAAK4C,eAAgB,CACvB,CAKA,gBAAMC,GACJ,UACQ7C,KAAKkI,iBAAiBrF,aAC5B7C,KAAK4C,eAAgB,CAEvB,CAAE,MAAOf,GAEP,MAAMA,CACR,CACF,CAOA,aAAMuG,CAAQpF,GACZ,IAAKhD,KAAK4C,cACR,MAAM,IAAIK,MAAM,kCAGlB,IAEE,MAAMoF,EAAerI,KAAKsI,WAAWtF,GAG/BE,QAAiBlD,KAAKkI,iBAAiBnF,QAAQsF,GAKrD,MAAO,CACLE,IAAKvF,EACLqF,eACAnF,SALoBlD,KAAKwI,YAAYtF,GAMrCuF,UAAWC,KAAKC,MAEpB,CAAE,MAAO9G,GAEP,MAAMA,CACR,CACF,CAOAyG,UAAAA,CAAWtF,GAET,MAAMK,EAAWrD,KAAK4I,kBAAkB5F,GAGlC6F,EAAY7I,KAAK8I,SAASzF,EAAUL,EAAUZ,WAAYpC,KAAKmC,OAAOC,YAGtE2G,EAAa/I,KAAKgJ,UAAUH,GAG5BjE,EAAW5E,KAAKiJ,YAAYF,GAKlC,MAAO,CACL1F,SAHoBrD,KAAKkJ,YAAYtE,GAIrCxC,WAAYpC,KAAKmC,OAAOC,WACxBC,YAAarC,KAAKmC,OAAOE,YACzBC,UAAWtC,KAAKmC,OAAOG,UAE3B,CAOAsG,iBAAAA,CAAkB5F,GAEhB,GAAIA,EAAUmG,WAAY,CAExB,MAAM9F,EAAW,IAAIsB,aAAa3B,EAAUmG,WAAWvI,QACvD,IAAK,IAAIgB,EAAI,EAAGA,EAAIoB,EAAUmG,WAAWvI,OAAQgB,IAC/CyB,EAASzB,IAAMoB,EAAUmG,WAAWvH,GAAK,KAAO,IAElD,OAAOyB,CACT,CAIA,MAAM+F,EAAWpG,EAAUqG,UACrBhG,EAAW,IAAIsB,aAAayE,EAASxI,QAG3C,IAAK,IAAIgB,EAAI,EAAGA,EAAIwH,EAASxI,OAAQgB,IACnCyB,EAASzB,IAAMwH,EAASxH,GAAK,KAAO,IAGtC,OAAOyB,CACT,CASAyF,QAAAA,CAASpH,EAAM4H,EAAWC,GACxB,GAAID,IAAcC,EAChB,OAAO7H,EAGT,MAAM8H,EAAQF,EAAYC,EACpBE,EAAepF,KAAKC,MAAM5C,EAAKd,OAAS4I,GACxCE,EAAS,IAAI/E,aAAa8E,GAGhC,IAAK,IAAI7H,EAAI,EAAGA,EAAI6H,EAAc7H,IAAK,CACrC,MAAM+H,EAAW/H,EAAI4H,EACfI,EAAgBvF,KAAKC,MAAMqF,GAC3BE,EAAexF,KAAKK,IAAIkF,EAAgB,EAAGlI,EAAKd,OAAS,GACzDkJ,EAAWH,EAAWC,EAE5BF,EAAO9H,GAAKF,EAAKkI,IAAkB,EAAIE,GAAYpI,EAAKmI,GAAgBC,CAC1E,CAEA,OAAOJ,CACT,CAOAV,SAAAA,CAAUtH,GAER,IAAIqI,EAAM,EACV,IAAK,IAAInI,EAAI,EAAGA,EAAIF,EAAKd,OAAQgB,IAC/BmI,GAAOrI,EAAKE,GAAKF,EAAKE,GAKxB,GAHAmI,EAAM1F,KAAK2F,KAAKD,EAAMrI,EAAKd,QAGvBmJ,EAAM,KACR,OAAOrI,EAIT,MAAMqH,EAAa,IAAIpE,aAAajD,EAAKd,QACnCqJ,EAAQ,IAAOF,EAErB,IAAK,IAAInI,EAAI,EAAGA,EAAIF,EAAKd,OAAQgB,IAC/BmH,EAAWnH,GAAKyC,KAAK8C,KAAK,EAAG9C,KAAKK,IAAI,EAAGhD,EAAKE,GAAKqI,IAGrD,OAAOlB,CACT,CAOAE,WAAAA,CAAYvH,GACV,MAAMkD,EAAW,IAAID,aAAajD,EAAKd,QAEvC,OAAQZ,KAAKmC,OAAO8F,YAClB,IAAK,OACH,IAAK,IAAIrG,EAAI,EAAGA,EAAIF,EAAKd,OAAQgB,IAAK,CACpC,MAAMoD,EAAS,IAAO,EAAIX,KAAKY,IAAI,EAAIZ,KAAKa,GAAKtD,GAAKF,EAAKd,OAAS,KACpEgE,EAAShD,GAAKF,EAAKE,GAAKoD,CAC1B,CACA,MACF,IAAK,UACH,IAAK,IAAIpD,EAAI,EAAGA,EAAIF,EAAKd,OAAQgB,IAAK,CACpC,MAAMoD,EAAS,IAAO,IAAOX,KAAKY,IAAI,EAAIZ,KAAKa,GAAKtD,GAAKF,EAAKd,OAAS,IACvEgE,EAAShD,GAAKF,EAAKE,GAAKoD,CAC1B,CACA,MACF,QAEE,OAAOtD,EAGX,OAAOkD,CACT,CAOAsE,WAAAA,CAAYxH,EAAMwI,EAAQ,KACxB,MAAMR,EAAS,IAAI/E,aAAajD,EAAKd,QACrC8I,EAAO,GAAKhI,EAAK,GAEjB,IAAK,IAAIE,EAAI,EAAGA,EAAIF,EAAKd,OAAQgB,IAC/B8H,EAAO9H,GAAKF,EAAKE,GAAKsI,EAAQxI,EAAKE,EAAI,GAGzC,OAAO8H,CACT,CAOAlB,WAAAA,CAAYtF,GAEV,MAAM6F,EAAa/I,KAAKmK,kBAAkBjH,GAGpCkH,EAAWpK,KAAKqK,eAAetB,GAKrC,OAFoB/I,KAAKsK,WAAWF,EAGtC,CAOAD,iBAAAA,CAAkBjH,GAChB,MAAM6F,EAAa,IAAK7F,GAGxB,GAAIA,EAASM,eAAgB,CAC3B,MAAM4C,EAAMlD,EAASM,eACf+G,EAAOnE,EAAIoB,OAAO,CAACR,EAAKS,IAAQT,EAAMS,EAAK,GAAKrB,EAAIxF,OACpD4J,EAAWpE,EAAIoB,OAAO,CAACR,EAAKS,IAAQT,GAAOS,EAAM8C,IAAS,EAAG,GAAKnE,EAAIxF,OACtE6J,EAAMpG,KAAK2F,KAAKQ,GAElBC,EAAM,OACR1B,EAAWvF,eAAiB4C,EAAIR,IAAI6B,IAAQA,EAAM8C,GAAQE,GAE9D,CAEA,OAAO1B,CACT,CAOAsB,cAAAA,CAAenH,GAEb,MAAMkH,EAAW,IAAKlH,GAEtB,GAAIA,EAASM,eAAgB,CAC3B,MAAMkH,EAAa,EACbtE,EAAMlD,EAASM,eACfmH,EAAc,IAAIhG,aAAayB,EAAIxF,QAEzC,IAAK,IAAIgB,EAAI,EAAGA,EAAIwE,EAAIxF,OAAQgB,IAAK,CACnC,IAAIoF,EAAM,EACN4D,EAAQ,EAEZ,IAAK,IAAIhE,EAAIvC,KAAK8C,IAAI,EAAGvF,EAAI8I,GAAa9D,GAAKvC,KAAKK,IAAI0B,EAAIxF,OAAS,EAAGgB,EAAI8I,GAAa9D,IACvFI,GAAOZ,EAAIQ,GACXgE,IAGFD,EAAY/I,GAAKoF,EAAM4D,CACzB,CAEAR,EAAS5G,eAAiBmH,CAC5B,CAEA,OAAOP,CACT,CAOAE,UAAAA,CAAWpH,GACT,MAAO,IACFA,EACH2H,QAAS,CACPzI,WAAYpC,KAAKmC,OAAOC,WACxBC,YAAarC,KAAKmC,OAAOE,YACzBC,UAAWtC,KAAKmC,OAAOG,UACvBmG,UAAWC,KAAKC,OAGtB,CAMAf,SAAAA,GACE,MAAO,CACLhF,cAAe5C,KAAK4C,cACpBT,OAAQnC,KAAKmC,OACb2I,WAAY9K,KAAKmI,YAAYvH,OAEjC,CAKAkH,OAAAA,GACM9H,KAAKkI,kBACPlI,KAAKkI,iBAAiBJ,UAGxB9H,KAAKmI,YAAc,GACnBnI,KAAK4C,eAAgB,CAGvB,ECtVK,MAAMmI,EACXhL,WAAAA,GACEC,KAAKgL,WAAY,EACjBhL,KAAKiL,UAAY,KAGjBjL,KAAKkL,QAAU,CAEbC,gBAAiB,CACfC,WAAY,EACZC,UAAW,EACXC,YAAa,EACbC,QAAS,EACTC,QAASC,KAIXC,YAAa,CACXN,WAAY,EACZC,UAAW,EACXC,YAAa,EACbC,QAAS,EACTC,QAASC,KAIXE,UAAW,CACTC,YAAa,EACbP,UAAW,EACXC,YAAa,EACbO,IAAK,EACLN,QAAS,EACTC,QAASC,KAIXK,OAAQ,CACNC,QAAS,EACTC,KAAM,EACNC,QAAS,EACTC,QAAS,IAIXC,IAAK,CACHJ,QAAS,EACTC,KAAM,EACNC,QAAS,EACTC,QAAS,KAKblM,KAAKmC,OAAS,CACZiK,eAAgB,IAChBC,WAAY,GACZC,uBAAuB,EACvBC,kBAAmB,CACjBC,oBAAqB,GACrBC,gBAAiB,IACjBC,cAAe,MACfC,YAAa,UACbC,SAAU,KAKd5M,KAAK6M,mBAAqB,KAC1B7M,KAAK8M,YAAc,IACrB,CAKAtI,KAAAA,GACMxE,KAAKgL,YAMThL,KAAKgL,WAAY,EACjBhL,KAAKiL,UAAY8B,YAAYpE,MAG7B3I,KAAK6M,mBAAqBG,YAAY,KACpChN,KAAKiN,iBACJjN,KAAKmC,OAAOiK,gBAGfpM,KAAKiN,gBACP,CAKAC,IAAAA,GACOlN,KAAKgL,YAMVhL,KAAKgL,WAAY,EAEbhL,KAAK6M,qBACPM,cAAcnN,KAAK6M,oBACnB7M,KAAK6M,mBAAqB,MAE9B,CAMAO,qBAAAA,CAAsBC,EAAiB,GACrCrN,KAAKsN,aAAa,kBAAmBD,GAEjCA,EAAiBrN,KAAKmC,OAAOoK,kBAAkBC,qBACjDxM,KAAKuN,YAAY,kBAAmBF,EAExC,CAMAG,iBAAAA,CAAkBC,EAAgB,GAChCzN,KAAKsN,aAAa,cAAeG,GAE7BA,EAAgBzN,KAAKmC,OAAOoK,kBAAkBE,iBAChDzM,KAAKuN,YAAY,cAAeE,EAEpC,CAMAC,eAAAA,CAAgBC,EAAa,GAC3B3N,KAAKsN,aAAa,YAAaK,GAG3BA,EAAa,IACf3N,KAAKkL,QAAQS,UAAUE,IAAM,IAAO8B,GAGlCA,EAAa3N,KAAKmC,OAAOoK,kBAAkBG,eAC7C1M,KAAKuN,YAAY,YAAaI,EAElC,CAOAL,YAAAA,CAAaM,EAAYC,GACvB,MAAMC,EAAS9N,KAAKkL,QAAQ0C,GACvBE,IAILA,EAAO1C,aACP0C,EAAOzC,WAAawC,EACpBC,EAAOxC,YAAcwC,EAAOzC,UAAYyC,EAAO1C,WAC/C0C,EAAOvC,QAAUlH,KAAK8C,IAAI2G,EAAOvC,QAASsC,GAC1CC,EAAOtC,QAAUnH,KAAKK,IAAIoJ,EAAOtC,QAASqC,GAEtC7N,KAAKmC,OAAOmK,sBAGlB,CAKAW,aAAAA,GAEEjN,KAAK+N,oBAGL/N,KAAKgO,iBAGLhO,KAAKiO,wBACP,CAKAF,iBAAAA,GACE,IAAIpB,EAAc,EAGdI,YAAYjB,OACda,EAAcI,YAAYjB,OAAOoC,eACxBC,UAAUC,eAEnBzB,EAAuC,KAAzBwB,UAAUC,aAAsB,KAAO,IAGvD,MAAMtC,EAAS9L,KAAKkL,QAAQY,OAC5BA,EAAOC,QAAUY,EACjBb,EAAOE,KAAO3H,KAAK8C,IAAI2E,EAAOE,KAAMW,GAGpCb,EAAOI,QAAQlL,KAAK,CAClByH,UAAWC,KAAKC,MAChBkF,MAAOlB,IAILb,EAAOI,QAAQtL,OAASZ,KAAKmC,OAAOkK,YACtCP,EAAOI,QAAQmC,QAIjBvC,EAAOG,QAAUH,EAAOI,QAAQ1E,OAAO,CAACR,EAAKsH,IAAWtH,EAAMsH,EAAOT,MAAO,GAAK/B,EAAOI,QAAQtL,MAClG,CAKAoN,cAAAA,GACE,MAAMrF,EAAMoE,YAAYpE,MAExB,GAAI3I,KAAK8M,YAAa,CAEpB,MAAMyB,EAAW5F,EAAM3I,KAAK8M,YACtB0B,EAAgD,GAA7BxO,KAAKmC,OAAOiK,eAC/BQ,EAAWvI,KAAK8C,IAAI,EAAG9C,KAAKK,IAAI,IAAyC,KAAnC,EAAI8J,EAAmBD,KAE7DpC,EAAMnM,KAAKkL,QAAQiB,IACzBA,EAAIJ,QAAUa,EACdT,EAAIH,KAAO3H,KAAK8C,IAAIgF,EAAIH,KAAMY,GAG9BT,EAAID,QAAQlL,KAAK,CACfyH,UAAWC,KAAKC,MAChBkF,MAAOjB,IAILT,EAAID,QAAQtL,OAASZ,KAAKmC,OAAOkK,YACnCF,EAAID,QAAQmC,QAIdlC,EAAIF,QAAUE,EAAID,QAAQ1E,OAAO,CAACR,EAAKsH,IAAWtH,EAAMsH,EAAOT,MAAO,GAAK1B,EAAID,QAAQtL,MACzF,CAEAZ,KAAK8M,YAAcnE,CACrB,CAKAsF,sBAAAA,GACE,MAAMQ,EAAazO,KAAKmC,OAAOoK,kBAG3BvM,KAAKkL,QAAQY,OAAOC,QAAU0C,EAAW9B,aAC3C3M,KAAKuN,YAAY,SAAUvN,KAAKkL,QAAQY,OAAOC,SAI7C/L,KAAKkL,QAAQiB,IAAIJ,QAAU0C,EAAW7B,UACxC5M,KAAKuN,YAAY,MAAOvN,KAAKkL,QAAQiB,IAAIJ,QAE7C,CAOAwB,WAAAA,CAAYmB,EAAMb,GAChB,MAAMc,EAAU,CACdD,OACAb,QACAtG,UAAWvH,KAAKmC,OAAOoK,kBAAkBmC,GACzCjG,UAAWC,KAAKC,OAMI,oBAAX3D,QAA0BA,OAAOlF,UAC1CkF,OAAOlF,SAAS2B,KAAK,yBAA0BkN,EAEnD,CAMA5M,QAAAA,GACE,MAAO,CACLiJ,UAAWhL,KAAKgL,UAChB4D,OAAQ5O,KAAKiL,UAAY8B,YAAYpE,MAAQ3I,KAAKiL,UAAY,EAC9DC,QAAS2D,KAAKC,MAAMD,KAAKE,UAAU/O,KAAKkL,UACxC8D,QAAShP,KAAKiP,aAElB,CAMAA,UAAAA,GACE,MAAO,CACL9D,gBAAiB,CACfG,YAAatL,KAAKkL,QAAQC,gBAAgBG,YAC1C4D,eAAgBlP,KAAKmP,kBAAkB,oBAEzCzD,YAAa,CACXJ,YAAatL,KAAKkL,QAAQQ,YAAYJ,YACtC4D,eAAgBlP,KAAKmP,kBAAkB,gBAEzCxD,UAAW,CACTE,IAAK7L,KAAKkL,QAAQS,UAAUE,IAC5BP,YAAatL,KAAKkL,QAAQS,UAAUL,aAEtCQ,OAAQ,CACNC,QAAS/L,KAAKoP,YAAYpP,KAAKkL,QAAQY,OAAOC,SAC9CC,KAAMhM,KAAKoP,YAAYpP,KAAKkL,QAAQY,OAAOE,MAC3CC,QAASjM,KAAKoP,YAAYpP,KAAKkL,QAAQY,OAAOG,UAEhDE,IAAK,CACHJ,QAAS,GAAG/L,KAAKkL,QAAQiB,IAAIJ,QAAQsD,QAAQ,MAC7CrD,KAAM,GAAGhM,KAAKkL,QAAQiB,IAAIH,KAAKqD,QAAQ,MACvCpD,QAAS,GAAGjM,KAAKkL,QAAQiB,IAAIF,QAAQoD,QAAQ,OAGnD,CAOAF,iBAAAA,CAAkBvB,GAChB,MAAME,EAAS9N,KAAKkL,QAAQ0C,GACtBgB,EAAS5O,KAAKiL,WAAa8B,YAAYpE,MAAQ3I,KAAKiL,WAAa,IAAO,EAC9E,OAAO6C,EAAO1C,WAAawD,CAC7B,CAOAQ,WAAAA,CAAYE,GACV,GAAc,IAAVA,EAAa,MAAO,MAExB,MAEM1N,EAAIyC,KAAKC,MAAMD,KAAK6C,IAAIoI,GAASjL,KAAK6C,IAFlC,OAIV,OAAOqI,YAAYD,EAAQjL,KAAKyC,IAJtB,KAI6BlF,IAAIyN,QAAQ,IAAM,IAH3C,CAAC,IAAK,KAAM,KAAM,MAGqCzN,EACvE,CAKA4N,KAAAA,GAEEC,OAAOC,KAAK1P,KAAKkL,SAASyE,QAAQC,IAChC,MAAM9B,EAAS9N,KAAKkL,QAAQ0E,QACFC,IAAtB/B,EAAO1C,aACT0C,EAAO1C,WAAa,EACpB0C,EAAOzC,UAAY,EACnByC,EAAOxC,YAAc,EACrBwC,EAAOvC,QAAU,EACjBuC,EAAOtC,QAAUC,KAEfqC,EAAO5B,UACT4B,EAAO5B,QAAU,MAIrBlM,KAAKiL,UAAY8B,YAAYpE,KAE/B,CAMAmH,YAAAA,CAAaC,GACX/P,KAAKmC,OAAS,IAAKnC,KAAKmC,UAAW4N,EACrC,CAKAjI,OAAAA,GACE9H,KAAKkN,MAEP,EC3YK,MAAM8C,EACXjQ,WAAAA,GACEC,KAAKgI,aAAe,KACpBhI,KAAKiQ,SAAW,KAChBjQ,KAAKkQ,WAAa,KAClBlQ,KAAKmQ,eAAiB,KACtBnQ,KAAK4C,eAAgB,EACrB5C,KAAKoQ,cAAe,EACpBpQ,KAAKqQ,mBAAqB,IAAItF,EAG9B/K,KAAKmC,OAAS,CACZC,WAAY,KACZI,QAAS,KACT8N,sBAAuB,GACvBC,mBAAoB,KAItBvQ,KAAKwQ,gBAAkBxQ,KAAKwQ,gBAAgBC,KAAKzQ,KACnD,CAMA,gBAAM6C,CAAW6N,GACf,IACE,GAAI1Q,KAAK4C,cAEP,OAMF5C,KAAKgI,aAAe,IAAKhD,OAAO2L,cAAgB3L,OAAO4L,oBAAoB,CACzExO,WAAYpC,KAAKmC,OAAOC,aAI1B,IACEpC,KAAKkQ,WAAalQ,KAAKgI,aAAa6I,yBAAyBH,EAC/D,CAAE,MAAO7O,GAEP,MAAM,IAAIoB,MAAM,gCAClB,CAGAjD,KAAKiQ,SAAWjQ,KAAKgI,aAAa8I,iBAClC9Q,KAAKiQ,SAASzN,QAAUxC,KAAKmC,OAAOK,QACpCxC,KAAKiQ,SAASK,sBAAwBtQ,KAAKmC,OAAOmO,sBAGlDtQ,KAAKmQ,eAAiB,IAAIpI,EAAe/H,KAAKgI,aAAchI,KAAKmC,cAC3DnC,KAAKmQ,eAAetN,aAG1B7C,KAAKkQ,WAAWa,QAAQ/Q,KAAKiQ,UAC7BjQ,KAAKiQ,SAASc,QAAQ/Q,KAAKgI,aAAagJ,aAGxChR,KAAKiR,yBAAyBP,GAE9B1Q,KAAK4C,eAAgB,EAErB9C,EAAS2B,KAAKQ,EAA0B,CACtCG,WAAYpC,KAAKgI,aAAa5F,WAC9B0I,WAAY9K,KAAKiQ,SAASiB,mBAK9B,CAAE,MAAOrP,GAGP,MADA/B,EAAS2B,KAAKQ,EAAoB,CAAEJ,MAAOA,EAAMsP,UAC3CtP,CACR,CACF,CAMAoP,wBAAAA,CAAyBP,GAEvBA,EAAaU,iBAAiB,OAAQ,KACJ,cAA5BpR,KAAKgI,aAAaqJ,OACpBrR,KAAKgI,aAAasJ,SAEpBtR,KAAKuR,oBAIPb,EAAaU,iBAAiB,QAAS,KACrCpR,KAAKwR,mBAIPd,EAAaU,iBAAiB,QAAS,KACrCpR,KAAKwR,kBAET,CAKAD,eAAAA,GACOvR,KAAK4C,gBAKN5C,KAAKoQ,eAOTpQ,KAAKoQ,cAAe,EACpBpQ,KAAKyR,iBAEL3R,EAAS2B,KAAKQ,IAChB,CAKAuP,cAAAA,GACOxR,KAAKoQ,eAMVpQ,KAAKoQ,cAAe,EAEpBtQ,EAAS2B,KAAKQ,GAChB,CAKA,oBAAMwP,GACJ,GAAKzR,KAAKoQ,aAIV,IACE,MAAMnF,EAAY8B,YAAYpE,MAGxB3F,EAAYhD,KAAK0R,eAGvB,GAAI1R,KAAK2R,cAAc3O,GAAY,CAEjC,MAAM4O,QAAsB5R,KAAKmQ,eAAe/H,QAAQpF,GAGxDlD,EAAS2B,KAAKQ,EAA6B,CACzCsG,IAAKvF,EACL6O,UAAWD,EACXnJ,UAAWC,KAAKC,OAEpB,CAGA,MAAM0E,EAAiBN,YAAYpE,MAAQsC,EAC3CjL,KAAKqQ,mBAAmByB,qBAAqBzE,GAG7C0E,WAAW,IAAM/R,KAAKyR,iBAAkBzR,KAAKmC,OAAOoO,mBAEtD,CAAE,MAAO1O,GAEP/B,EAAS2B,KAAKQ,EAAoB,CAAEJ,MAAOA,EAAMsP,UAGjDY,WAAW,IAAM/R,KAAKyR,iBAAmD,EAAjCzR,KAAKmC,OAAOoO,mBACtD,CACF,CAMAmB,YAAAA,GACE,MAAMM,EAAehS,KAAKiQ,SAASiB,kBAC7Be,EAAiB,IAAIC,WAAWF,GAChCG,EAAgB,IAAID,WAAWF,GAKrC,OAHAhS,KAAKiQ,SAASmC,sBAAsBH,GACpCjS,KAAKiQ,SAASoC,qBAAqBF,GAE5B,CACLhJ,WAAY8I,EACZ5I,UAAW8I,EACX/P,WAAYpC,KAAKgI,aAAa5F,WAC9B4P,eAEJ,CAOAL,aAAAA,CAAc3O,GACZ,MAAMN,EAAU2B,KAAK8C,OAAOnE,EAAUqG,WAChCiJ,EAAUtP,EAAUqG,UAAU7B,OAAO,CAACR,EAAKS,IAAQT,EAAMS,EAAK,GAAKzE,EAAUqG,UAAUzI,OAE7F,OAAO8B,EAAU,GAAK4P,EAAU,CAClC,CAMA9B,eAAAA,CAAgBxN,GAEdlD,EAAS2B,KAAKQ,EAA6Be,EAC7C,CAMAuP,mBAAAA,GACE,OAAKvS,KAAKgI,aAIH,CACLqJ,MAAOrR,KAAKgI,aAAaqJ,MACzBjP,WAAYpC,KAAKgI,aAAa5F,WAC9BoQ,YAAaxS,KAAKgI,aAAawK,YAC/BC,YAAazS,KAAKgI,aAAayK,aAAe,EAC9CC,cAAe1S,KAAKgI,aAAa0K,eAAiB,GAR3C,IAUX,CAMAC,mBAAAA,GACE,OAAO3S,KAAKqQ,mBAAmBtO,UACjC,CAMA+N,YAAAA,CAAaC,GACX/P,KAAKmC,OAAS,IAAKnC,KAAKmC,UAAW4N,GAE/B/P,KAAKiQ,UAAYF,EAAUvN,UAC7BxC,KAAKiQ,SAASzN,QAAUuN,EAAUvN,SAGhCxC,KAAKiQ,eAAgDJ,IAApCE,EAAUO,wBAC7BtQ,KAAKiQ,SAASK,sBAAwBP,EAAUO,sBAEpD,CAKAxI,OAAAA,GACE9H,KAAKwR,iBAEDxR,KAAKmQ,gBACPnQ,KAAKmQ,eAAerI,UAGlB9H,KAAKgI,cACPhI,KAAKgI,aAAa4K,QAGpB5S,KAAK4C,eAAgB,CAEvB,E,sBC7RK,MAAMiQ,EACX9S,WAAAA,GACEC,KAAK8S,MAAQ,KACb9S,KAAK+S,WAAa,KAClB/S,KAAK4C,eAAgB,EAGrB5C,KAAKmC,OAAS,CACZ6Q,SAAU,oDACVC,YAAa,2GACb7Q,WAAY,KACZ8Q,mBAAoB,IACpBC,gBAAiB,IACjBC,oBAAqB,GACrBC,KAAM,GAIRrT,KAAKsT,WAAa,CAChBlR,WAAY,KACZmR,gBAAiB,MACjBhR,QAAS,GACTiR,YAAa,IAIfxT,KAAKgC,MAAQ,CACXyR,iBAAkB,EAClBC,qBAAsB,EACtBC,kBAAmB,EAEvB,CAKA,gBAAM9Q,GACJ,UAIQ7C,KAAK4T,kBAGL5T,KAAK6T,qBAGL7T,KAAK8T,cAEX9T,KAAK4C,eAAgB,CAIvB,CAAE,MAAOf,GAEP,MAAMA,CACR,CACF,CAKA,eAAM+R,GACJ,IAIE5T,KAAK8S,YAAciB,EAAAA,eAAkB/T,KAAKmC,OAAO6Q,SAcnD,CAAE,MAAOnR,GAEP,MAAM,IAAIoB,MAAM,gCAAgCpB,EAAMsP,UACxD,CACF,CAKA,kBAAM0C,GACJ,IAGE,MAAMG,QAAiBC,MAAMjU,KAAKmC,OAAO8Q,aACzC,IAAKe,EAASE,GACZ,MAAM,IAAIjR,MAAM,QAAQ+Q,EAASG,WAAWH,EAASI,cAGvD,MAAMC,QAAgBL,EAASM,OAC/BtU,KAAK+S,WAAa/S,KAAKuU,cAAcF,EAIvC,CAAE,MAAOxS,GAGP7B,KAAK+S,WAAa/S,KAAKwU,oBAEzB,CACF,CAOAD,aAAAA,CAAcF,GACZ,MAAMI,EAAQJ,EAAQK,OAAOC,MAAM,MAC7B5B,EAAa,GAGnB,IAAK,IAAInR,EAAI,EAAGA,EAAI6S,EAAM7T,OAAQgB,IAAK,CACrC,MAAMgT,EAAOH,EAAM7S,GAAG8S,OACtB,GAAIE,EAAM,CAER,MAAMC,EAAQD,EAAKD,MAAM,KACzB,GAAIE,EAAMjU,QAAU,EAAG,CAErB,MAAMkU,EAAcD,EAAM,GAAGE,QAAQ,KAAM,IAAIL,OAC/C3B,EAAW/R,KAAK8T,EAClB,CACF,CACF,CAEA,OAAO/B,CACT,CAMAyB,kBAAAA,GAEE,MAAO,CACL,SAAU,QAAS,WAAY,WAAY,SAAU,YACrD,OAAQ,QAAS,QAAS,UAAW,QAAS,MAAO,MAAO,MAC5D,OAAQ,QAAS,OAAQ,OAAQ,UAAW,OAAQ,YACpD,UAAW,OAAQ,OAAQ,QAAS,QAAS,WAAY,aACzD,QAAS,YAAa,SAAU,kBAAmB,aAGvD,CAKA,iBAAMV,GACJ,IAIE,MAAMkB,EAAajB,EAAAA,MAAS,CAAC/T,KAAKsT,WAAWC,kBAGvCtI,EAAY8B,YAAYpE,MACxBsM,QAAoBjV,KAAK8S,MAAMoC,QAAQF,GAC1BjI,YAAYpE,MAG/BqM,EAAWlN,UACPqN,MAAMC,QAAQH,GAChBA,EAAYtF,QAAQ0F,GAAUA,EAAOvN,WAErCmN,EAAYnN,SAKhB,CAAE,MAAOjG,GAGT,CACF,CAOA,cAAMyT,CAASC,GACb,IAAKvV,KAAK4C,cACR,MAAM,IAAIK,MAAM,oCAGlB,MAAMgI,EAAY8B,YAAYpE,MAE9B,IAEE,MAAM6M,EAAcxV,KAAKyV,gBAAgBF,GAGnCN,QAAoBjV,KAAK8S,MAAMoC,QAAQM,GAGvCE,QAAgB1V,KAAK2V,uBAAuBV,GAGlDO,EAAY1N,UACRqN,MAAMC,QAAQH,GAChBA,EAAYtF,QAAQ0F,GAAUA,EAAOvN,WAErCmN,EAAYnN,UAId,MAAM2F,EAAgBV,YAAYpE,MAAQsC,EAG1C,OAFAjL,KAAK4V,YAAYnI,GAEV,CACLwH,YAAaS,EACbjI,gBACAhF,UAAWC,KAAKC,MAChBkN,UAAW,SAGf,CAAE,MAAOhU,GAEP,MAAMA,CACR,CACF,CAOA4T,eAAAA,CAAgBF,GAEd,GAAIA,EAAclS,SAChB,OAAOrD,KAAK8V,mBAAmBP,EAAclS,UAI/C,GAAIkS,EAAc/R,eAChB,OAAOxD,KAAK+V,yBAAyBR,EAAc/R,gBAIrD,GAAI+R,EAAclM,UAAW,CAC3B,MAAMhG,EAAWrD,KAAKgW,oBAAoBT,EAAclM,WACxD,OAAOrJ,KAAK8V,mBAAmBzS,EACjC,CAEA,MAAM,IAAIJ,MAAM,gDAClB,CAOA6S,kBAAAA,CAAmBzS,GAEjB,IAAIuO,EAEJ,GAAIvO,EAASzC,OAASZ,KAAKsT,WAAWC,gBAAiB,CAErD,MAAM/O,EAAQH,KAAKC,OAAOjB,EAASzC,OAASZ,KAAKsT,WAAWC,iBAAmB,GAC/E3B,EAAgBvO,EAAS4S,MAAMzR,EAAOA,EAAQxE,KAAKsT,WAAWC,gBAChE,MAAWlQ,EAASzC,OAASZ,KAAKsT,WAAWC,iBAE3C3B,EAAgB,IAAIjN,aAAa3E,KAAKsT,WAAWC,iBACjD3B,EAAcnR,IAAI4C,IAElBuO,EAAgBvO,EAIlB,OAAO0Q,EAAAA,SAAYnC,EACrB,CAOAmE,wBAAAA,CAAyBvS,GAKvB,MAAM0S,EAAW1S,EAAe2S,OAC1BC,EAAepW,KAAKsT,WAAWC,gBAE/B8C,EAAU,IAAI1R,aAAayR,GAC3B5M,EAAQ0M,EAAStV,OAASwV,EAEhC,IAAK,IAAIxU,EAAI,EAAGA,EAAIwU,EAAcxU,IAAK,CACrC,MAAM+H,EAAWtF,KAAKC,MAAM1C,EAAI4H,GAChC6M,EAAQzU,GAAKsU,EAASvM,IAAa,CACrC,CAEA,OAAOoK,EAAAA,SAAYsC,EACrB,CAOAL,mBAAAA,CAAoB7D,GAElB,MAAM9O,EAAW,IAAIsB,aAAawN,EAAcvR,QAEhD,IAAK,IAAIgB,EAAI,EAAGA,EAAIuQ,EAAcvR,OAAQgB,IAExCyB,EAASzB,IAAMuQ,EAAcvQ,GAAK,KAAO,IAG3C,OAAOyB,CACT,CAOA,4BAAMsS,CAAuBV,GAE3B,IAAIqB,EAIFA,EAFEnB,MAAMC,QAAQH,GAEDA,EAAY,GAEZA,EAIjB,MAAMsB,QAAmBD,EAAa5U,OAGhCgU,EAAU,GAChB,IAAK,IAAI9T,EAAI,EAAGA,EAAI2U,EAAW3V,QAAUgB,EAAI5B,KAAK+S,WAAWnS,OAAQgB,IAC/D2U,EAAW3U,IAAM5B,KAAKmC,OAAOiR,qBAC/BsC,EAAQ1U,KAAK,CACXwV,UAAWxW,KAAK+S,WAAWnR,IAAM,SAASA,IAC1C6U,WAAYF,EAAW3U,GACvB8U,WAAY9U,IAMlB,OAAO8T,EACJzU,KAAK,CAACC,EAAGC,IAAMA,EAAEsV,WAAavV,EAAEuV,YAChCR,MAAM,EAAGjW,KAAKmC,OAAOkR,KAC1B,CAMAuC,WAAAA,CAAYnI,GACVzN,KAAKgC,MAAMyR,mBACXzT,KAAKgC,MAAM2R,kBAAoBlG,EAI/BzN,KAAKgC,MAAM0R,qBACyB,GAAlC1T,KAAKgC,MAAM0R,qBAFC,GAEoCjG,CACpD,CAMAkJ,OAAAA,GACE,MAAO,CACL/T,cAAe5C,KAAK4C,cACpBiT,UAAW,SACXe,WAAY5W,KAAK+S,WAAa/S,KAAK+S,WAAWnS,OAAS,EACvDuB,OAAQnC,KAAKmC,OACbmR,WAAYtT,KAAKsT,WACjBtR,MAAOhC,KAAKgC,MAEhB,CAMA8N,YAAAA,CAAaC,GACX/P,KAAKmC,OAAS,IAAKnC,KAAKmC,UAAW4N,EACrC,CAMA8G,aAAAA,GACE,OAAO7W,KAAK+S,WAAa,IAAI/S,KAAK+S,YAAc,EAClD,CAMAhR,QAAAA,GACE,MAAO,IAAK/B,KAAKgC,MACnB,CAKA8F,OAAAA,GACM9H,KAAK8S,QACP9S,KAAK8S,MAAMhL,UACX9H,KAAK8S,MAAQ,MAGf9S,KAAK+S,WAAa,KAClB/S,KAAK4C,eAAgB,CAGvB,EC1aK,MAAMkU,EACX/W,WAAAA,GACEC,KAAK8S,MAAQ,KACb9S,KAAK+W,SAAW,KAChB/W,KAAK4C,eAAgB,EAGrB5C,KAAKmC,OAAS,CACZ6Q,SAAU,kGACVgE,cAAe,qGACf5U,WAAY,MACZI,QAAS,KACT4Q,oBAAqB,GACrBC,KAAM,EACN4D,sBAAsB,GAIxBjX,KAAKkX,eAAiB,CACpB,EAAK,MACL,EAAK,KACL,EAAK,KACL,GAAM,KACN,GAAM,OACN,GAAM,OACN,EAAK,KACL,EAAK,OACL,EAAK,OACL,EAAK,SAIPlX,KAAKgC,MAAQ,CACXmV,gBAAiB,EACjBzD,qBAAsB,EACtBC,kBAAmB,EACnByD,cAAe,CAAC,EAEpB,CAKA,gBAAMvU,GACJ,UAIQ7C,KAAK4T,kBAGL5T,KAAKqX,uBAGLrX,KAAK8T,cAEX9T,KAAK4C,eAAgB,CAIvB,CAAE,MAAOf,GAEP,MAAMA,CACR,CACF,CAKA,eAAM+R,GACJ,IAIE5T,KAAK8S,YAAciB,EAAAA,gBAAmB/T,KAAKmC,OAAO6Q,SAOpD,CAAE,MAAOnR,GAEP,MAAM,IAAIoB,MAAM,yCAAyCpB,EAAMsP,UACjE,CACF,CAKA,oBAAMkG,GACJ,IAGE,MAAMrD,QAAiBC,MAAMjU,KAAKmC,OAAO6U,eACzC,IAAKhD,EAASE,GACZ,MAAM,IAAIjR,MAAM,QAAQ+Q,EAASG,WAAWH,EAASI,cAGvD,MAAMkD,QAAiBtD,EAASuD,OAChCvX,KAAK+W,SAAWO,EAASE,OAASxX,KAAKyX,oBAIzC,CAAE,MAAO5V,GAGP7B,KAAK+W,SAAW/W,KAAKyX,oBAEvB,CACF,CAMAA,kBAAAA,GACE,MAAO,CACL,YAAa,YAAa,MAAO,KAAM,KAAM,OAAQ,OAAQ,QAC7D,KAAM,MAAO,OAAQ,KAAM,OAAQ,MAAO,MAAO,QAAS,OAC1D,OAAQ,MAAO,QAAS,QAAS,OAErC,CAKA,iBAAM3D,GACJ,IAIE,MAAM4D,EAAa1X,KAAK8S,MAAM6E,OAAO,GAAGC,MAClC5C,EAAajB,EAAAA,MAAS2D,EAAWzB,MAAM,IACvC4B,EAAe7C,EAAW8C,WAAW,GAGrC7M,EAAY8B,YAAYpE,MACxBsM,EAAcjV,KAAK8S,MAAMoC,QAAQ2C,GACpB9K,YAAYpE,MAG/BqM,EAAWlN,UACX+P,EAAa/P,UACbmN,EAAYnN,SAId,CAAE,MAAOjG,GAGT,CACF,CAOA,YAAMkW,CAAOxC,GACX,IAAKvV,KAAK4C,cACR,MAAM,IAAIK,MAAM,yCAGlB,MAAMgI,EAAY8B,YAAYpE,MAE9B,IAEE,MAAM6M,EAAcxV,KAAKyV,gBAAgBF,GAGnCN,EAAcjV,KAAK8S,MAAMoC,QAAQM,GAGjCE,QAAgB1V,KAAK2V,uBAAuBV,GAGlDO,EAAY1N,UACZmN,EAAYnN,UAGZ,MAAM2F,EAAgBV,YAAYpE,MAAQsC,EAG1C,OAFAjL,KAAK4V,YAAYnI,EAAeiI,GAEzB,CACLqB,SAAUrB,EACVjI,gBACAhF,UAAWC,KAAKC,MAChBkN,UAAW,kBAGf,CAAE,MAAOhU,GAEP,MAAMA,CACR,CACF,CAOA4T,eAAAA,CAAgBF,GAEd,GAAIA,EAAc/R,eAChB,OAAOxD,KAAKgY,sBAAsBzC,EAAc/R,gBAGlD,GAAI+R,EAAcpS,KAChB,OAAOnD,KAAKiY,eAAe1C,EAAcpS,MAG3C,GAAIoS,EAAclM,UAChB,OAAOrJ,KAAKkY,wBAAwB3C,EAAclM,WAGpD,MAAM,IAAIpG,MAAM,4DAClB,CAOA+U,qBAAAA,CAAsBxU,GAEpB,MAAMkU,EAAa1X,KAAK8S,MAAM6E,OAAO,GAAGC,OACjC,CAAEO,EAAQC,EAAOC,GAAYX,EAG9BY,EAAkB,IAAI3T,aAAawT,EAASC,EAAQC,GAG1D,IAAK,IAAIzW,EAAI,EAAGA,EAAIyC,KAAKK,IAAIlB,EAAe5C,OAAQuX,GAASvW,IAAK,CAChE,MAAM2C,EAAQf,EAAe5B,GAC7B,IAAK,IAAIgF,EAAI,EAAGA,EAAIvC,KAAKK,IAAIH,EAAM3D,OAAQwX,GAAQxR,IAAK,CACtD,MAAMvF,EAAQO,EAAIwW,EAAQC,EAAWzR,EAAIyR,EACzCC,EAAgBjX,GAASkD,EAAMqC,IAAM,EAGrC,IAAK,IAAI2R,EAAI,EAAGA,EAAIF,EAAUE,IAC5BD,EAAgBjX,EAAQkX,GAAKD,EAAgBjX,EAEjD,CACF,CAIA,OADe0S,EAAAA,SAAYuE,EAAiB,CAACH,EAAQC,EAAOC,IAC9CP,WAAW,EAC3B,CAOAG,cAAAA,CAAe9U,GAEb,MAAMG,EAAgBH,EAAKyC,IAAIrB,IAC7B,MAAMsB,EAAQ,IAAIlB,aAAaJ,EAAMa,KAAKxE,QAC1C,IAAK,IAAIgB,EAAI,EAAGA,EAAI2C,EAAMa,KAAKxE,OAAQgB,IACrCiE,EAAMjE,GAAK2C,EAAMa,KAAKxD,GAAK2C,EAAMa,KAAKxD,GAAK2C,EAAMc,KAAKzD,GAAK2C,EAAMc,KAAKzD,GAExE,OAAOiE,IAGT,OAAO7F,KAAKgY,sBAAsB1U,EACpC,CAOA4U,uBAAAA,CAAwB/F,GAEtB,MAAMuF,EAAa1X,KAAK8S,MAAM6E,OAAO,GAAGC,OACjC,CAAEO,EAAQC,EAAOC,GAAYX,EAE9BY,EAAkB,IAAI3T,aAAawT,EAASC,EAAQC,GAG1D,IAAK,IAAIzW,EAAI,EAAGA,EAAIuW,EAAQvW,IAC1B,IAAK,IAAIgF,EAAI,EAAGA,EAAIwR,EAAOxR,IAAK,CAC9B,MACMiH,GAASsE,EADG9N,KAAKC,MAAMsC,EAAIuL,EAAcvR,OAASwX,KACb,GAAK,IAE1C/W,EAAQO,EAAIwW,EAAQC,EAAWzR,EAAIyR,EACzC,IAAK,IAAIE,EAAI,EAAGA,EAAIF,EAAUE,IAC5BD,EAAgBjX,EAAQkX,GAAK1K,CAEjC,CAIF,OADekG,EAAAA,SAAYuE,EAAiB,CAACH,EAAQC,EAAOC,IAC9CP,WAAW,EAC3B,CAOA,4BAAMnC,CAAuBV,GAE3B,MAAMsB,QAAmBtB,EAAYvT,OAG/BgU,EAAU,GAChB,IAAK,IAAI9T,EAAI,EAAGA,EAAI2U,EAAW3V,QAAUgB,EAAI5B,KAAK+W,SAASnW,OAAQgB,IAAK,CACtE,MAAM6U,EAAaF,EAAW3U,GAE9B,GAAI6U,GAAczW,KAAKmC,OAAOiR,oBAAqB,CACjD,MAAMoF,EAAUxY,KAAK+W,SAASnV,GAG9B,IAAiB,cAAZ4W,GAAuC,cAAZA,IAA4B/B,EAAa,GACvE,SAGFf,EAAQ1U,KAAK,CACXwX,UACA/B,aACAgC,aAAc7W,EACd4U,UAAWgC,EACX9B,WAAY9U,GAEhB,CACF,CAGA,OAAO8T,EACJzU,KAAK,CAACC,EAAGC,IAAMA,EAAEsV,WAAavV,EAAEuV,YAChCR,MAAM,EAAGjW,KAAKmC,OAAOkR,KAC1B,CAOAuC,WAAAA,CAAYnI,EAAeiI,GACzB1V,KAAKgC,MAAMmV,kBACXnX,KAAKgC,MAAM2R,kBAAoBlG,EAI/BzN,KAAKgC,MAAM0R,qBACyB,GAAlC1T,KAAKgC,MAAM0R,qBAFC,GAEoCjG,EAGlDiI,EAAQ/F,QAAQ+I,IACd,MAAMF,EAAUE,EAAOF,QACvBxY,KAAKgC,MAAMoV,cAAcoB,IAAYxY,KAAKgC,MAAMoV,cAAcoB,IAAY,GAAK,GAEnF,CAMA7B,OAAAA,GACE,MAAO,CACL/T,cAAe5C,KAAK4C,cACpBiT,UAAW,iBACX8C,aAAc3Y,KAAK+W,SAAW/W,KAAK+W,SAASnW,OAAS,EACrDuB,OAAQnC,KAAKmC,OACbH,MAAOhC,KAAKgC,MAEhB,CAMA4W,aAAAA,GACE,OAAO5Y,KAAK+W,SAAW/W,KAAK+W,SAASnW,OAAS,CAChD,CAMAkP,YAAAA,CAAaC,GACX/P,KAAKmC,OAAS,IAAKnC,KAAKmC,UAAW4N,EACrC,CAMA8I,WAAAA,GACE,OAAO7Y,KAAK+W,SAAW,IAAI/W,KAAK+W,UAAY,EAC9C,CAMAhV,QAAAA,GACE,MAAO,IAAK/B,KAAKgC,MACnB,CAKA8F,OAAAA,GACM9H,KAAK8S,QACP9S,KAAK8S,MAAMhL,UACX9H,KAAK8S,MAAQ,MAGf9S,KAAK+W,SAAW,KAChB/W,KAAK4C,eAAgB,CAGvB,ECjaK,MAAMkW,EACX/Y,WAAAA,GACEC,KAAK+Y,OAAS,IAAI7Y,IAClBF,KAAKgZ,WAAa,IAAI9Y,IACtBF,KAAK4C,eAAgB,EAGrB5C,KAAKmC,OAAS,CACZ8W,eAAe,EACfC,aAAc,EACdC,kBAAkB,EAClBC,aAAc,IACdC,cAAe,GAIjBrZ,KAAKsZ,cAAgB,CACnBC,OAAQ,CACNC,IAAK,oDACL9K,KAAM,QACN+K,KAAM,OACNC,YAAa,qCAEfC,eAAgB,CACdH,IAAK,kGACL9K,KAAM,SACN+K,KAAM,OACNC,YAAa,gCAKjB1Z,KAAKgC,MAAQ,CACX4X,aAAc,EACdC,cAAe,EACfC,UAAW,EACXC,YAAa,EACbC,OAAQ,EAEZ,CAKA,gBAAMnX,GACJ,UAIQkR,EAAAA,QAGN/T,KAAKia,wBAGDja,KAAKmC,OAAOgX,wBACRnZ,KAAKka,gBAGbla,KAAK4C,eAAgB,CAGvB,CAAE,MAAOf,GAEP,MAAMA,CACR,CACF,CAKAoY,qBAAAA,GAEElG,EAAAA,MAAStT,IAAI,iCAAkC,GAC/CsT,EAAAA,MAAStT,IAAI,4BAA4B,GAGzCT,KAAKma,sBAAwBnN,YAAY,KACxB+G,EAAAA,SACJqG,SAAW,WAEpBpa,KAAKqa,uBAEN,IACL,CAKA,mBAAMH,GACJ,UAIQla,KAAK4T,UAAU,SAIvB,CAAE,MAAO/R,GAGT,CACF,CAQA,eAAM+R,CAAU0G,EAAW/Z,EAAU,CAAC,GACpC,IAEE,GAAIP,KAAKmC,OAAO8W,eAAiBjZ,KAAKgZ,WAAWxY,IAAI8Z,GAGnD,OADAta,KAAKgC,MAAM8X,YACJ9Z,KAAKgZ,WAAWrY,IAAI2Z,GAG7Bta,KAAKgC,MAAM+X,cAGX,MAAMQ,EAAcva,KAAKsZ,cAAcgB,GACvC,IAAKC,EACH,MAAM,IAAItX,MAAM,kBAAkBqX,KAIpC,MAAMrP,EAAY8B,YAAYpE,MAGxBmK,QAAc9S,KAAKwa,mBAAmBD,EAAaha,GAEnDka,EAAW1N,YAAYpE,MAAQsC,EAYrC,OARIjL,KAAKmC,OAAO8W,eACdjZ,KAAK0a,WAAWJ,EAAWxH,GAI7B9S,KAAKgC,MAAM4X,eACX5Z,KAAKgC,MAAM6X,eAAiBY,EAErB3H,CAET,CAAE,MAAOjR,GAGP,MADA7B,KAAKgC,MAAMgY,SACLnY,CACR,CACF,CAQA,wBAAM2Y,CAAmBD,EAAaha,GACpC,IAAIoa,EAEJ,IAAK,IAAIC,EAAU,EAAGA,GAAW5a,KAAKmC,OAAOkX,cAAeuB,IAC1D,IAEE,MAAMC,EAAc7a,KAAK8a,iBAAiBP,EAAaha,GACjDwa,EAAiB,IAAIC,QAAQ,CAACC,EAAGC,KACrCnJ,WAAW,IAAMmJ,EAAO,IAAIjY,MAAM,uBAAwBjD,KAAKmC,OAAOiX,gBAGxE,aAAa4B,QAAQG,KAAK,CAACN,EAAaE,GAE1C,CAAE,MAAOlZ,GAIP,GAHA8Y,EAAY9Y,EAGR+Y,EAAU5a,KAAKmC,OAAOkX,cAAe,CAEvC,MAAM+B,EAA+B,IAAvB/W,KAAKyC,IAAI,EAAG8T,SACpB,IAAII,QAAQK,GAAWtJ,WAAWsJ,EAASD,GACnD,CACF,CAGF,MAAMT,CACR,CAQA,sBAAMG,CAAiBP,EAAaha,GAClC,MAAM+a,EAAc,IACf/a,EACHgb,WAAazR,OAKf,GAAyB,UAArByQ,EAAY7L,KACd,aAAaqF,EAAAA,eAAkBwG,EAAYf,IAAK8B,GAC3C,GAAyB,WAArBf,EAAY7L,KACrB,aAAaqF,EAAAA,gBAAmBwG,EAAYf,IAAK8B,GAEjD,MAAM,IAAIrY,MAAM,2BAA2BsX,EAAY7L,OAE3D,CAOAgM,UAAAA,CAAWJ,EAAWxH,GAEpB,GAAI9S,KAAKgZ,WAAWS,MAAQzZ,KAAKmC,OAAO+W,aAAc,CAEpD,MAAMsC,EAAcxb,KAAKgZ,WAAWtJ,OAAO+L,OAAO5N,MAClD7N,KAAK0b,gBAAgBF,EACvB,CAEAxb,KAAKgZ,WAAWvY,IAAI6Z,EAAWxH,EAEjC,CAMA4I,eAAAA,CAAgBpB,GACd,GAAIta,KAAKgZ,WAAWxY,IAAI8Z,GAAY,CACpBta,KAAKgZ,WAAWrY,IAAI2Z,GAC5BxS,UACN9H,KAAKgZ,WAAWxX,OAAO8Y,EAEzB,CACF,CAKAD,mBAAAA,GAMiBtG,EAAAA,QAEjB,CAOA4H,YAAAA,CAAarB,GACX,MAAMnY,EAASnC,KAAKsZ,cAAcgB,GAClC,OAAKnY,EAIE,IACFA,EACHyZ,OAAQ5b,KAAKgZ,WAAWxY,IAAI8Z,GAC5BuB,OAAQ7b,KAAK+Y,OAAOvY,IAAI8Z,IANjB,IAQX,CAMAwB,kBAAAA,GACE,OAAOrM,OAAOC,KAAK1P,KAAKsZ,eAAe1T,IAAImW,IAAQ,CACjDA,UACG/b,KAAK2b,aAAaI,KAEzB,CAMAC,cAAAA,GACE,MAAO,CACLvC,KAAMzZ,KAAKgZ,WAAWS,KACtBwC,QAASjc,KAAKmC,OAAO+W,aACrBH,OAAQ5D,MAAM+G,KAAKlc,KAAKgZ,WAAWtJ,QACnCyM,QAASnc,KAAKgC,MAAM8X,WAAa9Z,KAAKgC,MAAM8X,UAAY9Z,KAAKgC,MAAM+X,cAAgB,EAEvF,CAMAhY,QAAAA,GACE,MAAO,IACF/B,KAAKgC,MACRoa,gBAAiBpc,KAAKgC,MAAM4X,aAAe,EACzC5Z,KAAKgC,MAAM6X,cAAgB7Z,KAAKgC,MAAM4X,aAAe,EACvDjN,YAAaoH,EAAAA,SACbsI,YAAarc,KAAKgc,iBAEtB,CAMA,iBAAMlI,CAAYwG,GAChB,IACE,MAAMxH,QAAc9S,KAAK4T,UAAU0G,GAG7B5C,EAAa5E,EAAM6E,OAAO,GAAGC,MAC7B5C,EAAajB,EAAAA,MAAS2D,EAAWzB,MAAM,IACvC4B,EAAe7C,EAAW8C,WAAW,GAGrCpO,EAASoJ,EAAMoC,QAAQ2C,GAG7B7C,EAAWlN,UACX+P,EAAa/P,UACTqN,MAAMC,QAAQ1L,GAChBA,EAAOiG,QAAQ0F,GAAUA,EAAOvN,WAEhC4B,EAAO5B,SAKX,CAAE,MAAOjG,GAET,CACF,CAKAiG,OAAAA,GAEE,IAAK,MAAOiU,EAAMjJ,KAAU9S,KAAKgZ,WAC/BlG,EAAMhL,UAGR9H,KAAKgZ,WAAWlX,QAGhB9B,KAAK+Y,OAAOjX,QAGR9B,KAAKma,uBACPhN,cAAcnN,KAAKma,uBAGrBna,KAAK4C,eAAgB,CAEvB,ECrWK,MAAM0Z,EACXvc,WAAAA,GACEC,KAAKuc,aAAe,IAAIzD,EACxB9Y,KAAKwc,iBAAmB,KACxBxc,KAAKyc,sBAAwB,KAE7Bzc,KAAK4C,eAAgB,EACrB5C,KAAKoQ,cAAe,EAGpBpQ,KAAKmC,OAAS,CACZua,cAAc,EACdC,sBAAsB,EACtBvJ,oBAAqB,GACrBwJ,eAAgB,EAChBC,eAAgB,WAChBC,WAAW,GAIb9c,KAAKgC,MAAQ,CACX+a,gBAAiB,EACjBrJ,qBAAsB,EACtBC,kBAAmB,EACnBqJ,WAAY,EAEhB,CAKA,gBAAMna,GACJ,UAIQ7C,KAAKid,+BAGLjd,KAAKuc,aAAa1Z,mBAGlB7C,KAAKkd,aAEXld,KAAK4C,eAAgB,EAErB9C,EAAS2B,KAAKQ,EAAwB,CACpC8W,OAAQ/Y,KAAKmd,kBACbC,QAASrJ,EAAAA,aACTjI,OAAQiI,EAAAA,UAOZ,CAAE,MAAOlS,GAGP,MADA/B,EAAS2B,KAAKQ,EAAuB,CAAEJ,MAAOA,EAAMsP,UAC9CtP,CACR,CACF,CAKA,4BAAMob,GACJ,IAEE,GAAIjd,KAAKmC,OAAO2a,iBAAmB/I,EAAAA,QAAY,CAC5BA,EAAAA,SAAYsJ,eAGhBC,SAAS,eACdvJ,EAAAA,WAAc,eAIdA,EAAAA,WAAc,MAExB,YACQA,EAAAA,WAAc,aAKhBA,EAAAA,QAGNA,EAAAA,MAAStT,IAAI,iCAAkC,GAC/CsT,EAAAA,MAAStT,IAAI,4BAA4B,EAE3C,CAAE,MAAOoB,GAEP,MAAMA,CACR,CACF,CAKA,gBAAMqb,GACJ,MAAMK,EAAe,GAGjBvd,KAAKmC,OAAOua,cACda,EAAavc,KAAKhB,KAAKwd,mBAIrBxd,KAAKmC,OAAOwa,sBACdY,EAAavc,KAAKhB,KAAKyd,gCAInBzC,QAAQ0C,IAAIH,EACpB,CAKA,qBAAMC,GACJ,IAGExd,KAAKwc,iBAAmB,IAAI3J,QACtB7S,KAAKwc,iBAAiB3Z,YAI9B,CAAE,MAAOhB,GAGP,MADA7B,KAAKmC,OAAOua,cAAe,EACrB7a,CACR,CACF,CAKA,4BAAM4b,GACJ,IAGEzd,KAAKyc,sBAAwB,IAAI3F,QAC3B9W,KAAKyc,sBAAsB5Z,YAInC,CAAE,MAAOhB,GAEP7B,KAAKmC,OAAOwa,sBAAuB,CAErC,CACF,CAOA,cAAMrH,CAASC,GACb,IAAKvV,KAAK4C,cACR,MAAM,IAAIK,MAAM,gCAGlB,MAAMgI,EAAY8B,YAAYpE,MAE9B,IACE7I,EAAS2B,KAAKQ,GAEd,MAAMyT,EAAU,CACdjN,UAAWC,KAAKC,MAChBsM,YAAa,GACb0I,YAAa,GACbhE,eAAgB,GAChBlD,WAAY,EACZpJ,eAAgB,GAIZuQ,EAAoB,GAGtB5d,KAAKwc,kBAAoBxc,KAAKmC,OAAOua,cACvCkB,EAAkB5c,KAChBhB,KAAKwc,iBAAiBlH,SAASC,GAC5BsI,KAAKnF,IAAU,CAAGhK,KAAM,SAAUgK,YAClCoF,MAAMjc,IAAS,CAAG6M,KAAM,SAAU7M,YAKrC7B,KAAKyc,uBAAyBzc,KAAKmC,OAAOwa,sBAC5CiB,EAAkB5c,KAChBhB,KAAKyc,sBAAsB1E,OAAOxC,GAC/BsI,KAAKnF,IAAU,CAAGhK,KAAM,SAAUgK,YAClCoF,MAAMjc,IAAS,CAAG6M,KAAM,SAAU7M,YAKzC,MAAMkc,QAAyB/C,QAAQ0C,IAAIE,GAG3C,IAAK,MAAMI,KAAaD,EAClBC,EAAUnc,MAEZ7B,KAAKgC,MAAMgb,cAIU,WAAnBgB,EAAUtP,MAAqBsP,EAAUtF,SAC3ChD,EAAQiI,YAAcK,EAAUtF,OAAOzD,aAAe,GACtDS,EAAQT,YAAYjU,QAAQgd,EAAUtF,OAAOzD,aAAe,KAGvC,WAAnB+I,EAAUtP,MAAqBsP,EAAUtF,SAC3ChD,EAAQiE,eAAiBqE,EAAUtF,OAAO3B,UAAY,GACtDrB,EAAQT,YAAYjU,QAAQgd,EAAUtF,OAAO3B,UAAY,MAK7DrB,EAAQT,YAAcjV,KAAKie,yBAAyBvI,EAAQT,aAC5DS,EAAQe,WAAaf,EAAQT,YAAYrU,OAAS,EAAI8U,EAAQT,YAAY,GAAGwB,WAAa,EAG1F,MAAMyH,EAAUnR,YAAYpE,MAU5B,OATA+M,EAAQrI,eAAiB6Q,EAAUjT,EAGnCjL,KAAK4V,YAAYF,EAAQrI,gBAGzBvN,EAAS2B,KAAKQ,EAAiCyT,GAC/C5V,EAAS2B,KAAKQ,GAEPyT,CAET,CAAE,MAAO7T,GAIP,MAFA7B,KAAKgC,MAAMgb,aACXld,EAAS2B,KAAKQ,EAAuB,CAAEJ,MAAOA,EAAMsP,UAC9CtP,CACR,CACF,CAOAoc,wBAAAA,CAAyBhJ,GACvB,OAAOA,EACJzO,OAAO2X,GAAQA,EAAK1H,YAAczW,KAAKmC,OAAOiR,qBAC9CnS,KAAK,CAACC,EAAGC,IAAMA,EAAEsV,WAAavV,EAAEuV,YAChCR,MAAM,EAAGjW,KAAKmC,OAAOya,eAC1B,CAMAhH,WAAAA,CAAYnI,GACVzN,KAAKgC,MAAM+a,kBACX/c,KAAKgC,MAAM2R,kBAAoBlG,EAI/BzN,KAAKgC,MAAM0R,qBACyB,GAAlC1T,KAAKgC,MAAM0R,qBAFC,GAEoCjG,CACpD,CAMA0P,eAAAA,GACE,MAAMpE,EAAS,GAoBf,OAlBI/Y,KAAKwc,kBACPzD,EAAO/X,KAAK,CACV+a,KAAM,SACNrN,KAAM,uBACN0P,QAAS,IACTjK,OAAQ,WAIRnU,KAAKyc,uBACP1D,EAAO/X,KAAK,CACV+a,KAAM,iBACNrN,KAAM,qBACN0P,QAASpe,KAAKyc,sBAAsB7D,gBACpCzE,OAAQ,WAIL4E,CACT,CAMA4C,YAAAA,GACE,MAAO,CACL/Y,cAAe5C,KAAK4C,cACpBwa,QAASrJ,EAAAA,aACTjI,OAAQiI,EAAAA,SACRgF,OAAQ/Y,KAAKmd,kBACbhb,OAAQnC,KAAKmC,OACbH,MAAOhC,KAAKgC,MAEhB,CAMA8N,YAAAA,CAAaC,GACX/P,KAAKmC,OAAS,IAAKnC,KAAKmC,UAAW4N,GAG/B/P,KAAKwc,kBACPxc,KAAKwc,iBAAiB1M,aAAaC,GAGjC/P,KAAKyc,uBACPzc,KAAKyc,sBAAsB3M,aAAaC,EAE5C,CAMAhO,QAAAA,GACE,MAAO,IACF/B,KAAKgC,MACR2K,YAAaoH,EAAAA,SACbqJ,QAASrJ,EAAAA,aAEb,CAKAsK,aAAAA,GAEEtK,EAAAA,mBAGI/O,OAAOsZ,IACTtZ,OAAOsZ,IAKX,CAKAxW,OAAAA,GACM9H,KAAKwc,kBACPxc,KAAKwc,iBAAiB1U,UAGpB9H,KAAKyc,uBACPzc,KAAKyc,sBAAsB3U,UAGzB9H,KAAKuc,cACPvc,KAAKuc,aAAazU,UAGpB9H,KAAKqe,gBAELre,KAAK4C,eAAgB,CAEvB,ECtYK,MAAM2b,EACXxe,WAAAA,GACEC,KAAKwe,OAAS,IAAIte,IAClBF,KAAKye,UAAY,KACjBze,KAAK4C,eAAgB,EAGrB5C,KAAKmC,OAAS,CACZuc,UAAW,EACXC,cAAe,IACfC,eAAgB,IAChBC,gBAAiB,IACjBC,SAAU,YACVC,SAAU,SACVC,kBAAkB,GAIpBhf,KAAKif,OAAS,CACZR,UAAW,CACTK,SAAU,QACVI,OAAQ,QACRC,cAAe,OACfC,WAAY,oBACZL,SAAU,QAEZM,MAAO,CACLC,WAAY,qBACZC,MAAO,QACPC,QAAS,WACTC,aAAc,MACdC,aAAc,MACdC,UAAW,+BACXC,WAAY,gBACZC,SAAU,QACVC,SAAU,eAKd9f,KAAK+f,eAAiB,CACxB,CAKAld,UAAAA,GACM7C,KAAK4C,gBAOT5C,KAAKggB,kBAELhgB,KAAK4C,eAAgB,EAEvB,CAKAod,eAAAA,GACEhgB,KAAKye,UAAYwB,SAASC,cAAc,OACxClgB,KAAKye,UAAU0B,GAAK,yBAGpB1Q,OAAO2Q,OAAOpgB,KAAKye,UAAU4B,MAAOrgB,KAAKif,OAAOR,WAGhDze,KAAKsgB,0BAGLL,SAASM,KAAKC,YAAYxgB,KAAKye,UAGjC,CAKA6B,uBAAAA,GACE,GAAKtgB,KAAKye,UAUV,OALAze,KAAKye,UAAU4B,MAAMI,IAAM,GAC3BzgB,KAAKye,UAAU4B,MAAMK,OAAS,GAC9B1gB,KAAKye,UAAU4B,MAAM5Z,KAAO,GAC5BzG,KAAKye,UAAU4B,MAAM1Z,MAAQ,GAErB3G,KAAKmC,OAAO2c,UAClB,IAAK,WACH9e,KAAKye,UAAU4B,MAAMI,IAAM,OAC3BzgB,KAAKye,UAAU4B,MAAM5Z,KAAO,OAC5B,MACF,IAAK,YAYL,QACEzG,KAAKye,UAAU4B,MAAMI,IAAM,OAC3BzgB,KAAKye,UAAU4B,MAAM1Z,MAAQ,aAV/B,IAAK,cACH3G,KAAKye,UAAU4B,MAAMK,OAAS,OAC9B1gB,KAAKye,UAAU4B,MAAM5Z,KAAO,OAC5B,MACF,IAAK,eACHzG,KAAKye,UAAU4B,MAAMK,OAAS,OAC9B1gB,KAAKye,UAAU4B,MAAM1Z,MAAQ,OAMnC,CASAga,SAAAA,CAAUrM,EAAMmC,EAAa,EAAKlW,EAAU,CAAC,GACtCP,KAAK4C,eACR5C,KAAK6C,aAIP,MAAM+d,EAAU,YAAW5gB,KAAK+f,eAG1Bc,EAAe7gB,KAAK8gB,mBAAmBxM,EAAMmC,EAAYlW,GA6B/D,OA1BAP,KAAK+gB,mBAGL/gB,KAAKye,UAAU+B,YAAYK,GAG3B7gB,KAAKwe,OAAO/d,IAAImgB,EAAS,CACvBI,QAASH,EACTvM,OACAmC,aACAhO,UAAWC,KAAKC,MAChBpI,YAIEP,KAAKmC,OAAO6c,kBACdhf,KAAKihB,UAAUJ,GAIjB9O,WAAW,KACT/R,KAAKkhB,UAAUN,IACd5gB,KAAKmC,OAAOwc,eAIRiC,CACT,CASAE,kBAAAA,CAAmBxM,EAAMmC,EAAYlW,GACnC,MAAMygB,EAAUf,SAASC,cAAc,OACvCc,EAAQxK,UAAY,oBAGpB/G,OAAO2Q,OAAOY,EAAQX,MAAOrgB,KAAKif,OAAOI,OAGzC,MAAM8B,EAAU9c,KAAK8C,IAAI,GAAKsP,GAC9BuK,EAAQX,MAAMf,WAAa,iBAA2B,GAAV6B,KAG5C,MAAM5B,EAAQvf,KAAKohB,cAAc9M,GAC7BiL,IACFyB,EAAQX,MAAMgB,WAAa,aAAa9B,KAI1C,MAAM+B,EAAiB7K,EAAa,EAAM,MAAmB,IAAbA,GAAkBpH,QAAQ,OAAS,GAanF,OAZA2R,EAAQO,UAAY,yEAEdvhB,KAAKwhB,gBAAgBlN,2BAEvBgN,EAAiB,gDAAgDA,UAAyB,WAI1F/gB,EAAQ8f,OACV5Q,OAAO2Q,OAAOY,EAAQX,MAAO9f,EAAQ8f,OAGhCW,CACT,CAOAQ,eAAAA,CAAgBlN,GAgCd,MAAO,GA9BS,CACd,OAAU,MACV,MAAS,KACT,SAAY,KACZ,SAAY,KACZ,OAAU,KACV,UAAa,KACb,KAAQ,KACR,MAAS,IACT,MAAS,KACT,QAAW,KACX,MAAS,KACT,IAAO,KACP,IAAO,KACP,IAAO,KACP,KAAQ,KACR,MAAS,KACT,KAAQ,KACR,KAAQ,MACR,QAAW,KACX,KAAQ,KACR,UAAa,KACb,QAAW,KACX,KAAQ,KACR,KAAQ,KACR,MAAS,IACT,MAAS,MAGUA,IAAS,QACZA,GACpB,CAOA8M,aAAAA,CAAcK,GAaZ,MAZiB,CACf,OAAU,UACV,MAAS,UACT,QAAW,UACX,UAAa,UACb,MAAS,UACT,MAAS,UACT,OAAU,UACV,SAAY,UACZ,SAAY,WAGEA,IAAc,IAChC,CAMAR,SAAAA,CAAUD,GACRA,EAAQX,MAAMc,QAAU,IACxBH,EAAQX,MAAMqB,UAAY,mBAG1BV,EAAQW,aAERX,EAAQX,MAAMT,WAAa,OAAO5f,KAAKmC,OAAOyc,wBAC9CoC,EAAQX,MAAMc,QAAU,IACxBH,EAAQX,MAAMqB,UAAY,eAC5B,CAOAE,UAAAA,CAAWZ,GACT,OAAO,IAAIhG,QAASK,IAClB2F,EAAQX,MAAMT,WAAa,OAAO5f,KAAKmC,OAAO0c,yBAC9CmC,EAAQX,MAAMc,QAAU,IACxBH,EAAQX,MAAMqB,UAAY,mBAE1B3P,WAAW,KACTsJ,KACCrb,KAAKmC,OAAO0c,kBAEnB,CAMA,eAAMqC,CAAUN,GACd,MAAMiB,EAAY7hB,KAAKwe,OAAO7d,IAAIigB,GAC7BiB,IAKD7hB,KAAKmC,OAAO6c,wBACRhf,KAAK4hB,WAAWC,EAAUb,SAI9Ba,EAAUb,QAAQc,YACpBD,EAAUb,QAAQc,WAAWC,YAAYF,EAAUb,SAIrDhhB,KAAKwe,OAAOhd,OAAOof,GAGrB,CAKAG,gBAAAA,GACE,GAAI/gB,KAAKwe,OAAO/E,MAAQzZ,KAAKmC,OAAOuc,UAAW,CAE7C,MAAMsD,EAAgBhiB,KAAKwe,OAAO9O,OAAO+L,OAAO5N,MAChD7N,KAAKkhB,UAAUc,EACjB,CACF,CAKAC,cAAAA,GACmB9M,MAAM+G,KAAKlc,KAAKwe,OAAO9O,QAC/BC,QAAQiR,IACf5gB,KAAKkhB,UAAUN,IAInB,CAOAsB,WAAAA,CAAYtB,EAASuB,GACnB,MAAMN,EAAY7hB,KAAKwe,OAAO7d,IAAIigB,GAClC,GAAKiB,EAAL,CAWA,QANqBhS,IAAjBsS,EAAQ7N,OACVuN,EAAUvN,KAAO6N,EAAQ7N,KACzBuN,EAAUb,QAAQoB,cAAc,OAAOC,YAAcriB,KAAKwhB,gBAAgBW,EAAQ7N,YAIzDzE,IAAvBsS,EAAQ1L,WAA0B,CACpCoL,EAAUpL,WAAa0L,EAAQ1L,WAC/B,MAAM6L,EAAgBT,EAAUb,QAAQoB,cAAc,kBAClDE,IACFA,EAAcD,YAAc,KAA0B,IAArBF,EAAQ1L,YAAkBpH,QAAQ,OAEvE,CAGI8S,EAAQ9B,OACV5Q,OAAO2Q,OAAOyB,EAAUb,QAAQX,MAAO8B,EAAQ9B,MAnBjD,CAqBF,CAMAkC,gBAAAA,GACE,OAAOpN,MAAM+G,KAAKlc,KAAKwe,OAAOgE,WAAW5c,IAAI,EAAEua,EAAIsC,MAAU,CAC3DtC,KACA7L,KAAMmO,EAAKnO,KACXmC,WAAYgM,EAAKhM,WACjBhO,UAAWga,EAAKha,YAEpB,CAMAqH,YAAAA,CAAaC,GASX,GARA/P,KAAKmC,OAAS,IAAKnC,KAAKmC,UAAW4N,GAG/BA,EAAU+O,UACZ9e,KAAKsgB,0BAIHvQ,EAAUgP,UAAY/e,KAAKye,UAAW,CACxC,MAAMiE,EAAc,CAClBC,MAAO,OACPC,OAAQ,OACRC,MAAO,QAET7iB,KAAKye,UAAU4B,MAAMtB,SAAW2D,EAAY3S,EAAUgP,WAAa,MACrE,CACF,CAMAhd,QAAAA,GACE,MAAO,CACL+gB,cAAe9iB,KAAKwe,OAAO/E,KAC3BiF,UAAW1e,KAAKmC,OAAOuc,UACvBqE,iBAAkB/iB,KAAK+f,eAE3B,CAKAjY,OAAAA,GAEE9H,KAAKiiB,iBAGDjiB,KAAKye,WAAaze,KAAKye,UAAUqD,YACnC9hB,KAAKye,UAAUqD,WAAWC,YAAY/hB,KAAKye,WAG7Cze,KAAKye,UAAY,KACjBze,KAAKwe,OAAO1c,QACZ9B,KAAK4C,eAAgB,CAGvB,EC7bK,MAAMogB,EACXjjB,WAAAA,GACEC,KAAKijB,OAAS,KACdjjB,KAAKkjB,UAAY,KACjBljB,KAAK0Q,aAAe,KACpB1Q,KAAK4C,eAAgB,EACrB5C,KAAKgL,WAAY,EACjBhL,KAAKmjB,iBAAmB,KAGxBnjB,KAAKmC,OAAS,CACZihB,uBAAuB,EACvBC,kBAAkB,EAClBC,4BAA4B,EAC5BC,uBAAwB,IACxBpC,QAAS,GACTqC,eAAe,EACfC,YAAa,WAIfzjB,KAAK0jB,aAAe,CAClBC,QAAS,CACPC,QAAS,CAAEC,EAAG,GAAIC,EAAG,IAAK3iB,EAAG,KAC7B4iB,QAAS,CAAEF,EAAG,GAAIC,EAAG,IAAK3iB,EAAG,KAC7B6iB,SAAU,CAAEH,EAAG,IAAKC,EAAG,IAAK3iB,EAAG,GAC/BwN,QAAS,CAAEkV,EAAG,IAAKC,EAAG,GAAI3iB,EAAG,IAC7B8iB,SAAU,CAAEJ,EAAG,IAAKC,EAAG,IAAK3iB,EAAG,MAEjC+iB,cAAe,CACbN,QAAS,CAAEC,EAAG,EAAGC,EAAG,IAAK3iB,EAAG,KAC5B4iB,QAAS,CAAEF,EAAG,EAAGC,EAAG,IAAK3iB,EAAG,KAC5B6iB,SAAU,CAAEH,EAAG,IAAKC,EAAG,IAAK3iB,EAAG,GAC/BwN,QAAS,CAAEkV,EAAG,IAAKC,EAAG,EAAG3iB,EAAG,GAC5B8iB,SAAU,CAAEJ,EAAG,IAAKC,EAAG,IAAK3iB,EAAG,OAKnCnB,KAAKmkB,iBAAmB,KAGxBnkB,KAAKgC,MAAQ,CACXoiB,eAAgB,EAChBC,kBAAmB,EACnBC,eAAgB,EAEpB,CAMA,gBAAMzhB,CAAW6N,GACf,IACE,GAAI1Q,KAAK4C,cACP,OAKF5C,KAAK0Q,aAAeA,QAGd1Q,KAAKukB,eAGXvkB,KAAKwkB,2BAELxkB,KAAK4C,eAAgB,CAGvB,CAAE,MAAOf,GAEP,MAAMA,CACR,CACF,CAKA,kBAAM0iB,GAEJ,MAAME,EAAiBzkB,KAAK0kB,qBAC5B,IAAKD,EACH,MAAM,IAAIxhB,MAAM,2CAIlBjD,KAAKijB,OAAShD,SAASC,cAAc,UACrClgB,KAAKijB,OAAO9C,GAAK,6BACjBngB,KAAKijB,OAAO5C,MAAMsE,QAAU,oJAOf3kB,KAAKmC,OAAOgf,uCAKzBnhB,KAAKkjB,UAAYljB,KAAKijB,OAAO2B,WAAW,MAGxCH,EAAejE,YAAYxgB,KAAKijB,QAGhCjjB,KAAK6kB,kBAGP,CAMAH,kBAAAA,GAEE,MAAMI,EAAY,CAChB,gBACA,yBACA,gBACA,uBAGF,IAAK,MAAMC,KAAYD,EAAW,CAChC,MAAMrG,EAAYwB,SAASmC,cAAc2C,GACzC,GAAItG,GAAaA,EAAUuG,SAAShlB,KAAK0Q,cACvC,OAAO+N,CAEX,CAGA,OAAOze,KAAK0Q,aAAauU,aAC3B,CAKAT,wBAAAA,GAOE,GALAxf,OAAOoM,iBAAiB,SAAU,KAChCpR,KAAK6kB,qBAIH7f,OAAOkgB,eAAgB,CACzB,MAAMC,EAAiB,IAAID,eAAe,KACxCllB,KAAK6kB,qBAGH7kB,KAAKijB,OAAOgC,eACdE,EAAeC,QAAQplB,KAAKijB,OAAOgC,cAEvC,CACF,CAKAJ,gBAAAA,GACE,IAAK7kB,KAAKijB,SAAWjjB,KAAKijB,OAAOgC,cAC/B,OAGF,MACMI,EADYrlB,KAAKijB,OAAOgC,cACPK,wBAGvBtlB,KAAKijB,OAAO7K,MAAQiN,EAAKjN,MACzBpY,KAAKijB,OAAO9K,OAASkN,EAAKlN,MAG5B,CAKA3T,KAAAA,GACOxE,KAAK4C,gBAAiB5C,KAAKgL,YAMhChL,KAAKgL,WAAY,EACjBhL,KAAKulB,aACP,CAKAC,KAAAA,GACExlB,KAAKgL,WAAY,EAEbhL,KAAKmjB,mBACPsC,qBAAqBzlB,KAAKmjB,kBAC1BnjB,KAAKmjB,iBAAmB,KAE5B,CAKAjW,IAAAA,GACElN,KAAKwlB,QAGDxlB,KAAKkjB,WACPljB,KAAKkjB,UAAUwC,UAAU,EAAG,EAAG1lB,KAAKijB,OAAO7K,MAAOpY,KAAKijB,OAAO9K,OAElE,CAKAoN,UAAAA,GACE,IAAKvlB,KAAKgL,UACR,OAGF,MAAMC,EAAY8B,YAAYpE,MAE9B,IAEE3I,KAAKkjB,UAAUwC,UAAU,EAAG,EAAG1lB,KAAKijB,OAAO7K,MAAOpY,KAAKijB,OAAO9K,QAG1DnY,KAAKmkB,iBACPnkB,KAAK2lB,oBAAoB3lB,KAAKmkB,kBAE9BnkB,KAAK4lB,qBAIH5lB,KAAKmC,OAAOqhB,eACdxjB,KAAK6lB,kBAIP,MAAMlY,EAAaZ,YAAYpE,MAAQsC,EACvCjL,KAAK4V,YAAYjI,EAEnB,CAAE,MAAO9L,GAET,CAGA7B,KAAKmjB,iBAAmB2C,sBAAsB,IAAM9lB,KAAKulB,aAC3D,CAMAI,mBAAAA,CAAoB3iB,GAEdhD,KAAKmC,OAAOihB,uBAAyBpgB,EAAUuF,KAAOvF,EAAUuF,IAAIc,WACtErJ,KAAK+lB,sBAAsB/iB,EAAUuF,IAAIc,UAAWrG,EAAUuF,IAAInG,YAIhEpC,KAAKmC,OAAOkhB,kBAAoBrgB,EAAUuF,KAAOvF,EAAUuF,IAAIY,YACjEnJ,KAAKgmB,iBAAiBhjB,EAAUuF,IAAIY,WAExC,CAOA4c,qBAAAA,CAAsB5T,EAAe/P,GACnC,MAAM4P,EAAeG,EAAcvR,OAC7BqlB,EAAYjmB,KAAKijB,OAAO7K,MAAQpG,EAAgB,IAChDkU,EAAU9jB,EAAa,EACvB+jB,EAASnmB,KAAK0jB,aAAa1jB,KAAKmC,OAAOshB,aAE7C,IAAI2C,EAAI,EAER,IAAK,IAAIxkB,EAAI,EAAGA,EAAIoQ,EAAcpQ,IAAK,CACrC,MAAMykB,EAAalU,EAAcvQ,GAAK,IAAS5B,KAAKijB,OAAO9K,OAAS,GAC9DmO,EAAoB1kB,EAAIskB,EAAWlU,EACnCuU,EAAYpU,EAAcvQ,GAAK,IAGrC,IAAI2d,EAGFA,EAFEvf,KAAKmC,OAAOmhB,4BAA8BgD,EAAmBtmB,KAAKmC,OAAOohB,uBAEnEvjB,KAAKwmB,iBAAiBL,EAAOnC,SAAUmC,EAAOxX,QAAS4X,GACtDD,EAAmB,IAEpBtmB,KAAKwmB,iBAAiBL,EAAOvC,QAASuC,EAAOpC,QAASwC,GAGtDvmB,KAAKwmB,iBAAiB,CAAE3C,EAAG,EAAGC,EAAG,EAAG3iB,EAAG,GAAKglB,EAAOvC,QAAS2C,GAItEvmB,KAAKkjB,UAAUuD,UAAY,QAAQlH,EAAMsE,MAAMtE,EAAMuE,MAAMvE,EAAMpe,MAAkB,GAAZolB,KACvEvmB,KAAKkjB,UAAUwD,SAASN,EAAGpmB,KAAKijB,OAAO9K,OAASkO,EAAWJ,EAAUI,GAErED,GAAKH,EAAW,CAClB,CACF,CAMAD,gBAAAA,CAAiB/T,GACf,MAAMkU,EAASnmB,KAAK0jB,aAAa1jB,KAAKmC,OAAOshB,aAE7CzjB,KAAKkjB,UAAUyD,UAAY,EAC3B3mB,KAAKkjB,UAAU0D,YAAc,QAAQT,EAAOlC,SAASJ,MAAMsC,EAAOlC,SAASH,MAAMqC,EAAOlC,SAAS9iB,UACjGnB,KAAKkjB,UAAU2D,YAEf,MAAMC,EAAa9mB,KAAKijB,OAAO7K,MAAQnG,EAAerR,OACtD,IAAIwlB,EAAI,EAER,IAAK,IAAIxkB,EAAI,EAAGA,EAAIqQ,EAAerR,OAAQgB,IAAK,CAC9C,MACMmlB,EADI9U,EAAerQ,GAAK,IACf5B,KAAKijB,OAAO9K,OAAU,EAE3B,IAANvW,EACF5B,KAAKkjB,UAAU8D,OAAOZ,EAAGW,GAEzB/mB,KAAKkjB,UAAU+D,OAAOb,EAAGW,GAG3BX,GAAKU,CACP,CAEA9mB,KAAKkjB,UAAU+D,OAAOjnB,KAAKijB,OAAO7K,MAAOpY,KAAKijB,OAAO9K,OAAS,GAC9DnY,KAAKkjB,UAAUgE,QACjB,CAKAtB,kBAAAA,GACE5lB,KAAKkjB,UAAUuD,UAAY,2BAC3BzmB,KAAKkjB,UAAUwD,SAAS,GAAI,GAAI,IAAK,IAErC1mB,KAAKkjB,UAAUuD,UAAY,2BAC3BzmB,KAAKkjB,UAAUiE,KAAO,aACtBnnB,KAAKkjB,UAAUkE,SAAS,6BAA8B,GAAI,IAC1DpnB,KAAKkjB,UAAUkE,SAAS,uBAAwB,GAAI,GACtD,CAKAvB,eAAAA,GACE7lB,KAAKkjB,UAAUuD,UAAY,qBAC3BzmB,KAAKkjB,UAAUwD,SAAS,GAAI1mB,KAAKijB,OAAO9K,OAAS,IAAK,IAAK,KAE3DnY,KAAKkjB,UAAUuD,UAAY,QAC3BzmB,KAAKkjB,UAAUiE,KAAO,aAER,CACZ,iCACA,WAAWnnB,KAAKijB,OAAO7K,SAASpY,KAAKijB,OAAO9K,SAC5C,WAAWnY,KAAKgC,MAAMoiB,iBACtB,eAAepkB,KAAKgC,MAAMqiB,kBAAkBhV,QAAQ,OACpD,gBAAgBrP,KAAKgC,MAAMsiB,eAAejV,QAAQ,OAClD,iBAAiBrP,KAAKmC,OAAOshB,eAGzB9T,QAAQ,CAACiF,EAAMvT,KACnBrB,KAAKkjB,UAAUkE,SAASxS,EAAM,GAAI5U,KAAKijB,OAAO9K,OAAS,IAAe,GAAR9W,IAElE,CASAmlB,gBAAAA,CAAiBa,EAAQC,EAAQC,GAC/B,MAAO,CACL1D,EAAGxf,KAAKmjB,MAAMH,EAAOxD,GAAKyD,EAAOzD,EAAIwD,EAAOxD,GAAK0D,GACjDzD,EAAGzf,KAAKmjB,MAAMH,EAAOvD,GAAKwD,EAAOxD,EAAIuD,EAAOvD,GAAKyD,GACjDpmB,EAAGkD,KAAKmjB,MAAMH,EAAOlmB,GAAKmmB,EAAOnmB,EAAIkmB,EAAOlmB,GAAKomB,GAErD,CAMAE,mBAAAA,CAAoBzkB,GAClBhD,KAAKmkB,iBAAmBnhB,CAC1B,CAMA4S,WAAAA,CAAYjI,GACV3N,KAAKgC,MAAMoiB,iBACXpkB,KAAKgC,MAAMsiB,eAAiB3W,EAI5B3N,KAAKgC,MAAMqiB,kBACsB,GAA/BrkB,KAAKgC,MAAMqiB,kBAFC,GAEiC1W,CACjD,CAMAmC,YAAAA,CAAaC,GACX/P,KAAKmC,OAAS,IAAKnC,KAAKmC,UAAW4N,QAGTF,IAAtBE,EAAUoR,SAAyBnhB,KAAKijB,SAC1CjjB,KAAKijB,OAAO5C,MAAMc,QAAUpR,EAAUoR,QAE1C,CAMApf,QAAAA,GACE,MAAO,IAAK/B,KAAKgC,MACnB,CAKA8F,OAAAA,GACE9H,KAAKkN,OAGDlN,KAAKijB,QAAUjjB,KAAKijB,OAAOgC,eAC7BjlB,KAAKijB,OAAOgC,cAAclD,YAAY/hB,KAAKijB,QAG7CjjB,KAAKijB,OAAS,KACdjjB,KAAKkjB,UAAY,KACjBljB,KAAK0Q,aAAe,KACpB1Q,KAAKmkB,iBAAmB,KACxBnkB,KAAK4C,eAAgB,CAGvB,ECpcK,MAAM8kB,EACX3nB,WAAAA,GACEC,KAAKmC,OAAS,KACdnC,KAAK2nB,cAAgB3nB,KAAK4nB,mBAC1B5nB,KAAK6nB,WAAa,qCAClB7nB,KAAK4C,eAAgB,EAGrB5C,KAAK8nB,gBAAkB,IAAIC,GAC7B,CAMAH,gBAAAA,GACE,MAAO,CAELI,GAAI,CACFtL,cAAc,EACdC,sBAAsB,EACtBsL,0BAA2B,GAC3BC,kCAAmC,GACnCtL,eAAgB,EAChBuL,uBAAuB,EACvBC,gBAAgB,EAChBC,YAAa,GACbC,OAAQ,IAIVC,GAAI,CACFC,YAAY,EACZ7J,cAAe,IACf8J,kBAAmB,EACnBC,cAAe,YACfC,uBAAuB,EACvBC,qBAAqB,EACrBC,qBAAsB,GACtB9J,SAAU,SACV+J,MAAO,QAIT/b,YAAa,CACXwD,mBAAoB,IACpBwY,uBAAuB,EACvBC,eAAgB,UAChBC,6BAA6B,EAC7BC,kBAAkB,GAIpBC,MAAO,CACL/mB,WAAY,KACZI,QAAS,KACT8N,sBAAuB,GACvB8Y,mBAAmB,EACnBnhB,WAAY,QAIdohB,cAAe,CACbjG,uBAAuB,EACvBC,kBAAkB,EAClBC,4BAA4B,EAC5BC,uBAAwB,IACxBE,YAAa,UACb6F,aAAc,CACZ1F,QAAS,UACTG,QAAS,UACTC,SAAU,UACVrV,QAAS,YAKbuV,cAAe,CACbqF,oBAAoB,EACpBC,iBAAiB,EACjBC,oBAAoB,EACpBC,0BAA0B,EAC1BC,cAAc,GAIhBC,MAAO,CACLC,eAAe,EACfC,qBAAqB,EACrBC,iBAAiB,EACjBC,SAAU,QAGhB,CAKA,gBAAMC,GACJ,IAIE,MAAMC,QAAqBlqB,KAAKmqB,kBAGhCnqB,KAAKmC,OAASnC,KAAKoqB,aAAapqB,KAAK2nB,cAAeuC,GAGpDlqB,KAAKqqB,iBAELrqB,KAAK4C,eAAgB,CAGvB,CAAE,MAAOf,GAGP7B,KAAKmC,OAAS,IAAKnC,KAAK2nB,eACxB3nB,KAAK4C,eAAgB,CACvB,CACF,CAMA,qBAAMunB,GACJ,IAEE,MAAsB,oBAAXG,QAA0BA,OAAOC,cAC7BvqB,KAAKwqB,wBAIbxqB,KAAKyqB,sBAEd,CAAE,MAAO5oB,GAEP,MAAO,CAAC,CACV,CACF,CAMA,2BAAM2oB,GACJ,OAAO,IAAIxP,QAAQ,CAACK,EAASH,KAC3BoP,OAAOC,QAAQG,KAAK/pB,IAAI,CAACX,KAAK6nB,YAAcnP,IACtC4R,OAAOK,QAAQhQ,UACjBO,EAAOoP,OAAOK,QAAQhQ,WAEtBU,EAAQ3C,EAAO1Y,KAAK6nB,aAAe,CAAC,MAI5C,CAMA4C,oBAAAA,GACE,MAAMG,EAASC,aAAaC,QAAQ9qB,KAAK6nB,YACzC,OAAO+C,EAAS/b,KAAKC,MAAM8b,GAAU,CAAC,CACxC,CAKA,gBAAMG,GACJ,UAEQ/qB,KAAKgrB,cAAchrB,KAAKmC,QAG9BnC,KAAKirB,oBAIP,CAAE,MAAOppB,GAEP,MAAMA,CACR,CACF,CAMA,mBAAMmpB,CAAc7oB,GAClB,IAEwB,oBAAXmoB,QAA0BA,OAAOC,cACpCvqB,KAAKkrB,oBAAoB/oB,GAG/BnC,KAAKmrB,mBAAmBhpB,EAE5B,CAAE,MAAON,GAEP7B,KAAKmrB,mBAAmBhpB,EAC1B,CACF,CAMA,yBAAM+oB,CAAoB/oB,GACxB,OAAO,IAAI6Y,QAAQ,CAACK,EAASH,KAC3BoP,OAAOC,QAAQG,KAAKjqB,IAAI,CAAE,CAACT,KAAK6nB,YAAa1lB,GAAU,KACjDmoB,OAAOK,QAAQhQ,UACjBO,EAAOoP,OAAOK,QAAQhQ,WAEtBU,OAIR,CAMA8P,kBAAAA,CAAmBhpB,GACjB0oB,aAAaO,QAAQprB,KAAK6nB,WAAYhZ,KAAKE,UAAU5M,GACvD,CAQAioB,YAAAA,CAAazC,EAAe0D,GAC1B,MAAMC,EAAS,CAAC,EAEhB,IAAK,MAAM1b,KAAO+X,EACkB,iBAAvBA,EAAc/X,IAAsBuF,MAAMC,QAAQuS,EAAc/X,IAGzE0b,EAAO1b,QAA2BC,IAApBwb,EAAWzb,GAAqByb,EAAWzb,GAAO+X,EAAc/X,GAF9E0b,EAAO1b,GAAO5P,KAAKoqB,aAAazC,EAAc/X,GAAMyb,EAAWzb,IAAQ,CAAC,GAM5E,OAAO0b,CACT,CAKAjB,cAAAA,GAEErqB,KAAKmC,OAAO6lB,GAAGC,0BAA4B5jB,KAAK8C,IAAI,EAAG9C,KAAKK,IAAI,EAAG1E,KAAKmC,OAAO6lB,GAAGC,4BAClFjoB,KAAKmC,OAAO6lB,GAAGE,kCAAoC7jB,KAAK8C,IAAI,EAAG9C,KAAKK,IAAI,EAAG1E,KAAKmC,OAAO6lB,GAAGE,oCAC1FloB,KAAKmC,OAAOomB,GAAG5J,cAAgBta,KAAK8C,IAAI,IAAM9C,KAAKK,IAAI,IAAO1E,KAAKmC,OAAOomB,GAAG5J,gBAC7E3e,KAAKmC,OAAOomB,GAAGE,kBAAoBpkB,KAAK8C,IAAI,EAAG9C,KAAKK,IAAI,GAAI1E,KAAKmC,OAAOomB,GAAGE,oBAC3EzoB,KAAKmC,OAAO4K,YAAYwD,mBAAqBlM,KAAK8C,IAAI,GAAI9C,KAAKK,IAAI,IAAM1E,KAAKmC,OAAO4K,YAAYwD,qBAG1E,CAAC,WAAY,YAAa,cAAe,gBAC5C+M,SAAStd,KAAKmC,OAAOomB,GAAGG,iBAC1C1oB,KAAKmC,OAAOomB,GAAGG,cAAgB,aAGb,CAAC,QAAS,OAAQ,QACrBpL,SAAStd,KAAKmC,OAAOomB,GAAGO,SACvC9oB,KAAKmC,OAAOomB,GAAGO,MAAQ,QAGF,CAAC,QAAS,SAAU,SACvBxL,SAAStd,KAAKmC,OAAOomB,GAAGxJ,YAC1C/e,KAAKmC,OAAOomB,GAAGxJ,SAAW,SAE9B,CAOAwM,SAAAA,CAAUC,EAAO,MACf,OAAKxrB,KAAK4C,cAKN4oB,EACKxrB,KAAKyrB,eAAezrB,KAAKmC,OAAQqpB,GAGnCxrB,KAAKmC,OAPHqpB,EAAOxrB,KAAKyrB,eAAezrB,KAAK2nB,cAAe6D,GAAQxrB,KAAK2nB,aAQvE,CAOA,eAAM+D,CAAUF,EAAM3d,GACpB,IAAK7N,KAAK4C,cACR,MAAM,IAAIK,MAAM,iCAGlBjD,KAAK2rB,eAAe3rB,KAAKmC,OAAQqpB,EAAM3d,GACvC7N,KAAKqqB,uBACCrqB,KAAK+qB,YACb,CAMA,kBAAMjb,CAAaqS,GACjB,IAAKniB,KAAK4C,cACR,MAAM,IAAIK,MAAM,iCAGlBjD,KAAKmC,OAASnC,KAAKoqB,aAAapqB,KAAKmC,OAAQggB,GAC7CniB,KAAKqqB,uBACCrqB,KAAK+qB,YACb,CAQAU,cAAAA,CAAeG,EAAKJ,GAClB,OAAOA,EAAK7W,MAAM,KAAKnN,OAAO,CAACuE,EAAS6D,IAAQ7D,GAAWA,EAAQ6D,GAAMgc,EAC3E,CAQAD,cAAAA,CAAeC,EAAKJ,EAAM3d,GACxB,MAAM6B,EAAO8b,EAAK7W,MAAM,KAClBkX,EAAUnc,EAAKoc,MACNpc,EAAKlI,OAAO,CAACuE,EAAS6D,KAC9B7D,EAAQ6D,IAAgC,iBAAjB7D,EAAQ6D,KAClC7D,EAAQ6D,GAAO,CAAC,GAEX7D,EAAQ6D,IACdgc,GAEIC,GAAWhe,CACpB,CAKA,iBAAMke,GACJ/rB,KAAKmC,OAAS,IAAKnC,KAAK2nB,qBAClB3nB,KAAK+qB,YAEb,CAMAiB,YAAAA,GACE,OAAOnd,KAAKE,UAAU/O,KAAKmC,OAAQ,KAAM,EAC3C,CAMA,kBAAM8pB,CAAaC,GACjB,IACE,MAAMC,EAAiBtd,KAAKC,MAAMod,GAClClsB,KAAKmC,OAASnC,KAAKoqB,aAAapqB,KAAK2nB,cAAewE,GACpDnsB,KAAKqqB,uBACCrqB,KAAK+qB,YAEb,CAAE,MAAOlpB,GAEP,MAAMA,CACR,CACF,CAMAuqB,iBAAAA,CAAkBvrB,GAChBb,KAAK8nB,gBAAgBuE,IAAIxrB,EAC3B,CAMAyrB,oBAAAA,CAAqBzrB,GACnBb,KAAK8nB,gBAAgBtmB,OAAOX,EAC9B,CAKAoqB,kBAAAA,GACEjrB,KAAK8nB,gBAAgBnY,QAAQ9O,IAC3B,IACEA,EAASb,KAAKmC,OAChB,CAAE,MAAON,GAET,GAEJ,CAMA0qB,gBAAAA,GACE,MAAO,CACLC,UAAWxsB,KAAKmC,OAAO6lB,GAAGtL,cAAgB1c,KAAKmC,OAAO6lB,GAAGrL,qBACzD8P,UAAWzsB,KAAKmC,OAAOomB,GAAGC,YAAcxoB,KAAKmC,OAAOomB,GAAGK,oBACvD8D,qBAAsB1sB,KAAKmC,OAAO4K,YAAYgc,uBAAyB/oB,KAAKmC,OAAO4K,YAAYmc,iBAC/FyD,qBAAsBld,OAAOmd,OAAO5sB,KAAKmC,OAAO+hB,eAAe2I,KAAKhf,IAAmB,IAAVA,GAEjF,CAKA/F,OAAAA,GACE9H,KAAK8nB,gBAAgBhmB,QACrB9B,KAAK4C,eAAgB,CAEvB,ECxCF,MAAMkqB,EAAM,IAhYZ,MACE/sB,WAAAA,GACEC,KAAK+sB,aAAe,KACpB/sB,KAAKgtB,aAAe,KACpBhtB,KAAKitB,aAAe,KACpBjtB,KAAKktB,sBAAwB,KAC7BltB,KAAKqQ,mBAAqB,IAAItF,EAC9B/K,KAAKmtB,cAAgB,IAAIzF,EAEzB1nB,KAAK4C,eAAgB,EACrB5C,KAAKgL,WAAY,EACjBhL,KAAKotB,oBAAsB,KAG3BptB,KAAKqtB,kBAAoB,IAAIC,QAG7BttB,KAAKutB,gBAAkBvtB,KAAKutB,gBAAgB9c,KAAKzQ,MACjDA,KAAKwtB,kBAAoBxtB,KAAKwtB,kBAAkB/c,KAAKzQ,MACrDA,KAAKwQ,gBAAkBxQ,KAAKwQ,gBAAgBC,KAAKzQ,MACjDA,KAAKytB,2BAA6BztB,KAAKytB,2BAA2Bhd,KAAKzQ,KACzE,CAKA,gBAAM6C,GACJ,UAIQ7C,KAAKmtB,cAAclD,aAGzBjqB,KAAKqQ,mBAAmB7L,QAGxBxE,KAAK0tB,4BAGC1tB,KAAK2tB,uBAGX3tB,KAAK4tB,8BAGC5tB,KAAK6tB,sBAEX7tB,KAAK4C,eAAgB,EACrB9C,EAAS2B,KAAKQ,EAIhB,CAAE,MAAOJ,GAGP,MADA/B,EAAS2B,KAAKQ,EAAqB,CAAEJ,MAAOA,EAAMsP,UAC5CtP,CACR,CACF,CAKA,0BAAM8rB,GAEJ3tB,KAAK+sB,aAAe,IAAI/c,EAGxBhQ,KAAKgtB,aAAe,IAAI1Q,QAClBtc,KAAKgtB,aAAanqB,aAGxB7C,KAAKitB,aAAe,IAAI1O,EAGxBve,KAAKktB,sBAAwB,IAAIlK,CACnC,CAKA0K,mBAAAA,GAEE5tB,EAASM,GAAG6B,EAA6BjC,KAAKwQ,iBAG9C1Q,EAASM,GAAG6B,EAAiCjC,KAAKytB,4BAGlD3tB,EAASM,GAAG6B,EAAsBP,IAEhC1B,KAAK8tB,kBAAkBpsB,EAAKG,SAI9B/B,EAASM,GAAG6B,EAAgCP,IAE1C1B,KAAK+tB,yBAAyBrsB,IAElC,CAKAksB,qBAAAA,GACmB,IAAII,iBAAiBhuB,KAAKutB,iBAElCnI,QAAQnF,SAASM,KAAM,CAC9B0N,WAAW,EACXC,SAAS,GAIb,CAMAX,eAAAA,CAAgBY,GACd,IAAK,MAAMC,KAAYD,EACC,cAAlBC,EAAS1f,MACX0f,EAASC,WAAW1e,QAAQ2e,IAC1B,GAAsB,IAAlBA,EAAKC,SAAgB,CACvB,IAAI7d,EAAe,KAGjBA,EADmB,UAAjB4d,EAAKE,QACQF,EAEAA,EAAKlM,cAAc,SAGhC1R,IAAiB1Q,KAAKqtB,kBAAkB7sB,IAAIkQ,IAC9C1Q,KAAKwtB,kBAAkB9c,EAE3B,GAIR,CAKA,yBAAMmd,GACJ,MAAMY,EAAgBxO,SAASmC,cAAc,SACzCqM,IAAkBzuB,KAAKqtB,kBAAkB7sB,IAAIiuB,UAEzCzuB,KAAKwtB,kBAAkBiB,EAEjC,CAMA,uBAAMjB,CAAkB9c,GACtB,IACE,GAAI1Q,KAAKqtB,kBAAkB7sB,IAAIkQ,GAC7B,OAMF1Q,KAAKqtB,kBAAkBhB,IAAI3b,GAC3B1Q,KAAKotB,oBAAsB1c,EAGvBA,EAAage,WAAa,SACtB,IAAI1T,QAASK,IACjB3K,EAAaU,iBAAiB,UAAWiK,EAAS,CAAEva,MAAM,YAKxDd,KAAK+sB,aAAalqB,WAAW6N,SAG7B1Q,KAAKktB,sBAAsBrqB,WAAW6N,GAG5C1Q,KAAKiR,yBAAyBP,EAIhC,CAAE,MAAO7O,GAEP/B,EAAS2B,KAAKQ,EAAqB,CAAEJ,MAAOA,EAAMsP,SACpD,CACF,CAMAF,wBAAAA,CAAyBP,GACvBA,EAAaU,iBAAiB,OAAQ,KAEpCpR,KAAKwE,UAGPkM,EAAaU,iBAAiB,QAAS,KAErCpR,KAAKwlB,UAGP9U,EAAaU,iBAAiB,QAAS,KAErCpR,KAAKkN,QAET,CAKA1I,KAAAA,GACOxE,KAAK4C,gBAAiB5C,KAAKgL,YAMhChL,KAAKgL,WAAY,EACjBhL,KAAK+sB,aAAaxb,kBAClBvR,KAAKktB,sBAAsB1oB,QAE3B1E,EAAS2B,KAAKQ,GAChB,CAKAujB,KAAAA,GACOxlB,KAAKgL,YAMVhL,KAAK+sB,aAAavb,iBAClBxR,KAAKktB,sBAAsB1H,QAC7B,CAKAtY,IAAAA,GACOlN,KAAKgL,YAMVhL,KAAKgL,WAAY,EACjBhL,KAAK+sB,aAAavb,iBAClBxR,KAAKktB,sBAAsBhgB,OAC3BlN,KAAKitB,aAAahL,iBAElBniB,EAAS2B,KAAKQ,GAChB,CAMA,qBAAMuO,CAAgBxN,GACpB,IAEEhD,KAAKktB,sBAAsBzF,oBAAoBzkB,GAG3ChD,KAAKgtB,cAAgBhqB,EAAU6O,iBAC3B7R,KAAKgtB,aAAa1X,SAAStS,EAAU6O,WAI7C7R,KAAKqQ,mBAAmBjD,uBAE1B,CAAE,MAAOvL,GAET,CACF,CAMA4rB,0BAAAA,CAA2B/U,GACzB,IAEE,GAAIA,EAAOzD,aAAeyD,EAAOzD,YAAYrU,OAAS,EAAG,CACvD,MAAM+tB,EAAgBjW,EAAOzD,YAAY,GAErC0Z,EAAclY,WAAa,IAC7BzW,KAAKitB,aAAatM,UAChBgO,EAAcnY,UACdmY,EAAclY,WACd,CAAE2P,EAAG,GAAIW,EAAG,IAGlB,CAGA/mB,KAAKqQ,mBAAmB7C,mBAE1B,CAAE,MAAO3L,GAET,CACF,CAMAisB,iBAAAA,CAAkBjsB,GAEhB7B,KAAKitB,aAAatM,UAAU,OAAQ,EAAK,CAAEyF,EAAG,GAAIW,EAAG,KAGrDhV,WAAW,MACJ/R,KAAKgL,WAAahL,KAAKotB,qBAC1BptB,KAAKwtB,kBAAkBxtB,KAAKotB,sBAE7B,IACL,CAMAW,wBAAAA,CAAyBrsB,GAInBA,EAAKkL,SAAW,IAElB5M,KAAK+sB,aAAajd,aAAa,CAAES,mBAAoB,KAEzD,CAMA3I,SAAAA,GAAY,IAAAgnB,EACV,MAAO,CACLhsB,cAAe5C,KAAK4C,cACpBoI,UAAWhL,KAAKgL,UAChB+hB,aAA+B,QAAnB6B,EAAE5uB,KAAK+sB,oBAAY,IAAA6B,OAAA,EAAjBA,EAAmBrc,sBACjCxF,YAAa/M,KAAKqQ,mBAAmBtO,WACrCI,OAAQnC,KAAKmtB,cAAc5B,YAE/B,CAKAzjB,OAAAA,GACE9H,KAAKkN,OAEDlN,KAAK+sB,cACP/sB,KAAK+sB,aAAajlB,UAGhB9H,KAAKgtB,cACPhtB,KAAKgtB,aAAallB,UAGhB9H,KAAKktB,uBACPltB,KAAKktB,sBAAsBplB,UAGzB9H,KAAKitB,cACPjtB,KAAKitB,aAAanlB,UAGpBhI,EAASgC,OAGX,GAO0B,YAAxBme,SAASyO,WACXzO,SAAS7O,iBAAiB,mBAAoB,IAAM0b,EAAIjqB,cAExDiqB,EAAIjqB,aAINmC,OAAO6pB,yBAA2B/B,C,mDCxZ9BgC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBnf,IAAjBof,EACH,OAAOA,EAAaC,QAGrB,IAAIC,EAASL,EAAyBE,GAAY,CACjD7O,GAAI6O,EACJnT,QAAQ,EACRqT,QAAS,CAAC,GAUX,OANAE,EAAoBJ,GAAUK,KAAKF,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAG3EI,EAAOtT,QAAS,EAGTsT,EAAOD,OACf,CAGAH,EAAoBO,EAAIF,EC5BxBL,EAAoBQ,KAAO,WAC1B,MAAM,IAAItsB,MAAM,iCACjB,ECFA8rB,EAAoBS,KAAO,CAAC,EhBAxB3vB,EAAW,GACfkvB,EAAoBU,EAAI,CAAC/W,EAAQgX,EAAUC,EAAI5uB,KAC9C,IAAG2uB,EAAH,CAMA,IAAIE,EAAenkB,IACnB,IAAS7J,EAAI,EAAGA,EAAI/B,EAASe,OAAQgB,IAAK,CAGzC,IAFA,IAAK8tB,EAAUC,EAAI5uB,GAAYlB,EAAS+B,GACpCiuB,GAAY,EACPjpB,EAAI,EAAGA,EAAI8oB,EAAS9uB,OAAQgG,MACpB,EAAX7F,GAAsB6uB,GAAgB7uB,IAAa0O,OAAOC,KAAKqf,EAAoBU,GAAGK,MAAOlgB,GAASmf,EAAoBU,EAAE7f,GAAK8f,EAAS9oB,KAC9I8oB,EAASnuB,OAAOqF,IAAK,IAErBipB,GAAY,EACT9uB,EAAW6uB,IAAcA,EAAe7uB,IAG7C,GAAG8uB,EAAW,CACbhwB,EAAS0B,OAAOK,IAAK,GACrB,IAAIiiB,EAAI8L,SACE9f,IAANgU,IAAiBnL,EAASmL,EAC/B,CACD,CACA,OAAOnL,CAnBP,CAJC3X,EAAWA,GAAY,EACvB,IAAI,IAAIa,EAAI/B,EAASe,OAAQgB,EAAI,GAAK/B,EAAS+B,EAAI,GAAG,GAAKb,EAAUa,IAAK/B,EAAS+B,GAAK/B,EAAS+B,EAAI,GACrG/B,EAAS+B,GAAK,CAAC8tB,EAAUC,EAAI5uB,IiBJ/BguB,EAAoBtpB,EAAK0pB,IACxB,IAAIY,EAASZ,GAAUA,EAAOa,WAC7B,IAAOb,EAAiB,QACxB,IAAM,EAEP,OADAJ,EAAoBkB,EAAEF,EAAQ,CAAE7uB,EAAG6uB,IAC5BA,GCLRhB,EAAoBkB,EAAI,CAACf,EAASgB,KACjC,IAAI,IAAItgB,KAAOsgB,EACXnB,EAAoBoB,EAAED,EAAYtgB,KAASmf,EAAoBoB,EAAEjB,EAAStf,IAC5EH,OAAO2gB,eAAelB,EAAStf,EAAK,CAAEygB,YAAY,EAAM1vB,IAAKuvB,EAAWtgB,MCJ3Emf,EAAoBjL,EAAI,WACvB,GAA0B,iBAAfwM,WAAyB,OAAOA,WAC3C,IACC,OAAOtwB,MAAQ,IAAIuwB,SAAS,cAAb,EAChB,CAAE,MAAOC,GACR,GAAsB,iBAAXxrB,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxB+pB,EAAoBoB,EAAI,CAACvE,EAAK6E,IAAUhhB,OAAOihB,UAAUC,eAAetB,KAAKzD,EAAK6E,GCClF1B,EAAoBlL,EAAKqL,IACH,oBAAX0B,QAA0BA,OAAOC,aAC1CphB,OAAO2gB,eAAelB,EAAS0B,OAAOC,YAAa,CAAEhjB,MAAO,WAE7D4B,OAAO2gB,eAAelB,EAAS,aAAc,CAAErhB,OAAO,KCLvDkhB,EAAoB+B,IAAO3B,IAC1BA,EAAO4B,MAAQ,GACV5B,EAAO6B,WAAU7B,EAAO6B,SAAW,IACjC7B,GCHRJ,EAAoBnoB,EAAI,I,MCKxB,IAAIqqB,EAAkB,CACrB,IAAK,GAaNlC,EAAoBU,EAAE7oB,EAAKsqB,GAA0C,IAA7BD,EAAgBC,GAGxD,IAAIC,EAAuB,CAACC,EAA4B1vB,KACvD,IAGIstB,EAAUkC,GAHTxB,EAAU2B,EAAa1G,GAAWjpB,EAGhBE,EAAI,EAC3B,GAAG8tB,EAAS7C,KAAM1M,GAAgC,IAAxB8Q,EAAgB9Q,IAAa,CACtD,IAAI6O,KAAYqC,EACZtC,EAAoBoB,EAAEkB,EAAarC,KACrCD,EAAoBO,EAAEN,GAAYqC,EAAYrC,IAGhD,GAAGrE,EAAS,IAAIjS,EAASiS,EAAQoE,EAClC,CAEA,IADGqC,GAA4BA,EAA2B1vB,GACrDE,EAAI8tB,EAAS9uB,OAAQgB,IACzBsvB,EAAUxB,EAAS9tB,GAChBmtB,EAAoBoB,EAAEc,EAAiBC,IAAYD,EAAgBC,IACrED,EAAgBC,GAAS,KAE1BD,EAAgBC,GAAW,EAE5B,OAAOnC,EAAoBU,EAAE/W,IAG1B4Y,EAAqBC,KAA8C,wCAAIA,KAA8C,yCAAK,GAC9HD,EAAmB3hB,QAAQwhB,EAAqB1gB,KAAK,KAAM,IAC3D6gB,EAAmBtwB,KAAOmwB,EAAqB1gB,KAAK,KAAM6gB,EAAmBtwB,KAAKyP,KAAK6gB,G,KC7CvF,IAAIE,EAAsBzC,EAAoBU,OAAE5f,EAAW,CAAC,IAAI,KAAM,IAAOkf,EAAoB,OACjGyC,EAAsBzC,EAAoBU,EAAE+B,E", "sources": ["webpack://youtube-aural-visual-bridge/webpack/runtime/chunk loaded", "webpack://youtube-aural-visual-bridge/./src/core/EventBus.js", "webpack://youtube-aural-visual-bridge/./src/audio/FeatureExtractor.js", "webpack://youtube-aural-visual-bridge/./src/audio/AudioProcessor.js", "webpack://youtube-aural-visual-bridge/./src/utils/PerformanceMonitor.js", "webpack://youtube-aural-visual-bridge/./src/core/AudioManager.js", "webpack://youtube-aural-visual-bridge/./src/ai/YAMNetClassifier.js", "webpack://youtube-aural-visual-bridge/./src/ai/SpeechCommandDetector.js", "webpack://youtube-aural-visual-bridge/./src/ai/ModelManager.js", "webpack://youtube-aural-visual-bridge/./src/core/AIClassifier.js", "webpack://youtube-aural-visual-bridge/./src/ui/LabelManager.js", "webpack://youtube-aural-visual-bridge/./src/ui/VisualizationRenderer.js", "webpack://youtube-aural-visual-bridge/./src/api/ConfigManager.js", "webpack://youtube-aural-visual-bridge/./src/content.js", "webpack://youtube-aural-visual-bridge/webpack/bootstrap", "webpack://youtube-aural-visual-bridge/webpack/runtime/amd define", "webpack://youtube-aural-visual-bridge/webpack/runtime/amd options", "webpack://youtube-aural-visual-bridge/webpack/runtime/compat get default export", "webpack://youtube-aural-visual-bridge/webpack/runtime/define property getters", "webpack://youtube-aural-visual-bridge/webpack/runtime/global", "webpack://youtube-aural-visual-bridge/webpack/runtime/hasOwnProperty shorthand", "webpack://youtube-aural-visual-bridge/webpack/runtime/make namespace object", "webpack://youtube-aural-visual-bridge/webpack/runtime/node module decorator", "webpack://youtube-aural-visual-bridge/webpack/runtime/runtimeId", "webpack://youtube-aural-visual-bridge/webpack/runtime/jsonp chunk loading", "webpack://youtube-aural-visual-bridge/webpack/startup"], "sourcesContent": ["var deferred = [];\n__webpack_require__.O = (result, chunkIds, fn, priority) => {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar [chunkIds, fn, priority] = deferred[i];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "/**\n * 事件总线 - 用于模块间通信\n * 实现发布-订阅模式，解耦各个模块\n */\nclass EventBus {\n  constructor() {\n    this.events = new Map();\n    this.maxListeners = 100; // 防止内存泄漏\n  }\n\n  /**\n   * 订阅事件\n   * @param {string} event - 事件名称\n   * @param {Function} callback - 回调函数\n   * @param {Object} options - 选项\n   */\n  on(event, callback, options = {}) {\n    if (!this.events.has(event)) {\n      this.events.set(event, []);\n    }\n\n    const listeners = this.events.get(event);\n    \n    // 检查监听器数量限制\n    if (listeners.length >= this.maxListeners) {\n      console.warn(`EventBus: Too many listeners for event \"${event}\"`);\n      return;\n    }\n\n    const listener = {\n      callback,\n      once: options.once || false,\n      priority: options.priority || 0\n    };\n\n    listeners.push(listener);\n    \n    // 按优先级排序\n    listeners.sort((a, b) => b.priority - a.priority);\n  }\n\n  /**\n   * 订阅一次性事件\n   * @param {string} event - 事件名称\n   * @param {Function} callback - 回调函数\n   */\n  once(event, callback) {\n    this.on(event, callback, { once: true });\n  }\n\n  /**\n   * 取消订阅事件\n   * @param {string} event - 事件名称\n   * @param {Function} callback - 回调函数\n   */\n  off(event, callback) {\n    if (!this.events.has(event)) {\n      return;\n    }\n\n    const listeners = this.events.get(event);\n    const index = listeners.findIndex(listener => listener.callback === callback);\n    \n    if (index !== -1) {\n      listeners.splice(index, 1);\n    }\n\n    // 如果没有监听器了，删除事件\n    if (listeners.length === 0) {\n      this.events.delete(event);\n    }\n  }\n\n  /**\n   * 发布事件\n   * @param {string} event - 事件名称\n   * @param {*} data - 事件数据\n   */\n  emit(event, data) {\n    if (!this.events.has(event)) {\n      return;\n    }\n\n    const listeners = this.events.get(event);\n    const toRemove = [];\n\n    // 执行所有监听器\n    for (let i = 0; i < listeners.length; i++) {\n      const listener = listeners[i];\n      \n      try {\n        listener.callback(data);\n        \n        // 标记一次性监听器待删除\n        if (listener.once) {\n          toRemove.push(i);\n        }\n      } catch (error) {\n        console.error(`EventBus: Error in listener for event \"${event}\":`, error);\n      }\n    }\n\n    // 删除一次性监听器\n    for (let i = toRemove.length - 1; i >= 0; i--) {\n      listeners.splice(toRemove[i], 1);\n    }\n  }\n\n  /**\n   * 清除所有事件监听器\n   */\n  clear() {\n    this.events.clear();\n  }\n\n  /**\n   * 获取事件统计信息\n   */\n  getStats() {\n    const stats = {};\n    for (const [event, listeners] of this.events) {\n      stats[event] = listeners.length;\n    }\n    return stats;\n  }\n}\n\n// 创建全局事件总线实例\nconst eventBus = new EventBus();\n\n// 定义事件常量\nexport const EVENTS = {\n  // 音频相关事件\n  AUDIO_INITIALIZED: 'audio:initialized',\n  AUDIO_STARTED: 'audio:started',\n  AUDIO_STOPPED: 'audio:stopped',\n  AUDIO_DATA_AVAILABLE: 'audio:data-available',\n  AUDIO_ERROR: 'audio:error',\n\n  // AI分类相关事件\n  AI_MODEL_LOADED: 'ai:model-loaded',\n  AI_MODEL_ERROR: 'ai:model-error',\n  AI_CLASSIFICATION_RESULT: 'ai:classification-result',\n  AI_PROCESSING_START: 'ai:processing-start',\n  AI_PROCESSING_END: 'ai:processing-end',\n\n  // UI相关事件\n  UI_LABEL_SHOW: 'ui:label-show',\n  UI_LABEL_HIDE: 'ui:label-hide',\n  UI_CONFIG_CHANGED: 'ui:config-changed',\n  UI_PERFORMANCE_WARNING: 'ui:performance-warning',\n\n  // 系统相关事件\n  SYSTEM_READY: 'system:ready',\n  SYSTEM_ERROR: 'system:error',\n  SYSTEM_PERFORMANCE_UPDATE: 'system:performance-update'\n};\n\nexport default eventBus;\n", "/**\n * 特征提取器 - 从音频数据中提取AI模型所需的特征\n * 主要提取梅尔频谱图等特征用于YAMNet模型\n */\nexport class FeatureExtractor {\n  constructor(config = {}) {\n    this.config = {\n      sampleRate: 16000,\n      frameLength: 1024,\n      hopLength: 512,\n      melBins: 64,\n      fftSize: 2048,\n      minFreq: 125.0,\n      maxFreq: 7500.0,\n      ...config\n    };\n    \n    this.melFilterBank = null;\n    this.isInitialized = false;\n  }\n\n  /**\n   * 初始化特征提取器\n   */\n  async initialize() {\n    try {\n      // 创建梅尔滤波器组\n      this.melFilterBank = this.createMelFilterBank();\n      this.isInitialized = true;\n      console.log('✅ FeatureExtractor: Initialized successfully');\n    } catch (error) {\n      console.error('❌ FeatureExtractor: Initialization failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 提取音频特征\n   * @param {Object} audioData - 预处理后的音频数据\n   * @returns {Object} 提取的特征\n   */\n  async extract(audioData) {\n    if (!this.isInitialized) {\n      throw new Error('FeatureExtractor not initialized');\n    }\n\n    try {\n      const features = {};\n      \n      // 1. 计算短时傅里叶变换 (STFT)\n      const stft = this.computeSTFT(audioData.timeData);\n      features.stft = stft;\n      \n      // 2. 计算功率谱\n      const powerSpectrum = this.computePowerSpectrum(stft);\n      features.powerSpectrum = powerSpectrum;\n      \n      // 3. 计算梅尔频谱图\n      const melSpectrogram = this.computeMelSpectrogram(powerSpectrum);\n      features.melSpectrogram = melSpectrogram;\n      \n      // 4. 计算对数梅尔频谱图\n      const logMelSpectrogram = this.computeLogMelSpectrogram(melSpectrogram);\n      features.logMelSpectrogram = logMelSpectrogram;\n      \n      // 5. 计算MFCC（可选）\n      const mfcc = this.computeMFCC(logMelSpectrogram);\n      features.mfcc = mfcc;\n      \n      // 6. 计算其他特征\n      features.spectralCentroid = this.computeSpectralCentroid(powerSpectrum);\n      features.spectralRolloff = this.computeSpectralRolloff(powerSpectrum);\n      features.zeroCrossingRate = this.computeZeroCrossingRate(audioData.timeData);\n      \n      return features;\n    } catch (error) {\n      console.error('FeatureExtractor: Feature extraction error:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 计算短时傅里叶变换\n   * @param {Float32Array} timeData - 时域数据\n   * @returns {Array} STFT结果\n   */\n  computeSTFT(timeData) {\n    const frameLength = this.config.frameLength;\n    const hopLength = this.config.hopLength;\n    const numFrames = Math.floor((timeData.length - frameLength) / hopLength) + 1;\n    \n    const stft = [];\n    \n    for (let frame = 0; frame < numFrames; frame++) {\n      const start = frame * hopLength;\n      const end = Math.min(start + frameLength, timeData.length);\n      \n      // 提取帧数据\n      const frameData = new Float32Array(frameLength);\n      for (let i = 0; i < frameLength; i++) {\n        frameData[i] = (start + i < timeData.length) ? timeData[start + i] : 0;\n      }\n      \n      // 应用窗函数\n      const windowed = this.applyHannWindow(frameData);\n      \n      // 计算FFT\n      const fftResult = this.computeFFT(windowed);\n      stft.push(fftResult);\n    }\n    \n    return stft;\n  }\n\n  /**\n   * 应用汉宁窗\n   * @param {Float32Array} data - 输入数据\n   * @returns {Float32Array} 应用窗函数后的数据\n   */\n  applyHannWindow(data) {\n    const windowed = new Float32Array(data.length);\n    for (let i = 0; i < data.length; i++) {\n      const window = 0.5 * (1 - Math.cos(2 * Math.PI * i / (data.length - 1)));\n      windowed[i] = data[i] * window;\n    }\n    return windowed;\n  }\n\n  /**\n   * 计算FFT（简化实现）\n   * @param {Float32Array} data - 输入数据\n   * @returns {Object} FFT结果（实部和虚部）\n   */\n  computeFFT(data) {\n    // 这里使用简化的FFT实现\n    // 实际应用中建议使用优化的FFT库如fft.js\n    const N = data.length;\n    const real = new Float32Array(N);\n    const imag = new Float32Array(N);\n    \n    // 简单的DFT实现（性能较低，仅用于演示）\n    for (let k = 0; k < N / 2; k++) {\n      let realSum = 0;\n      let imagSum = 0;\n      \n      for (let n = 0; n < N; n++) {\n        const angle = -2 * Math.PI * k * n / N;\n        realSum += data[n] * Math.cos(angle);\n        imagSum += data[n] * Math.sin(angle);\n      }\n      \n      real[k] = realSum;\n      imag[k] = imagSum;\n    }\n    \n    return { real, imag };\n  }\n\n  /**\n   * 计算功率谱\n   * @param {Array} stft - STFT结果\n   * @returns {Array} 功率谱\n   */\n  computePowerSpectrum(stft) {\n    return stft.map(frame => {\n      const power = new Float32Array(frame.real.length);\n      for (let i = 0; i < frame.real.length; i++) {\n        power[i] = frame.real[i] * frame.real[i] + frame.imag[i] * frame.imag[i];\n      }\n      return power;\n    });\n  }\n\n  /**\n   * 创建梅尔滤波器组\n   * @returns {Array} 梅尔滤波器组\n   */\n  createMelFilterBank() {\n    const melBins = this.config.melBins;\n    const fftBins = this.config.fftSize / 2 + 1;\n    const sampleRate = this.config.sampleRate;\n    const minFreq = this.config.minFreq;\n    const maxFreq = this.config.maxFreq;\n    \n    // 转换为梅尔刻度\n    const melMin = this.hzToMel(minFreq);\n    const melMax = this.hzToMel(maxFreq);\n    \n    // 创建梅尔刻度上的等间距点\n    const melPoints = [];\n    for (let i = 0; i <= melBins + 1; i++) {\n      melPoints.push(melMin + (melMax - melMin) * i / (melBins + 1));\n    }\n    \n    // 转换回Hz\n    const hzPoints = melPoints.map(mel => this.melToHz(mel));\n    \n    // 转换为FFT bin索引\n    const binPoints = hzPoints.map(hz => Math.floor(hz * this.config.fftSize / sampleRate));\n    \n    // 创建滤波器组\n    const filterBank = [];\n    for (let i = 0; i < melBins; i++) {\n      const filter = new Float32Array(fftBins);\n      const left = binPoints[i];\n      const center = binPoints[i + 1];\n      const right = binPoints[i + 2];\n      \n      // 三角滤波器\n      for (let j = left; j < center; j++) {\n        if (center > left) {\n          filter[j] = (j - left) / (center - left);\n        }\n      }\n      \n      for (let j = center; j < right; j++) {\n        if (right > center) {\n          filter[j] = (right - j) / (right - center);\n        }\n      }\n      \n      filterBank.push(filter);\n    }\n    \n    return filterBank;\n  }\n\n  /**\n   * Hz转梅尔刻度\n   * @param {number} hz - 频率(Hz)\n   * @returns {number} 梅尔刻度值\n   */\n  hzToMel(hz) {\n    return 2595 * Math.log10(1 + hz / 700);\n  }\n\n  /**\n   * 梅尔刻度转Hz\n   * @param {number} mel - 梅尔刻度值\n   * @returns {number} 频率(Hz)\n   */\n  melToHz(mel) {\n    return 700 * (Math.pow(10, mel / 2595) - 1);\n  }\n\n  /**\n   * 计算梅尔频谱图\n   * @param {Array} powerSpectrum - 功率谱\n   * @returns {Array} 梅尔频谱图\n   */\n  computeMelSpectrogram(powerSpectrum) {\n    return powerSpectrum.map(frame => {\n      const melFrame = new Float32Array(this.config.melBins);\n      \n      for (let i = 0; i < this.config.melBins; i++) {\n        let sum = 0;\n        const filter = this.melFilterBank[i];\n        \n        for (let j = 0; j < Math.min(frame.length, filter.length); j++) {\n          sum += frame[j] * filter[j];\n        }\n        \n        melFrame[i] = sum;\n      }\n      \n      return melFrame;\n    });\n  }\n\n  /**\n   * 计算对数梅尔频谱图\n   * @param {Array} melSpectrogram - 梅尔频谱图\n   * @returns {Array} 对数梅尔频谱图\n   */\n  computeLogMelSpectrogram(melSpectrogram) {\n    return melSpectrogram.map(frame => {\n      const logFrame = new Float32Array(frame.length);\n      for (let i = 0; i < frame.length; i++) {\n        // 添加小的常数避免log(0)\n        logFrame[i] = Math.log(Math.max(frame[i], 1e-10));\n      }\n      return logFrame;\n    });\n  }\n\n  /**\n   * 计算MFCC\n   * @param {Array} logMelSpectrogram - 对数梅尔频谱图\n   * @returns {Array} MFCC特征\n   */\n  computeMFCC(logMelSpectrogram) {\n    const numCoeffs = 13; // 通常使用13个MFCC系数\n    \n    return logMelSpectrogram.map(frame => {\n      const mfcc = new Float32Array(numCoeffs);\n      \n      // 简化的DCT实现\n      for (let i = 0; i < numCoeffs; i++) {\n        let sum = 0;\n        for (let j = 0; j < frame.length; j++) {\n          sum += frame[j] * Math.cos(Math.PI * i * (j + 0.5) / frame.length);\n        }\n        mfcc[i] = sum;\n      }\n      \n      return mfcc;\n    });\n  }\n\n  /**\n   * 计算频谱质心\n   * @param {Array} powerSpectrum - 功率谱\n   * @returns {Array} 频谱质心\n   */\n  computeSpectralCentroid(powerSpectrum) {\n    return powerSpectrum.map(frame => {\n      let weightedSum = 0;\n      let totalPower = 0;\n      \n      for (let i = 0; i < frame.length; i++) {\n        const freq = i * this.config.sampleRate / (2 * frame.length);\n        weightedSum += freq * frame[i];\n        totalPower += frame[i];\n      }\n      \n      return totalPower > 0 ? weightedSum / totalPower : 0;\n    });\n  }\n\n  /**\n   * 计算频谱滚降点\n   * @param {Array} powerSpectrum - 功率谱\n   * @returns {Array} 频谱滚降点\n   */\n  computeSpectralRolloff(powerSpectrum, rolloffPercent = 0.85) {\n    return powerSpectrum.map(frame => {\n      const totalPower = frame.reduce((sum, val) => sum + val, 0);\n      const threshold = totalPower * rolloffPercent;\n      \n      let cumulativePower = 0;\n      for (let i = 0; i < frame.length; i++) {\n        cumulativePower += frame[i];\n        if (cumulativePower >= threshold) {\n          return i * this.config.sampleRate / (2 * frame.length);\n        }\n      }\n      \n      return this.config.sampleRate / 2; // 奈奎斯特频率\n    });\n  }\n\n  /**\n   * 计算过零率\n   * @param {Float32Array} timeData - 时域数据\n   * @returns {number} 过零率\n   */\n  computeZeroCrossingRate(timeData) {\n    let crossings = 0;\n    for (let i = 1; i < timeData.length; i++) {\n      if ((timeData[i] >= 0) !== (timeData[i - 1] >= 0)) {\n        crossings++;\n      }\n    }\n    return crossings / (timeData.length - 1);\n  }\n\n  /**\n   * 获取提取器状态\n   * @returns {Object} 提取器状态\n   */\n  getStatus() {\n    return {\n      isInitialized: this.isInitialized,\n      config: this.config,\n      melFilterBankSize: this.melFilterBank ? this.melFilterBank.length : 0\n    };\n  }\n\n  /**\n   * 销毁提取器\n   */\n  dispose() {\n    this.melFilterBank = null;\n    this.isInitialized = false;\n    console.log('🎵 FeatureExtractor: Disposed');\n  }\n}\n", "/**\n * 音频处理器 - 负责音频数据的预处理和特征提取\n * 将Web Audio API数据转换为AI模型可用的格式\n */\nimport { FeatureExtractor } from './FeatureExtractor.js';\n\nexport class AudioProcessor {\n  constructor(audioContext, config = {}) {\n    this.audioContext = audioContext;\n    this.config = {\n      sampleRate: 16000,        // YAMNet标准采样率\n      frameLength: 1024,        // 帧长度\n      hopLength: 512,           // 跳跃长度\n      windowType: 'hann',       // 窗函数类型\n      melBins: 64,              // 梅尔滤波器组数量\n      fftSize: 2048,            // FFT大小\n      ...config\n    };\n    \n    this.featureExtractor = new FeatureExtractor(this.config);\n    this.audioBuffer = [];\n    this.isInitialized = false;\n  }\n\n  /**\n   * 初始化音频处理器\n   */\n  async initialize() {\n    try {\n      await this.featureExtractor.initialize();\n      this.isInitialized = true;\n      console.log('✅ AudioProcessor: Initialized successfully');\n    } catch (error) {\n      console.error('❌ AudioProcessor: Initialization failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 处理音频数据\n   * @param {Object} audioData - 原始音频数据\n   * @returns {Object} 处理后的音频特征\n   */\n  async process(audioData) {\n    if (!this.isInitialized) {\n      throw new Error('AudioProcessor not initialized');\n    }\n\n    try {\n      // 1. 预处理音频数据\n      const preprocessed = this.preprocess(audioData);\n      \n      // 2. 提取特征\n      const features = await this.featureExtractor.extract(preprocessed);\n      \n      // 3. 后处理特征\n      const postprocessed = this.postprocess(features);\n      \n      return {\n        raw: audioData,\n        preprocessed,\n        features: postprocessed,\n        timestamp: Date.now()\n      };\n    } catch (error) {\n      console.error('AudioProcessor: Processing error:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 预处理音频数据\n   * @param {Object} audioData - 原始音频数据\n   * @returns {Object} 预处理后的数据\n   */\n  preprocess(audioData) {\n    // 1. 转换频域数据为时域数据（如果需要）\n    const timeData = this.convertToTimeData(audioData);\n    \n    // 2. 重采样到目标采样率\n    const resampled = this.resample(timeData, audioData.sampleRate, this.config.sampleRate);\n    \n    // 3. 归一化\n    const normalized = this.normalize(resampled);\n    \n    // 4. 应用窗函数\n    const windowed = this.applyWindow(normalized);\n    \n    // 5. 预加重（可选）\n    const preemphasized = this.preemphasis(windowed);\n    \n    return {\n      timeData: preemphasized,\n      sampleRate: this.config.sampleRate,\n      frameLength: this.config.frameLength,\n      hopLength: this.config.hopLength\n    };\n  }\n\n  /**\n   * 转换频域数据为时域数据\n   * @param {Object} audioData - 音频数据\n   * @returns {Float32Array} 时域数据\n   */\n  convertToTimeData(audioData) {\n    // 如果已经有时域数据，直接使用\n    if (audioData.timeDomain) {\n      // 转换Uint8Array到Float32Array并归一化到[-1, 1]\n      const timeData = new Float32Array(audioData.timeDomain.length);\n      for (let i = 0; i < audioData.timeDomain.length; i++) {\n        timeData[i] = (audioData.timeDomain[i] - 128) / 128.0;\n      }\n      return timeData;\n    }\n    \n    // 如果只有频域数据，使用IFFT转换（简化实现）\n    // 实际应用中可能需要更复杂的重构\n    const freqData = audioData.frequency;\n    const timeData = new Float32Array(freqData.length);\n    \n    // 简单的频域到时域转换（这里使用简化方法）\n    for (let i = 0; i < freqData.length; i++) {\n      timeData[i] = (freqData[i] - 128) / 128.0;\n    }\n    \n    return timeData;\n  }\n\n  /**\n   * 重采样音频数据\n   * @param {Float32Array} data - 输入数据\n   * @param {number} inputRate - 输入采样率\n   * @param {number} outputRate - 输出采样率\n   * @returns {Float32Array} 重采样后的数据\n   */\n  resample(data, inputRate, outputRate) {\n    if (inputRate === outputRate) {\n      return data;\n    }\n    \n    const ratio = inputRate / outputRate;\n    const outputLength = Math.floor(data.length / ratio);\n    const output = new Float32Array(outputLength);\n    \n    // 简单的线性插值重采样\n    for (let i = 0; i < outputLength; i++) {\n      const srcIndex = i * ratio;\n      const srcIndexFloor = Math.floor(srcIndex);\n      const srcIndexCeil = Math.min(srcIndexFloor + 1, data.length - 1);\n      const fraction = srcIndex - srcIndexFloor;\n      \n      output[i] = data[srcIndexFloor] * (1 - fraction) + data[srcIndexCeil] * fraction;\n    }\n    \n    return output;\n  }\n\n  /**\n   * 归一化音频数据\n   * @param {Float32Array} data - 输入数据\n   * @returns {Float32Array} 归一化后的数据\n   */\n  normalize(data) {\n    // 计算RMS\n    let rms = 0;\n    for (let i = 0; i < data.length; i++) {\n      rms += data[i] * data[i];\n    }\n    rms = Math.sqrt(rms / data.length);\n    \n    // 避免除零\n    if (rms < 1e-8) {\n      return data;\n    }\n    \n    // 归一化到[-1, 1]\n    const normalized = new Float32Array(data.length);\n    const scale = 0.95 / rms; // 留一点余量避免削波\n    \n    for (let i = 0; i < data.length; i++) {\n      normalized[i] = Math.max(-1, Math.min(1, data[i] * scale));\n    }\n    \n    return normalized;\n  }\n\n  /**\n   * 应用窗函数\n   * @param {Float32Array} data - 输入数据\n   * @returns {Float32Array} 应用窗函数后的数据\n   */\n  applyWindow(data) {\n    const windowed = new Float32Array(data.length);\n    \n    switch (this.config.windowType) {\n      case 'hann':\n        for (let i = 0; i < data.length; i++) {\n          const window = 0.5 * (1 - Math.cos(2 * Math.PI * i / (data.length - 1)));\n          windowed[i] = data[i] * window;\n        }\n        break;\n      case 'hamming':\n        for (let i = 0; i < data.length; i++) {\n          const window = 0.54 - 0.46 * Math.cos(2 * Math.PI * i / (data.length - 1));\n          windowed[i] = data[i] * window;\n        }\n        break;\n      default:\n        // 矩形窗（无窗函数）\n        return data;\n    }\n    \n    return windowed;\n  }\n\n  /**\n   * 预加重滤波\n   * @param {Float32Array} data - 输入数据\n   * @returns {Float32Array} 预加重后的数据\n   */\n  preemphasis(data, alpha = 0.97) {\n    const output = new Float32Array(data.length);\n    output[0] = data[0];\n    \n    for (let i = 1; i < data.length; i++) {\n      output[i] = data[i] - alpha * data[i - 1];\n    }\n    \n    return output;\n  }\n\n  /**\n   * 后处理特征\n   * @param {Object} features - 提取的特征\n   * @returns {Object} 后处理后的特征\n   */\n  postprocess(features) {\n    // 1. 特征归一化\n    const normalized = this.normalizeFeatures(features);\n    \n    // 2. 特征平滑\n    const smoothed = this.smoothFeatures(normalized);\n    \n    // 3. 添加上下文信息\n    const withContext = this.addContext(smoothed);\n    \n    return withContext;\n  }\n\n  /**\n   * 归一化特征\n   * @param {Object} features - 特征数据\n   * @returns {Object} 归一化后的特征\n   */\n  normalizeFeatures(features) {\n    const normalized = { ...features };\n    \n    // 归一化梅尔频谱\n    if (features.melSpectrogram) {\n      const mel = features.melSpectrogram;\n      const mean = mel.reduce((sum, val) => sum + val, 0) / mel.length;\n      const variance = mel.reduce((sum, val) => sum + (val - mean) ** 2, 0) / mel.length;\n      const std = Math.sqrt(variance);\n      \n      if (std > 1e-8) {\n        normalized.melSpectrogram = mel.map(val => (val - mean) / std);\n      }\n    }\n    \n    return normalized;\n  }\n\n  /**\n   * 平滑特征\n   * @param {Object} features - 特征数据\n   * @returns {Object} 平滑后的特征\n   */\n  smoothFeatures(features) {\n    // 简单的移动平均平滑\n    const smoothed = { ...features };\n    \n    if (features.melSpectrogram) {\n      const windowSize = 3;\n      const mel = features.melSpectrogram;\n      const smoothedMel = new Float32Array(mel.length);\n      \n      for (let i = 0; i < mel.length; i++) {\n        let sum = 0;\n        let count = 0;\n        \n        for (let j = Math.max(0, i - windowSize); j <= Math.min(mel.length - 1, i + windowSize); j++) {\n          sum += mel[j];\n          count++;\n        }\n        \n        smoothedMel[i] = sum / count;\n      }\n      \n      smoothed.melSpectrogram = smoothedMel;\n    }\n    \n    return smoothed;\n  }\n\n  /**\n   * 添加上下文信息\n   * @param {Object} features - 特征数据\n   * @returns {Object} 添加上下文后的特征\n   */\n  addContext(features) {\n    return {\n      ...features,\n      context: {\n        sampleRate: this.config.sampleRate,\n        frameLength: this.config.frameLength,\n        hopLength: this.config.hopLength,\n        timestamp: Date.now()\n      }\n    };\n  }\n\n  /**\n   * 获取处理器状态\n   * @returns {Object} 处理器状态\n   */\n  getStatus() {\n    return {\n      isInitialized: this.isInitialized,\n      config: this.config,\n      bufferSize: this.audioBuffer.length\n    };\n  }\n\n  /**\n   * 销毁处理器\n   */\n  dispose() {\n    if (this.featureExtractor) {\n      this.featureExtractor.dispose();\n    }\n    \n    this.audioBuffer = [];\n    this.isInitialized = false;\n    \n    console.log('🎵 AudioProcessor: Disposed');\n  }\n}\n", "/**\n * 性能监控器 - 监控系统性能和资源使用\n */\nexport class PerformanceMonitor {\n  constructor() {\n    this.isRunning = false;\n    this.startTime = null;\n    \n    // 性能指标\n    this.metrics = {\n      // 音频处理性能\n      audioProcessing: {\n        totalCalls: 0,\n        totalTime: 0,\n        averageTime: 0,\n        maxTime: 0,\n        minTime: Infinity\n      },\n      \n      // AI推理性能\n      aiInference: {\n        totalCalls: 0,\n        totalTime: 0,\n        averageTime: 0,\n        maxTime: 0,\n        minTime: Infinity\n      },\n      \n      // 渲染性能\n      rendering: {\n        totalFrames: 0,\n        totalTime: 0,\n        averageTime: 0,\n        fps: 0,\n        maxTime: 0,\n        minTime: Infinity\n      },\n      \n      // 内存使用\n      memory: {\n        current: 0,\n        peak: 0,\n        average: 0,\n        samples: []\n      },\n      \n      // CPU使用（估算）\n      cpu: {\n        current: 0,\n        peak: 0,\n        average: 0,\n        samples: []\n      }\n    };\n    \n    // 配置\n    this.config = {\n      sampleInterval: 1000, // 1秒采样间隔\n      maxSamples: 60, // 保留60个样本（1分钟）\n      enableDetailedLogging: false,\n      warningThresholds: {\n        audioProcessingTime: 50, // 50ms\n        aiInferenceTime: 200, // 200ms\n        renderingTime: 16.67, // 60fps = 16.67ms per frame\n        memoryUsage: 100 * 1024 * 1024, // 100MB\n        cpuUsage: 80 // 80%\n      }\n    };\n    \n    // 监控定时器\n    this.monitoringInterval = null;\n    this.lastCpuTime = null;\n  }\n\n  /**\n   * 开始性能监控\n   */\n  start() {\n    if (this.isRunning) {\n      return;\n    }\n    \n    console.log('📊 PerformanceMonitor: Starting...');\n    \n    this.isRunning = true;\n    this.startTime = performance.now();\n    \n    // 开始定期采样\n    this.monitoringInterval = setInterval(() => {\n      this.sampleMetrics();\n    }, this.config.sampleInterval);\n    \n    // 初始采样\n    this.sampleMetrics();\n  }\n\n  /**\n   * 停止性能监控\n   */\n  stop() {\n    if (!this.isRunning) {\n      return;\n    }\n    \n    console.log('📊 PerformanceMonitor: Stopping...');\n    \n    this.isRunning = false;\n    \n    if (this.monitoringInterval) {\n      clearInterval(this.monitoringInterval);\n      this.monitoringInterval = null;\n    }\n  }\n\n  /**\n   * 记录音频处理时间\n   * @param {number} processingTime - 处理时间(ms)\n   */\n  recordAudioProcessing(processingTime = 0) {\n    this.updateMetric('audioProcessing', processingTime);\n    \n    if (processingTime > this.config.warningThresholds.audioProcessingTime) {\n      this.emitWarning('audioProcessing', processingTime);\n    }\n  }\n\n  /**\n   * 记录AI推理时间\n   * @param {number} inferenceTime - 推理时间(ms)\n   */\n  recordAIInference(inferenceTime = 0) {\n    this.updateMetric('aiInference', inferenceTime);\n    \n    if (inferenceTime > this.config.warningThresholds.aiInferenceTime) {\n      this.emitWarning('aiInference', inferenceTime);\n    }\n  }\n\n  /**\n   * 记录渲染时间\n   * @param {number} renderTime - 渲染时间(ms)\n   */\n  recordRendering(renderTime = 0) {\n    this.updateMetric('rendering', renderTime);\n    \n    // 计算FPS\n    if (renderTime > 0) {\n      this.metrics.rendering.fps = 1000 / renderTime;\n    }\n    \n    if (renderTime > this.config.warningThresholds.renderingTime) {\n      this.emitWarning('rendering', renderTime);\n    }\n  }\n\n  /**\n   * 更新性能指标\n   * @param {string} metricName - 指标名称\n   * @param {number} value - 值\n   */\n  updateMetric(metricName, value) {\n    const metric = this.metrics[metricName];\n    if (!metric) {\n      return;\n    }\n    \n    metric.totalCalls++;\n    metric.totalTime += value;\n    metric.averageTime = metric.totalTime / metric.totalCalls;\n    metric.maxTime = Math.max(metric.maxTime, value);\n    metric.minTime = Math.min(metric.minTime, value);\n    \n    if (this.config.enableDetailedLogging) {\n      console.log(`📊 ${metricName}: ${value.toFixed(2)}ms (avg: ${metric.averageTime.toFixed(2)}ms)`);\n    }\n  }\n\n  /**\n   * 采样系统指标\n   */\n  sampleMetrics() {\n    // 采样内存使用\n    this.sampleMemoryUsage();\n    \n    // 采样CPU使用（估算）\n    this.sampleCpuUsage();\n    \n    // 检查警告阈值\n    this.checkWarningThresholds();\n  }\n\n  /**\n   * 采样内存使用\n   */\n  sampleMemoryUsage() {\n    let memoryUsage = 0;\n    \n    // 尝试获取内存信息\n    if (performance.memory) {\n      memoryUsage = performance.memory.usedJSHeapSize;\n    } else if (navigator.deviceMemory) {\n      // 估算内存使用\n      memoryUsage = navigator.deviceMemory * 1024 * 1024 * 0.1; // 估算使用10%\n    }\n    \n    const memory = this.metrics.memory;\n    memory.current = memoryUsage;\n    memory.peak = Math.max(memory.peak, memoryUsage);\n    \n    // 添加样本\n    memory.samples.push({\n      timestamp: Date.now(),\n      value: memoryUsage\n    });\n    \n    // 限制样本数量\n    if (memory.samples.length > this.config.maxSamples) {\n      memory.samples.shift();\n    }\n    \n    // 计算平均值\n    memory.average = memory.samples.reduce((sum, sample) => sum + sample.value, 0) / memory.samples.length;\n  }\n\n  /**\n   * 采样CPU使用（估算）\n   */\n  sampleCpuUsage() {\n    const now = performance.now();\n    \n    if (this.lastCpuTime) {\n      // 简单的CPU使用估算\n      const timeDiff = now - this.lastCpuTime;\n      const expectedIdleTime = this.config.sampleInterval * 0.8; // 期望80%空闲\n      const cpuUsage = Math.max(0, Math.min(100, (1 - expectedIdleTime / timeDiff) * 100));\n      \n      const cpu = this.metrics.cpu;\n      cpu.current = cpuUsage;\n      cpu.peak = Math.max(cpu.peak, cpuUsage);\n      \n      // 添加样本\n      cpu.samples.push({\n        timestamp: Date.now(),\n        value: cpuUsage\n      });\n      \n      // 限制样本数量\n      if (cpu.samples.length > this.config.maxSamples) {\n        cpu.samples.shift();\n      }\n      \n      // 计算平均值\n      cpu.average = cpu.samples.reduce((sum, sample) => sum + sample.value, 0) / cpu.samples.length;\n    }\n    \n    this.lastCpuTime = now;\n  }\n\n  /**\n   * 检查警告阈值\n   */\n  checkWarningThresholds() {\n    const thresholds = this.config.warningThresholds;\n    \n    // 检查内存使用\n    if (this.metrics.memory.current > thresholds.memoryUsage) {\n      this.emitWarning('memory', this.metrics.memory.current);\n    }\n    \n    // 检查CPU使用\n    if (this.metrics.cpu.current > thresholds.cpuUsage) {\n      this.emitWarning('cpu', this.metrics.cpu.current);\n    }\n  }\n\n  /**\n   * 发出性能警告\n   * @param {string} type - 警告类型\n   * @param {number} value - 当前值\n   */\n  emitWarning(type, value) {\n    const warning = {\n      type,\n      value,\n      threshold: this.config.warningThresholds[type],\n      timestamp: Date.now()\n    };\n    \n    console.warn(`⚠️ Performance warning: ${type} = ${value}`, warning);\n    \n    // 发送事件（如果有事件总线）\n    if (typeof window !== 'undefined' && window.eventBus) {\n      window.eventBus.emit('UI_PERFORMANCE_WARNING', warning);\n    }\n  }\n\n  /**\n   * 获取性能统计\n   * @returns {Object} 性能统计\n   */\n  getStats() {\n    return {\n      isRunning: this.isRunning,\n      uptime: this.startTime ? performance.now() - this.startTime : 0,\n      metrics: JSON.parse(JSON.stringify(this.metrics)), // 深拷贝\n      summary: this.getSummary()\n    };\n  }\n\n  /**\n   * 获取性能摘要\n   * @returns {Object} 性能摘要\n   */\n  getSummary() {\n    return {\n      audioProcessing: {\n        averageTime: this.metrics.audioProcessing.averageTime,\n        callsPerSecond: this.getCallsPerSecond('audioProcessing')\n      },\n      aiInference: {\n        averageTime: this.metrics.aiInference.averageTime,\n        callsPerSecond: this.getCallsPerSecond('aiInference')\n      },\n      rendering: {\n        fps: this.metrics.rendering.fps,\n        averageTime: this.metrics.rendering.averageTime\n      },\n      memory: {\n        current: this.formatBytes(this.metrics.memory.current),\n        peak: this.formatBytes(this.metrics.memory.peak),\n        average: this.formatBytes(this.metrics.memory.average)\n      },\n      cpu: {\n        current: `${this.metrics.cpu.current.toFixed(1)}%`,\n        peak: `${this.metrics.cpu.peak.toFixed(1)}%`,\n        average: `${this.metrics.cpu.average.toFixed(1)}%`\n      }\n    };\n  }\n\n  /**\n   * 计算每秒调用次数\n   * @param {string} metricName - 指标名称\n   * @returns {number} 每秒调用次数\n   */\n  getCallsPerSecond(metricName) {\n    const metric = this.metrics[metricName];\n    const uptime = this.startTime ? (performance.now() - this.startTime) / 1000 : 1;\n    return metric.totalCalls / uptime;\n  }\n\n  /**\n   * 格式化字节数\n   * @param {number} bytes - 字节数\n   * @returns {string} 格式化后的字符串\n   */\n  formatBytes(bytes) {\n    if (bytes === 0) return '0 B';\n    \n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    \n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  /**\n   * 重置统计信息\n   */\n  reset() {\n    // 重置所有指标\n    Object.keys(this.metrics).forEach(key => {\n      const metric = this.metrics[key];\n      if (metric.totalCalls !== undefined) {\n        metric.totalCalls = 0;\n        metric.totalTime = 0;\n        metric.averageTime = 0;\n        metric.maxTime = 0;\n        metric.minTime = Infinity;\n      }\n      if (metric.samples) {\n        metric.samples = [];\n      }\n    });\n    \n    this.startTime = performance.now();\n    console.log('📊 PerformanceMonitor: Statistics reset');\n  }\n\n  /**\n   * 更新配置\n   * @param {Object} newConfig - 新配置\n   */\n  updateConfig(newConfig) {\n    this.config = { ...this.config, ...newConfig };\n  }\n\n  /**\n   * 销毁性能监控器\n   */\n  dispose() {\n    this.stop();\n    console.log('📊 PerformanceMonitor: Disposed');\n  }\n}\n", "/**\n * 音频管理器 - 统一管理音频相关功能\n * 负责音频初始化、数据获取和处理协调\n */\nimport eventBus, { EVENTS } from './EventBus.js';\nimport { AudioProcessor } from '../audio/AudioProcessor.js';\nimport { PerformanceMonitor } from '../utils/PerformanceMonitor.js';\n\nexport class AudioManager {\n  constructor() {\n    this.audioContext = null;\n    this.analyser = null;\n    this.sourceNode = null;\n    this.audioProcessor = null;\n    this.isInitialized = false;\n    this.isProcessing = false;\n    this.performanceMonitor = new PerformanceMonitor();\n    \n    // 音频配置\n    this.config = {\n      sampleRate: 16000,      // YAMNet标准采样率\n      fftSize: 2048,          // FFT大小\n      smoothingTimeConstant: 0.8,\n      processingInterval: 100  // 处理间隔(ms)\n    };\n\n    // 绑定方法\n    this.handleAudioData = this.handleAudioData.bind(this);\n  }\n\n  /**\n   * 初始化音频系统\n   * @param {HTMLVideoElement} videoElement - 视频元素\n   */\n  async initialize(videoElement) {\n    try {\n      if (this.isInitialized) {\n        console.warn('AudioManager: Already initialized');\n        return;\n      }\n\n      console.log('🎵 AudioManager: Initializing audio system...');\n      \n      // 创建音频上下文\n      this.audioContext = new (window.AudioContext || window.webkitAudioContext)({\n        sampleRate: this.config.sampleRate\n      });\n\n      // 创建音频源\n      try {\n        this.sourceNode = this.audioContext.createMediaElementSource(videoElement);\n      } catch (error) {\n        console.error('AudioManager: Error creating media element source:', error);\n        throw new Error('Failed to create audio source');\n      }\n\n      // 创建分析器\n      this.analyser = this.audioContext.createAnalyser();\n      this.analyser.fftSize = this.config.fftSize;\n      this.analyser.smoothingTimeConstant = this.config.smoothingTimeConstant;\n\n      // 创建音频处理器\n      this.audioProcessor = new AudioProcessor(this.audioContext, this.config);\n      await this.audioProcessor.initialize();\n\n      // 连接音频图\n      this.sourceNode.connect(this.analyser);\n      this.analyser.connect(this.audioContext.destination);\n\n      // 监听视频播放状态\n      this.setupVideoEventListeners(videoElement);\n\n      this.isInitialized = true;\n      \n      eventBus.emit(EVENTS.AUDIO_INITIALIZED, {\n        sampleRate: this.audioContext.sampleRate,\n        bufferSize: this.analyser.frequencyBinCount\n      });\n\n      console.log('✅ AudioManager: Audio system initialized successfully');\n      \n    } catch (error) {\n      console.error('❌ AudioManager: Initialization failed:', error);\n      eventBus.emit(EVENTS.AUDIO_ERROR, { error: error.message });\n      throw error;\n    }\n  }\n\n  /**\n   * 设置视频事件监听器\n   * @param {HTMLVideoElement} videoElement - 视频元素\n   */\n  setupVideoEventListeners(videoElement) {\n    // 播放开始时恢复音频上下文\n    videoElement.addEventListener('play', () => {\n      if (this.audioContext.state === 'suspended') {\n        this.audioContext.resume();\n      }\n      this.startProcessing();\n    });\n\n    // 暂停时停止处理\n    videoElement.addEventListener('pause', () => {\n      this.stopProcessing();\n    });\n\n    // 结束时停止处理\n    videoElement.addEventListener('ended', () => {\n      this.stopProcessing();\n    });\n  }\n\n  /**\n   * 开始音频处理\n   */\n  startProcessing() {\n    if (!this.isInitialized) {\n      console.warn('AudioManager: Not initialized');\n      return;\n    }\n\n    if (this.isProcessing) {\n      console.warn('AudioManager: Already processing');\n      return;\n    }\n\n    console.log('🎵 AudioManager: Starting audio processing...');\n    \n    this.isProcessing = true;\n    this.processingLoop();\n    \n    eventBus.emit(EVENTS.AUDIO_STARTED);\n  }\n\n  /**\n   * 停止音频处理\n   */\n  stopProcessing() {\n    if (!this.isProcessing) {\n      return;\n    }\n\n    console.log('🎵 AudioManager: Stopping audio processing...');\n    \n    this.isProcessing = false;\n    \n    eventBus.emit(EVENTS.AUDIO_STOPPED);\n  }\n\n  /**\n   * 音频处理循环\n   */\n  async processingLoop() {\n    if (!this.isProcessing) {\n      return;\n    }\n\n    try {\n      const startTime = performance.now();\n      \n      // 获取音频数据\n      const audioData = this.getAudioData();\n      \n      // 检查是否有有效音频\n      if (this.hasValidAudio(audioData)) {\n        // 处理音频数据\n        const processedData = await this.audioProcessor.process(audioData);\n        \n        // 发送音频数据事件\n        eventBus.emit(EVENTS.AUDIO_DATA_AVAILABLE, {\n          raw: audioData,\n          processed: processedData,\n          timestamp: Date.now()\n        });\n      }\n\n      // 性能监控\n      const processingTime = performance.now() - startTime;\n      this.performanceMonitor.recordProcessingTime(processingTime);\n      \n      // 调度下一次处理\n      setTimeout(() => this.processingLoop(), this.config.processingInterval);\n      \n    } catch (error) {\n      console.error('AudioManager: Error in processing loop:', error);\n      eventBus.emit(EVENTS.AUDIO_ERROR, { error: error.message });\n      \n      // 错误后重试\n      setTimeout(() => this.processingLoop(), this.config.processingInterval * 2);\n    }\n  }\n\n  /**\n   * 获取音频数据\n   * @returns {Object} 音频数据\n   */\n  getAudioData() {\n    const bufferLength = this.analyser.frequencyBinCount;\n    const timeDomainData = new Uint8Array(bufferLength);\n    const frequencyData = new Uint8Array(bufferLength);\n\n    this.analyser.getByteTimeDomainData(timeDomainData);\n    this.analyser.getByteFrequencyData(frequencyData);\n\n    return {\n      timeDomain: timeDomainData,\n      frequency: frequencyData,\n      sampleRate: this.audioContext.sampleRate,\n      bufferLength\n    };\n  }\n\n  /**\n   * 检查是否有有效音频\n   * @param {Object} audioData - 音频数据\n   * @returns {boolean} 是否有有效音频\n   */\n  hasValidAudio(audioData) {\n    const maxFreq = Math.max(...audioData.frequency);\n    const avgFreq = audioData.frequency.reduce((sum, val) => sum + val, 0) / audioData.frequency.length;\n    \n    return maxFreq > 0 && avgFreq > 1; // 基本的音频检测阈值\n  }\n\n  /**\n   * 处理音频数据\n   * @param {Object} audioData - 音频数据\n   */\n  handleAudioData(audioData) {\n    // 这个方法可以被外部调用来处理特定的音频数据\n    eventBus.emit(EVENTS.AUDIO_DATA_AVAILABLE, audioData);\n  }\n\n  /**\n   * 获取音频上下文信息\n   * @returns {Object} 音频上下文信息\n   */\n  getAudioContextInfo() {\n    if (!this.audioContext) {\n      return null;\n    }\n\n    return {\n      state: this.audioContext.state,\n      sampleRate: this.audioContext.sampleRate,\n      currentTime: this.audioContext.currentTime,\n      baseLatency: this.audioContext.baseLatency || 0,\n      outputLatency: this.audioContext.outputLatency || 0\n    };\n  }\n\n  /**\n   * 获取性能统计\n   * @returns {Object} 性能统计\n   */\n  getPerformanceStats() {\n    return this.performanceMonitor.getStats();\n  }\n\n  /**\n   * 更新配置\n   * @param {Object} newConfig - 新配置\n   */\n  updateConfig(newConfig) {\n    this.config = { ...this.config, ...newConfig };\n    \n    if (this.analyser && newConfig.fftSize) {\n      this.analyser.fftSize = newConfig.fftSize;\n    }\n    \n    if (this.analyser && newConfig.smoothingTimeConstant !== undefined) {\n      this.analyser.smoothingTimeConstant = newConfig.smoothingTimeConstant;\n    }\n  }\n\n  /**\n   * 销毁音频管理器\n   */\n  dispose() {\n    this.stopProcessing();\n    \n    if (this.audioProcessor) {\n      this.audioProcessor.dispose();\n    }\n    \n    if (this.audioContext) {\n      this.audioContext.close();\n    }\n    \n    this.isInitialized = false;\n    console.log('🎵 AudioManager: Disposed');\n  }\n}\n", "/**\n * YAMNet分类器 - Google YAMNet音频事件分类模型\n * 支持521种音频事件的实时分类\n */\nimport * as tf from '@tensorflow/tfjs';\n\nexport class YAMNetClassifier {\n  constructor() {\n    this.model = null;\n    this.classNames = null;\n    this.isInitialized = false;\n    \n    // YAMNet模型配置\n    this.config = {\n      modelUrl: 'https://tfhub.dev/google/tfjs-model/yamnet/tfjs/1',\n      classMapUrl: 'https://raw.githubusercontent.com/tensorflow/models/master/research/audioset/yamnet/yamnet_class_map.csv',\n      sampleRate: 16000,\n      patchWindowSeconds: 0.96,\n      patchHopSeconds: 0.48,\n      confidenceThreshold: 0.3,\n      topK: 5\n    };\n    \n    // 输入规格\n    this.inputSpecs = {\n      sampleRate: 16000,\n      expectedSamples: 15600, // 0.975秒 @ 16kHz\n      melBins: 64,\n      patchFrames: 96\n    };\n    \n    // 性能统计\n    this.stats = {\n      totalPredictions: 0,\n      averageInferenceTime: 0,\n      lastInferenceTime: 0\n    };\n  }\n\n  /**\n   * 初始化YAMNet分类器\n   */\n  async initialize() {\n    try {\n      console.log('🎵 YAMNetClassifier: Initializing...');\n      \n      // 1. 加载模型\n      await this.loadModel();\n      \n      // 2. 加载类别映射\n      await this.loadClassMap();\n      \n      // 3. 预热模型\n      await this.warmupModel();\n      \n      this.isInitialized = true;\n      console.log('✅ YAMNetClassifier: Initialized successfully');\n      console.log(`📊 Model supports ${this.classNames.length} audio event classes`);\n      \n    } catch (error) {\n      console.error('❌ YAMNetClassifier: Initialization failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 加载YAMNet模型\n   */\n  async loadModel() {\n    try {\n      console.log('📥 Loading YAMNet model from TensorFlow Hub...');\n      \n      // 从TensorFlow Hub加载模型\n      this.model = await tf.loadGraphModel(this.config.modelUrl);\n      \n      console.log('✅ YAMNet model loaded successfully');\n      console.log('📋 Model inputs:', this.model.inputs.map(input => ({\n        name: input.name,\n        shape: input.shape,\n        dtype: input.dtype\n      })));\n      console.log('📋 Model outputs:', this.model.outputs.map(output => ({\n        name: output.name,\n        shape: output.shape,\n        dtype: output.dtype\n      })));\n      \n    } catch (error) {\n      console.error('Failed to load YAMNet model:', error);\n      throw new Error(`Failed to load YAMNet model: ${error.message}`);\n    }\n  }\n\n  /**\n   * 加载类别映射\n   */\n  async loadClassMap() {\n    try {\n      console.log('📥 Loading YAMNet class map...');\n      \n      const response = await fetch(this.config.classMapUrl);\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n      \n      const csvText = await response.text();\n      this.classNames = this.parseClassMap(csvText);\n      \n      console.log(`✅ Loaded ${this.classNames.length} class names`);\n      \n    } catch (error) {\n      console.error('Failed to load class map:', error);\n      // 使用备用类别映射\n      this.classNames = this.getDefaultClassMap();\n      console.log('⚠️ Using default class map');\n    }\n  }\n\n  /**\n   * 解析CSV格式的类别映射\n   * @param {string} csvText - CSV文本\n   * @returns {Array} 类别名称数组\n   */\n  parseClassMap(csvText) {\n    const lines = csvText.trim().split('\\n');\n    const classNames = [];\n    \n    // 跳过标题行\n    for (let i = 1; i < lines.length; i++) {\n      const line = lines[i].trim();\n      if (line) {\n        // CSV格式: index,mid,display_name\n        const parts = line.split(',');\n        if (parts.length >= 3) {\n          // 移除引号并获取显示名称\n          const displayName = parts[2].replace(/\"/g, '').trim();\n          classNames.push(displayName);\n        }\n      }\n    }\n    \n    return classNames;\n  }\n\n  /**\n   * 获取默认类别映射（备用）\n   * @returns {Array} 默认类别名称\n   */\n  getDefaultClassMap() {\n    // 这里包含一些常见的音频事件类别\n    return [\n      'Speech', 'Music', 'Laughter', 'Applause', 'Crying', 'Footsteps',\n      'Door', 'Knock', 'Glass', 'Gunshot', 'Siren', 'Car', 'Dog', 'Cat',\n      'Bird', 'Water', 'Wind', 'Rain', 'Thunder', 'Fire', 'Explosion',\n      'Whistle', 'Bell', 'Horn', 'Alarm', 'Phone', 'Computer', 'Television',\n      'Radio', 'Microwave', 'Vacuum', 'Washing machine', 'Dishwasher'\n      // ... 更多类别\n    ];\n  }\n\n  /**\n   * 预热模型\n   */\n  async warmupModel() {\n    try {\n      console.log('🔥 Warming up YAMNet model...');\n      \n      // 创建虚拟输入数据\n      const dummyInput = tf.zeros([this.inputSpecs.expectedSamples]);\n      \n      // 执行一次推理来预热模型\n      const startTime = performance.now();\n      const predictions = await this.model.predict(dummyInput);\n      const warmupTime = performance.now() - startTime;\n      \n      // 清理张量\n      dummyInput.dispose();\n      if (Array.isArray(predictions)) {\n        predictions.forEach(tensor => tensor.dispose());\n      } else {\n        predictions.dispose();\n      }\n      \n      console.log(`✅ Model warmup completed in ${warmupTime.toFixed(2)}ms`);\n      \n    } catch (error) {\n      console.error('Model warmup failed:', error);\n      // 预热失败不影响主要功能\n    }\n  }\n\n  /**\n   * 对音频特征进行分类\n   * @param {Object} audioFeatures - 音频特征数据\n   * @returns {Promise<Object>} 分类结果\n   */\n  async classify(audioFeatures) {\n    if (!this.isInitialized) {\n      throw new Error('YAMNetClassifier not initialized');\n    }\n\n    const startTime = performance.now();\n    \n    try {\n      // 1. 预处理音频数据\n      const inputTensor = this.preprocessAudio(audioFeatures);\n      \n      // 2. 执行模型推理\n      const predictions = await this.model.predict(inputTensor);\n      \n      // 3. 后处理预测结果\n      const results = await this.postprocessPredictions(predictions);\n      \n      // 4. 清理张量\n      inputTensor.dispose();\n      if (Array.isArray(predictions)) {\n        predictions.forEach(tensor => tensor.dispose());\n      } else {\n        predictions.dispose();\n      }\n      \n      // 5. 更新统计信息\n      const inferenceTime = performance.now() - startTime;\n      this.updateStats(inferenceTime);\n      \n      return {\n        predictions: results,\n        inferenceTime,\n        timestamp: Date.now(),\n        modelType: 'yamnet'\n      };\n      \n    } catch (error) {\n      console.error('YAMNet classification error:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 预处理音频数据\n   * @param {Object} audioFeatures - 音频特征\n   * @returns {tf.Tensor} 预处理后的输入张量\n   */\n  preprocessAudio(audioFeatures) {\n    // 如果有时域数据，直接使用\n    if (audioFeatures.timeData) {\n      return this.preprocessTimeData(audioFeatures.timeData);\n    }\n    \n    // 如果有梅尔频谱图，转换为YAMNet输入格式\n    if (audioFeatures.melSpectrogram) {\n      return this.preprocessMelSpectrogram(audioFeatures.melSpectrogram);\n    }\n    \n    // 如果只有频域数据，尝试重构时域数据\n    if (audioFeatures.frequency) {\n      const timeData = this.reconstructTimeData(audioFeatures.frequency);\n      return this.preprocessTimeData(timeData);\n    }\n    \n    throw new Error('No suitable audio data found for YAMNet input');\n  }\n\n  /**\n   * 预处理时域数据\n   * @param {Float32Array} timeData - 时域音频数据\n   * @returns {tf.Tensor} 输入张量\n   */\n  preprocessTimeData(timeData) {\n    // 确保数据长度符合YAMNet要求\n    let processedData;\n    \n    if (timeData.length > this.inputSpecs.expectedSamples) {\n      // 截取中间部分\n      const start = Math.floor((timeData.length - this.inputSpecs.expectedSamples) / 2);\n      processedData = timeData.slice(start, start + this.inputSpecs.expectedSamples);\n    } else if (timeData.length < this.inputSpecs.expectedSamples) {\n      // 零填充\n      processedData = new Float32Array(this.inputSpecs.expectedSamples);\n      processedData.set(timeData);\n    } else {\n      processedData = timeData;\n    }\n    \n    // 创建张量\n    return tf.tensor1d(processedData);\n  }\n\n  /**\n   * 预处理梅尔频谱图\n   * @param {Array} melSpectrogram - 梅尔频谱图\n   * @returns {tf.Tensor} 输入张量\n   */\n  preprocessMelSpectrogram(melSpectrogram) {\n    // YAMNet期望的是原始音频，这里需要从梅尔频谱图重构\n    // 这是一个简化的实现，实际应用中可能需要更复杂的重构算法\n    \n    // 简单地将梅尔频谱图展平并调整大小\n    const flatData = melSpectrogram.flat();\n    const targetLength = this.inputSpecs.expectedSamples;\n    \n    const resized = new Float32Array(targetLength);\n    const ratio = flatData.length / targetLength;\n    \n    for (let i = 0; i < targetLength; i++) {\n      const srcIndex = Math.floor(i * ratio);\n      resized[i] = flatData[srcIndex] || 0;\n    }\n    \n    return tf.tensor1d(resized);\n  }\n\n  /**\n   * 从频域数据重构时域数据\n   * @param {Uint8Array} frequencyData - 频域数据\n   * @returns {Float32Array} 重构的时域数据\n   */\n  reconstructTimeData(frequencyData) {\n    // 简单的频域到时域转换\n    const timeData = new Float32Array(frequencyData.length);\n    \n    for (let i = 0; i < frequencyData.length; i++) {\n      // 归一化到[-1, 1]范围\n      timeData[i] = (frequencyData[i] - 128) / 128.0;\n    }\n    \n    return timeData;\n  }\n\n  /**\n   * 后处理预测结果\n   * @param {tf.Tensor|tf.Tensor[]} predictions - 模型预测输出\n   * @returns {Promise<Array>} 处理后的预测结果\n   */\n  async postprocessPredictions(predictions) {\n    // YAMNet输出多个张量，我们需要分类分数\n    let scoresTensor;\n    \n    if (Array.isArray(predictions)) {\n      // 通常分类分数是第一个输出\n      scoresTensor = predictions[0];\n    } else {\n      scoresTensor = predictions;\n    }\n    \n    // 获取分数数据\n    const scoresData = await scoresTensor.data();\n    \n    // 创建预测结果数组\n    const results = [];\n    for (let i = 0; i < scoresData.length && i < this.classNames.length; i++) {\n      if (scoresData[i] >= this.config.confidenceThreshold) {\n        results.push({\n          className: this.classNames[i] || `Class_${i}`,\n          confidence: scoresData[i],\n          classIndex: i\n        });\n      }\n    }\n    \n    // 按置信度排序并返回前K个结果\n    return results\n      .sort((a, b) => b.confidence - a.confidence)\n      .slice(0, this.config.topK);\n  }\n\n  /**\n   * 更新统计信息\n   * @param {number} inferenceTime - 推理时间\n   */\n  updateStats(inferenceTime) {\n    this.stats.totalPredictions++;\n    this.stats.lastInferenceTime = inferenceTime;\n    \n    // 计算移动平均\n    const alpha = 0.1;\n    this.stats.averageInferenceTime = \n      this.stats.averageInferenceTime * (1 - alpha) + inferenceTime * alpha;\n  }\n\n  /**\n   * 获取分类器信息\n   * @returns {Object} 分类器信息\n   */\n  getInfo() {\n    return {\n      isInitialized: this.isInitialized,\n      modelType: 'YAMNet',\n      classCount: this.classNames ? this.classNames.length : 0,\n      config: this.config,\n      inputSpecs: this.inputSpecs,\n      stats: this.stats\n    };\n  }\n\n  /**\n   * 更新配置\n   * @param {Object} newConfig - 新配置\n   */\n  updateConfig(newConfig) {\n    this.config = { ...this.config, ...newConfig };\n  }\n\n  /**\n   * 获取所有类别名称\n   * @returns {Array} 类别名称数组\n   */\n  getClassNames() {\n    return this.classNames ? [...this.classNames] : [];\n  }\n\n  /**\n   * 获取性能统计\n   * @returns {Object} 性能统计\n   */\n  getStats() {\n    return { ...this.stats };\n  }\n\n  /**\n   * 销毁分类器\n   */\n  dispose() {\n    if (this.model) {\n      this.model.dispose();\n      this.model = null;\n    }\n    \n    this.classNames = null;\n    this.isInitialized = false;\n    \n    console.log('🎵 YAMNetClassifier: Disposed');\n  }\n}\n", "/**\n * 语音指令检测器 - 检测简单的语音指令\n * 支持\"是\"、\"否\"、\"开始\"、\"停止\"等基本指令\n */\nimport * as tf from '@tensorflow/tfjs';\n\nexport class SpeechCommandDetector {\n  constructor() {\n    this.model = null;\n    this.commands = null;\n    this.isInitialized = false;\n    \n    // 配置\n    this.config = {\n      modelUrl: 'https://storage.googleapis.com/tfjs-models/tfjs/speech-commands/v0.4/browser_fft/18w/model.json',\n      vocabularyUrl: 'https://storage.googleapis.com/tfjs-models/tfjs/speech-commands/v0.4/browser_fft/18w/metadata.json',\n      sampleRate: 44100,\n      fftSize: 1024,\n      confidenceThreshold: 0.7,\n      topK: 3,\n      enableCustomCommands: true\n    };\n    \n    // 自定义指令映射（中文到英文）\n    this.customCommands = {\n      '是': 'yes',\n      '否': 'no', \n      '不': 'no',\n      '开始': 'go',\n      '停止': 'stop',\n      '暂停': 'stop',\n      '上': 'up',\n      '下': 'down',\n      '左': 'left',\n      '右': 'right'\n    };\n    \n    // 性能统计\n    this.stats = {\n      totalDetections: 0,\n      averageInferenceTime: 0,\n      lastInferenceTime: 0,\n      commandCounts: {}\n    };\n  }\n\n  /**\n   * 初始化语音指令检测器\n   */\n  async initialize() {\n    try {\n      console.log('🗣️ SpeechCommandDetector: Initializing...');\n      \n      // 1. 加载模型\n      await this.loadModel();\n      \n      // 2. 加载词汇表\n      await this.loadVocabulary();\n      \n      // 3. 预热模型\n      await this.warmupModel();\n      \n      this.isInitialized = true;\n      console.log('✅ SpeechCommandDetector: Initialized successfully');\n      console.log(`📊 Supports ${this.commands.length} speech commands`);\n      \n    } catch (error) {\n      console.error('❌ SpeechCommandDetector: Initialization failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 加载语音指令模型\n   */\n  async loadModel() {\n    try {\n      console.log('📥 Loading Speech Commands model...');\n      \n      // 加载预训练的语音指令模型\n      this.model = await tf.loadLayersModel(this.config.modelUrl);\n      \n      console.log('✅ Speech Commands model loaded successfully');\n      console.log('📋 Model summary:');\n      console.log('  - Input shape:', this.model.inputs[0].shape);\n      console.log('  - Output shape:', this.model.outputs[0].shape);\n      \n    } catch (error) {\n      console.error('Failed to load Speech Commands model:', error);\n      throw new Error(`Failed to load Speech Commands model: ${error.message}`);\n    }\n  }\n\n  /**\n   * 加载词汇表\n   */\n  async loadVocabulary() {\n    try {\n      console.log('📥 Loading speech commands vocabulary...');\n      \n      const response = await fetch(this.config.vocabularyUrl);\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n      \n      const metadata = await response.json();\n      this.commands = metadata.words || this.getDefaultCommands();\n      \n      console.log(`✅ Loaded ${this.commands.length} commands:`, this.commands);\n      \n    } catch (error) {\n      console.error('Failed to load vocabulary:', error);\n      // 使用默认词汇表\n      this.commands = this.getDefaultCommands();\n      console.log('⚠️ Using default command vocabulary');\n    }\n  }\n\n  /**\n   * 获取默认指令词汇表\n   * @returns {Array} 默认指令列表\n   */\n  getDefaultCommands() {\n    return [\n      '_silence_', '_unknown_', 'yes', 'no', 'up', 'down', 'left', 'right',\n      'on', 'off', 'stop', 'go', 'zero', 'one', 'two', 'three', 'four',\n      'five', 'six', 'seven', 'eight', 'nine'\n    ];\n  }\n\n  /**\n   * 预热模型\n   */\n  async warmupModel() {\n    try {\n      console.log('🔥 Warming up Speech Commands model...');\n      \n      // 创建虚拟输入（通常是频谱图）\n      const inputShape = this.model.inputs[0].shape;\n      const dummyInput = tf.zeros(inputShape.slice(1)); // 去掉batch维度\n      const batchedInput = dummyInput.expandDims(0); // 添加batch维度\n      \n      // 执行一次推理\n      const startTime = performance.now();\n      const predictions = this.model.predict(batchedInput);\n      const warmupTime = performance.now() - startTime;\n      \n      // 清理张量\n      dummyInput.dispose();\n      batchedInput.dispose();\n      predictions.dispose();\n      \n      console.log(`✅ Model warmup completed in ${warmupTime.toFixed(2)}ms`);\n      \n    } catch (error) {\n      console.error('Model warmup failed:', error);\n      // 预热失败不影响主要功能\n    }\n  }\n\n  /**\n   * 检测语音指令\n   * @param {Object} audioFeatures - 音频特征数据\n   * @returns {Promise<Object>} 检测结果\n   */\n  async detect(audioFeatures) {\n    if (!this.isInitialized) {\n      throw new Error('SpeechCommandDetector not initialized');\n    }\n\n    const startTime = performance.now();\n    \n    try {\n      // 1. 预处理音频数据\n      const inputTensor = this.preprocessAudio(audioFeatures);\n      \n      // 2. 执行模型推理\n      const predictions = this.model.predict(inputTensor);\n      \n      // 3. 后处理预测结果\n      const results = await this.postprocessPredictions(predictions);\n      \n      // 4. 清理张量\n      inputTensor.dispose();\n      predictions.dispose();\n      \n      // 5. 更新统计信息\n      const inferenceTime = performance.now() - startTime;\n      this.updateStats(inferenceTime, results);\n      \n      return {\n        commands: results,\n        inferenceTime,\n        timestamp: Date.now(),\n        modelType: 'speech_commands'\n      };\n      \n    } catch (error) {\n      console.error('Speech command detection error:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 预处理音频数据\n   * @param {Object} audioFeatures - 音频特征\n   * @returns {tf.Tensor} 预处理后的输入张量\n   */\n  preprocessAudio(audioFeatures) {\n    // 语音指令模型通常期望频谱图输入\n    if (audioFeatures.melSpectrogram) {\n      return this.preprocessSpectrogram(audioFeatures.melSpectrogram);\n    }\n    \n    if (audioFeatures.stft) {\n      return this.preprocessSTFT(audioFeatures.stft);\n    }\n    \n    if (audioFeatures.frequency) {\n      return this.preprocessFrequencyData(audioFeatures.frequency);\n    }\n    \n    throw new Error('No suitable audio data found for speech command detection');\n  }\n\n  /**\n   * 预处理频谱图数据\n   * @param {Array} melSpectrogram - 梅尔频谱图\n   * @returns {tf.Tensor} 输入张量\n   */\n  preprocessSpectrogram(melSpectrogram) {\n    // 获取模型期望的输入形状\n    const inputShape = this.model.inputs[0].shape;\n    const [, height, width, channels] = inputShape;\n    \n    // 将梅尔频谱图转换为模型期望的格式\n    const spectrogramData = new Float32Array(height * width * channels);\n    \n    // 简化的频谱图处理\n    for (let i = 0; i < Math.min(melSpectrogram.length, height); i++) {\n      const frame = melSpectrogram[i];\n      for (let j = 0; j < Math.min(frame.length, width); j++) {\n        const index = i * width * channels + j * channels;\n        spectrogramData[index] = frame[j] || 0;\n        \n        // 如果是多通道，复制到其他通道\n        for (let c = 1; c < channels; c++) {\n          spectrogramData[index + c] = spectrogramData[index];\n        }\n      }\n    }\n    \n    // 创建张量并添加batch维度\n    const tensor = tf.tensor3d(spectrogramData, [height, width, channels]);\n    return tensor.expandDims(0);\n  }\n\n  /**\n   * 预处理STFT数据\n   * @param {Array} stft - STFT数据\n   * @returns {tf.Tensor} 输入张量\n   */\n  preprocessSTFT(stft) {\n    // 从STFT计算功率谱\n    const powerSpectrum = stft.map(frame => {\n      const power = new Float32Array(frame.real.length);\n      for (let i = 0; i < frame.real.length; i++) {\n        power[i] = frame.real[i] * frame.real[i] + frame.imag[i] * frame.imag[i];\n      }\n      return power;\n    });\n    \n    return this.preprocessSpectrogram(powerSpectrum);\n  }\n\n  /**\n   * 预处理频域数据\n   * @param {Uint8Array} frequencyData - 频域数据\n   * @returns {tf.Tensor} 输入张量\n   */\n  preprocessFrequencyData(frequencyData) {\n    // 将频域数据转换为简单的频谱图格式\n    const inputShape = this.model.inputs[0].shape;\n    const [, height, width, channels] = inputShape;\n    \n    const spectrogramData = new Float32Array(height * width * channels);\n    \n    // 简单地将频域数据重复填充到频谱图中\n    for (let i = 0; i < height; i++) {\n      for (let j = 0; j < width; j++) {\n        const freqIndex = Math.floor(j * frequencyData.length / width);\n        const value = (frequencyData[freqIndex] || 0) / 255.0; // 归一化\n        \n        const index = i * width * channels + j * channels;\n        for (let c = 0; c < channels; c++) {\n          spectrogramData[index + c] = value;\n        }\n      }\n    }\n    \n    const tensor = tf.tensor3d(spectrogramData, [height, width, channels]);\n    return tensor.expandDims(0);\n  }\n\n  /**\n   * 后处理预测结果\n   * @param {tf.Tensor} predictions - 模型预测输出\n   * @returns {Promise<Array>} 处理后的预测结果\n   */\n  async postprocessPredictions(predictions) {\n    // 获取预测分数\n    const scoresData = await predictions.data();\n    \n    // 创建结果数组\n    const results = [];\n    for (let i = 0; i < scoresData.length && i < this.commands.length; i++) {\n      const confidence = scoresData[i];\n      \n      if (confidence >= this.config.confidenceThreshold) {\n        const command = this.commands[i];\n        \n        // 跳过静音和未知指令（除非置信度很高）\n        if ((command === '_silence_' || command === '_unknown_') && confidence < 0.9) {\n          continue;\n        }\n        \n        results.push({\n          command,\n          confidence,\n          commandIndex: i,\n          className: command, // 为了与YAMNet保持一致的接口\n          classIndex: i\n        });\n      }\n    }\n    \n    // 按置信度排序并返回前K个结果\n    return results\n      .sort((a, b) => b.confidence - a.confidence)\n      .slice(0, this.config.topK);\n  }\n\n  /**\n   * 更新统计信息\n   * @param {number} inferenceTime - 推理时间\n   * @param {Array} results - 检测结果\n   */\n  updateStats(inferenceTime, results) {\n    this.stats.totalDetections++;\n    this.stats.lastInferenceTime = inferenceTime;\n    \n    // 计算移动平均\n    const alpha = 0.1;\n    this.stats.averageInferenceTime = \n      this.stats.averageInferenceTime * (1 - alpha) + inferenceTime * alpha;\n    \n    // 统计指令频次\n    results.forEach(result => {\n      const command = result.command;\n      this.stats.commandCounts[command] = (this.stats.commandCounts[command] || 0) + 1;\n    });\n  }\n\n  /**\n   * 获取检测器信息\n   * @returns {Object} 检测器信息\n   */\n  getInfo() {\n    return {\n      isInitialized: this.isInitialized,\n      modelType: 'SpeechCommands',\n      commandCount: this.commands ? this.commands.length : 0,\n      config: this.config,\n      stats: this.stats\n    };\n  }\n\n  /**\n   * 获取类别数量\n   * @returns {number} 类别数量\n   */\n  getClassCount() {\n    return this.commands ? this.commands.length : 0;\n  }\n\n  /**\n   * 更新配置\n   * @param {Object} newConfig - 新配置\n   */\n  updateConfig(newConfig) {\n    this.config = { ...this.config, ...newConfig };\n  }\n\n  /**\n   * 获取所有支持的指令\n   * @returns {Array} 指令列表\n   */\n  getCommands() {\n    return this.commands ? [...this.commands] : [];\n  }\n\n  /**\n   * 获取性能统计\n   * @returns {Object} 性能统计\n   */\n  getStats() {\n    return { ...this.stats };\n  }\n\n  /**\n   * 销毁检测器\n   */\n  dispose() {\n    if (this.model) {\n      this.model.dispose();\n      this.model = null;\n    }\n    \n    this.commands = null;\n    this.isInitialized = false;\n    \n    console.log('🗣️ SpeechCommandDetector: Disposed');\n  }\n}\n", "/**\n * 模型管理器 - 统一管理AI模型的加载、缓存和生命周期\n */\nimport * as tf from '@tensorflow/tfjs';\n\nexport class ModelManager {\n  constructor() {\n    this.models = new Map();\n    this.modelCache = new Map();\n    this.isInitialized = false;\n    \n    // 配置\n    this.config = {\n      enableCaching: true,\n      maxCacheSize: 3, // 最大缓存模型数量\n      enablePreloading: true,\n      modelTimeout: 30000, // 30秒超时\n      retryAttempts: 3\n    };\n    \n    // 模型注册表\n    this.modelRegistry = {\n      yamnet: {\n        url: 'https://tfhub.dev/google/tfjs-model/yamnet/tfjs/1',\n        type: 'graph',\n        size: '~5MB',\n        description: 'YAMNet audio event classification'\n      },\n      speechCommands: {\n        url: 'https://storage.googleapis.com/tfjs-models/tfjs/speech-commands/v0.4/browser_fft/18w/model.json',\n        type: 'layers',\n        size: '~2MB',\n        description: 'Speech commands recognition'\n      }\n    };\n    \n    // 统计信息\n    this.stats = {\n      modelsLoaded: 0,\n      totalLoadTime: 0,\n      cacheHits: 0,\n      cacheMisses: 0,\n      errors: 0\n    };\n  }\n\n  /**\n   * 初始化模型管理器\n   */\n  async initialize() {\n    try {\n      console.log('🔧 ModelManager: Initializing...');\n      \n      // 检查TensorFlow.js状态\n      await tf.ready();\n      \n      // 设置内存管理\n      this.setupMemoryManagement();\n      \n      // 预加载关键模型（如果启用）\n      if (this.config.enablePreloading) {\n        await this.preloadModels();\n      }\n      \n      this.isInitialized = true;\n      console.log('✅ ModelManager: Initialized successfully');\n      \n    } catch (error) {\n      console.error('❌ ModelManager: Initialization failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 设置内存管理\n   */\n  setupMemoryManagement() {\n    // 设置TensorFlow.js内存管理参数\n    tf.env().set('WEBGL_DELETE_TEXTURE_THRESHOLD', 0);\n    tf.env().set('WEBGL_FORCE_F16_TEXTURES', false);\n    \n    // 监控内存使用\n    this.memoryMonitorInterval = setInterval(() => {\n      const memory = tf.memory();\n      if (memory.numBytes > 100 * 1024 * 1024) { // 100MB\n        console.warn('⚠️ High memory usage detected:', memory);\n        this.cleanupUnusedModels();\n      }\n    }, 10000); // 每10秒检查一次\n  }\n\n  /**\n   * 预加载模型\n   */\n  async preloadModels() {\n    try {\n      console.log('📥 Preloading critical models...');\n      \n      // 预加载YAMNet模型（优先级最高）\n      await this.loadModel('yamnet');\n      \n      console.log('✅ Critical models preloaded');\n      \n    } catch (error) {\n      console.warn('⚠️ Model preloading failed:', error);\n      // 预加载失败不影响主要功能\n    }\n  }\n\n  /**\n   * 加载模型\n   * @param {string} modelName - 模型名称\n   * @param {Object} options - 加载选项\n   * @returns {Promise<tf.LayersModel|tf.GraphModel>} 加载的模型\n   */\n  async loadModel(modelName, options = {}) {\n    try {\n      // 检查缓存\n      if (this.config.enableCaching && this.modelCache.has(modelName)) {\n        console.log(`📋 Loading ${modelName} from cache`);\n        this.stats.cacheHits++;\n        return this.modelCache.get(modelName);\n      }\n      \n      this.stats.cacheMisses++;\n      \n      // 获取模型配置\n      const modelConfig = this.modelRegistry[modelName];\n      if (!modelConfig) {\n        throw new Error(`Unknown model: ${modelName}`);\n      }\n      \n      console.log(`📥 Loading ${modelName} model...`);\n      const startTime = performance.now();\n      \n      // 加载模型\n      const model = await this.loadModelWithRetry(modelConfig, options);\n      \n      const loadTime = performance.now() - startTime;\n      console.log(`✅ ${modelName} loaded in ${loadTime.toFixed(2)}ms`);\n      \n      // 缓存模型\n      if (this.config.enableCaching) {\n        this.cacheModel(modelName, model);\n      }\n      \n      // 更新统计\n      this.stats.modelsLoaded++;\n      this.stats.totalLoadTime += loadTime;\n      \n      return model;\n      \n    } catch (error) {\n      console.error(`❌ Failed to load ${modelName}:`, error);\n      this.stats.errors++;\n      throw error;\n    }\n  }\n\n  /**\n   * 带重试的模型加载\n   * @param {Object} modelConfig - 模型配置\n   * @param {Object} options - 加载选项\n   * @returns {Promise<tf.LayersModel|tf.GraphModel>} 加载的模型\n   */\n  async loadModelWithRetry(modelConfig, options) {\n    let lastError;\n    \n    for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {\n      try {\n        // 设置超时\n        const loadPromise = this.loadModelFromUrl(modelConfig, options);\n        const timeoutPromise = new Promise((_, reject) => {\n          setTimeout(() => reject(new Error('Model load timeout')), this.config.modelTimeout);\n        });\n        \n        return await Promise.race([loadPromise, timeoutPromise]);\n        \n      } catch (error) {\n        lastError = error;\n        console.warn(`⚠️ Model load attempt ${attempt} failed:`, error.message);\n        \n        if (attempt < this.config.retryAttempts) {\n          // 指数退避\n          const delay = Math.pow(2, attempt) * 1000;\n          await new Promise(resolve => setTimeout(resolve, delay));\n        }\n      }\n    }\n    \n    throw lastError;\n  }\n\n  /**\n   * 从URL加载模型\n   * @param {Object} modelConfig - 模型配置\n   * @param {Object} options - 加载选项\n   * @returns {Promise<tf.LayersModel|tf.GraphModel>} 加载的模型\n   */\n  async loadModelFromUrl(modelConfig, options) {\n    const loadOptions = {\n      ...options,\n      onProgress: (fraction) => {\n        console.log(`📊 Loading progress: ${(fraction * 100).toFixed(1)}%`);\n      }\n    };\n    \n    if (modelConfig.type === 'graph') {\n      return await tf.loadGraphModel(modelConfig.url, loadOptions);\n    } else if (modelConfig.type === 'layers') {\n      return await tf.loadLayersModel(modelConfig.url, loadOptions);\n    } else {\n      throw new Error(`Unsupported model type: ${modelConfig.type}`);\n    }\n  }\n\n  /**\n   * 缓存模型\n   * @param {string} modelName - 模型名称\n   * @param {tf.LayersModel|tf.GraphModel} model - 模型实例\n   */\n  cacheModel(modelName, model) {\n    // 检查缓存大小限制\n    if (this.modelCache.size >= this.config.maxCacheSize) {\n      // 移除最旧的模型\n      const oldestModel = this.modelCache.keys().next().value;\n      this.removeFromCache(oldestModel);\n    }\n    \n    this.modelCache.set(modelName, model);\n    console.log(`📋 Cached model: ${modelName}`);\n  }\n\n  /**\n   * 从缓存中移除模型\n   * @param {string} modelName - 模型名称\n   */\n  removeFromCache(modelName) {\n    if (this.modelCache.has(modelName)) {\n      const model = this.modelCache.get(modelName);\n      model.dispose();\n      this.modelCache.delete(modelName);\n      console.log(`🗑️ Removed from cache: ${modelName}`);\n    }\n  }\n\n  /**\n   * 清理未使用的模型\n   */\n  cleanupUnusedModels() {\n    console.log('🧹 Cleaning up unused models...');\n    \n    // 这里可以实现更复杂的清理逻辑\n    // 例如基于最后使用时间、内存压力等\n    \n    const memory = tf.memory();\n    console.log('💾 Memory after cleanup:', memory);\n  }\n\n  /**\n   * 获取模型信息\n   * @param {string} modelName - 模型名称\n   * @returns {Object|null} 模型信息\n   */\n  getModelInfo(modelName) {\n    const config = this.modelRegistry[modelName];\n    if (!config) {\n      return null;\n    }\n    \n    return {\n      ...config,\n      cached: this.modelCache.has(modelName),\n      loaded: this.models.has(modelName)\n    };\n  }\n\n  /**\n   * 获取所有可用模型\n   * @returns {Array} 模型列表\n   */\n  getAvailableModels() {\n    return Object.keys(this.modelRegistry).map(name => ({\n      name,\n      ...this.getModelInfo(name)\n    }));\n  }\n\n  /**\n   * 获取缓存状态\n   * @returns {Object} 缓存状态\n   */\n  getCacheStatus() {\n    return {\n      size: this.modelCache.size,\n      maxSize: this.config.maxCacheSize,\n      models: Array.from(this.modelCache.keys()),\n      hitRate: this.stats.cacheHits / (this.stats.cacheHits + this.stats.cacheMisses) || 0\n    };\n  }\n\n  /**\n   * 获取统计信息\n   * @returns {Object} 统计信息\n   */\n  getStats() {\n    return {\n      ...this.stats,\n      averageLoadTime: this.stats.modelsLoaded > 0 ? \n        this.stats.totalLoadTime / this.stats.modelsLoaded : 0,\n      memoryUsage: tf.memory(),\n      cacheStatus: this.getCacheStatus()\n    };\n  }\n\n  /**\n   * 预热模型\n   * @param {string} modelName - 模型名称\n   */\n  async warmupModel(modelName) {\n    try {\n      const model = await this.loadModel(modelName);\n      \n      // 创建虚拟输入进行预热\n      const inputShape = model.inputs[0].shape;\n      const dummyInput = tf.zeros(inputShape.slice(1));\n      const batchedInput = dummyInput.expandDims(0);\n      \n      // 执行推理\n      const output = model.predict(batchedInput);\n      \n      // 清理\n      dummyInput.dispose();\n      batchedInput.dispose();\n      if (Array.isArray(output)) {\n        output.forEach(tensor => tensor.dispose());\n      } else {\n        output.dispose();\n      }\n      \n      console.log(`🔥 Model ${modelName} warmed up`);\n      \n    } catch (error) {\n      console.warn(`⚠️ Failed to warmup ${modelName}:`, error);\n    }\n  }\n\n  /**\n   * 清理所有资源\n   */\n  dispose() {\n    // 清理缓存的模型\n    for (const [name, model] of this.modelCache) {\n      model.dispose();\n      console.log(`🗑️ Disposed cached model: ${name}`);\n    }\n    this.modelCache.clear();\n    \n    // 清理注册的模型\n    this.models.clear();\n    \n    // 清理内存监控\n    if (this.memoryMonitorInterval) {\n      clearInterval(this.memoryMonitorInterval);\n    }\n    \n    this.isInitialized = false;\n    console.log('🔧 ModelManager: Disposed');\n  }\n}\n", "/**\n * AI分类器 - 统一管理AI模型和推理\n * 支持多种模型：YAMNet、语音指令检测等\n */\nimport * as tf from '@tensorflow/tfjs';\nimport '@tensorflow/tfjs-backend-webgl';\nimport eventBus, { EVENTS } from './EventBus.js';\nimport { YAMNetClassifier } from '../ai/YAMNetClassifier.js';\nimport { SpeechCommandDetector } from '../ai/SpeechCommandDetector.js';\nimport { ModelManager } from '../ai/ModelManager.js';\n\nexport class AIClassifier {\n  constructor() {\n    this.modelManager = new ModelManager();\n    this.yamnetClassifier = null;\n    this.speechCommandDetector = null;\n    \n    this.isInitialized = false;\n    this.isProcessing = false;\n    \n    // 配置\n    this.config = {\n      enableYAMNet: true,\n      enableSpeechCommands: true,\n      confidenceThreshold: 0.5,\n      maxPredictions: 5,\n      processingMode: 'realtime', // 'realtime' | 'batch'\n      enableGPU: true\n    };\n    \n    // 性能统计\n    this.stats = {\n      totalInferences: 0,\n      averageInferenceTime: 0,\n      lastInferenceTime: 0,\n      errorCount: 0\n    };\n  }\n\n  /**\n   * 初始化AI分类器\n   */\n  async initialize() {\n    try {\n      console.log('🤖 AIClassifier: Initializing...');\n      \n      // 1. 设置TensorFlow.js后端\n      await this.setupTensorFlowBackend();\n      \n      // 2. 初始化模型管理器\n      await this.modelManager.initialize();\n      \n      // 3. 加载模型\n      await this.loadModels();\n      \n      this.isInitialized = true;\n      \n      eventBus.emit(EVENTS.AI_MODEL_LOADED, {\n        models: this.getLoadedModels(),\n        backend: tf.getBackend(),\n        memory: tf.memory()\n      });\n      \n      console.log('✅ AIClassifier: Initialized successfully');\n      console.log('📊 TensorFlow.js Backend:', tf.getBackend());\n      console.log('💾 Memory usage:', tf.memory());\n      \n    } catch (error) {\n      console.error('❌ AIClassifier: Initialization failed:', error);\n      eventBus.emit(EVENTS.AI_MODEL_ERROR, { error: error.message });\n      throw error;\n    }\n  }\n\n  /**\n   * 设置TensorFlow.js后端\n   */\n  async setupTensorFlowBackend() {\n    try {\n      // 检查WebGL支持\n      if (this.config.enableGPU && await tf.ready()) {\n        const backends = tf.engine().backendNames();\n        console.log('🔧 Available backends:', backends);\n        \n        if (backends.includes('webgl')) {\n          await tf.setBackend('webgl');\n          console.log('✅ Using WebGL backend for GPU acceleration');\n        } else {\n          console.warn('⚠️ WebGL not available, falling back to CPU');\n          await tf.setBackend('cpu');\n        }\n      } else {\n        await tf.setBackend('cpu');\n        console.log('💻 Using CPU backend');\n      }\n      \n      // 等待后端准备就绪\n      await tf.ready();\n      \n      // 设置内存管理\n      tf.env().set('WEBGL_DELETE_TEXTURE_THRESHOLD', 0);\n      tf.env().set('WEBGL_FORCE_F16_TEXTURES', false);\n      \n    } catch (error) {\n      console.error('Failed to setup TensorFlow.js backend:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 加载AI模型\n   */\n  async loadModels() {\n    const loadPromises = [];\n    \n    // 加载YAMNet模型\n    if (this.config.enableYAMNet) {\n      loadPromises.push(this.loadYAMNetModel());\n    }\n    \n    // 加载语音指令检测模型\n    if (this.config.enableSpeechCommands) {\n      loadPromises.push(this.loadSpeechCommandModel());\n    }\n    \n    // 并行加载所有模型\n    await Promise.all(loadPromises);\n  }\n\n  /**\n   * 加载YAMNet模型\n   */\n  async loadYAMNetModel() {\n    try {\n      console.log('📥 Loading YAMNet model...');\n      \n      this.yamnetClassifier = new YAMNetClassifier();\n      await this.yamnetClassifier.initialize();\n      \n      console.log('✅ YAMNet model loaded successfully');\n      \n    } catch (error) {\n      console.error('❌ Failed to load YAMNet model:', error);\n      this.config.enableYAMNet = false;\n      throw error;\n    }\n  }\n\n  /**\n   * 加载语音指令检测模型\n   */\n  async loadSpeechCommandModel() {\n    try {\n      console.log('📥 Loading Speech Command model...');\n      \n      this.speechCommandDetector = new SpeechCommandDetector();\n      await this.speechCommandDetector.initialize();\n      \n      console.log('✅ Speech Command model loaded successfully');\n      \n    } catch (error) {\n      console.error('❌ Failed to load Speech Command model:', error);\n      this.config.enableSpeechCommands = false;\n      // 语音指令模型失败不影响主要功能\n    }\n  }\n\n  /**\n   * 执行音频分类\n   * @param {Object} audioFeatures - 音频特征数据\n   * @returns {Promise<Object>} 分类结果\n   */\n  async classify(audioFeatures) {\n    if (!this.isInitialized) {\n      throw new Error('AIClassifier not initialized');\n    }\n\n    const startTime = performance.now();\n    \n    try {\n      eventBus.emit(EVENTS.AI_PROCESSING_START);\n      \n      const results = {\n        timestamp: Date.now(),\n        predictions: [],\n        audioEvents: [],\n        speechCommands: [],\n        confidence: 0,\n        processingTime: 0\n      };\n      \n      // 并行执行多个模型推理\n      const inferencePromises = [];\n      \n      // YAMNet音频事件分类\n      if (this.yamnetClassifier && this.config.enableYAMNet) {\n        inferencePromises.push(\n          this.yamnetClassifier.classify(audioFeatures)\n            .then(result => ({ type: 'yamnet', result }))\n            .catch(error => ({ type: 'yamnet', error }))\n        );\n      }\n      \n      // 语音指令检测\n      if (this.speechCommandDetector && this.config.enableSpeechCommands) {\n        inferencePromises.push(\n          this.speechCommandDetector.detect(audioFeatures)\n            .then(result => ({ type: 'speech', result }))\n            .catch(error => ({ type: 'speech', error }))\n        );\n      }\n      \n      // 等待所有推理完成\n      const inferenceResults = await Promise.all(inferencePromises);\n      \n      // 处理推理结果\n      for (const inference of inferenceResults) {\n        if (inference.error) {\n          console.error(`${inference.type} inference error:`, inference.error);\n          this.stats.errorCount++;\n          continue;\n        }\n        \n        if (inference.type === 'yamnet' && inference.result) {\n          results.audioEvents = inference.result.predictions || [];\n          results.predictions.push(...inference.result.predictions || []);\n        }\n        \n        if (inference.type === 'speech' && inference.result) {\n          results.speechCommands = inference.result.commands || [];\n          results.predictions.push(...inference.result.commands || []);\n        }\n      }\n      \n      // 过滤和排序预测结果\n      results.predictions = this.filterAndSortPredictions(results.predictions);\n      results.confidence = results.predictions.length > 0 ? results.predictions[0].confidence : 0;\n      \n      // 计算处理时间\n      const endTime = performance.now();\n      results.processingTime = endTime - startTime;\n      \n      // 更新统计信息\n      this.updateStats(results.processingTime);\n      \n      // 发送分类结果事件\n      eventBus.emit(EVENTS.AI_CLASSIFICATION_RESULT, results);\n      eventBus.emit(EVENTS.AI_PROCESSING_END);\n      \n      return results;\n      \n    } catch (error) {\n      console.error('AIClassifier: Classification error:', error);\n      this.stats.errorCount++;\n      eventBus.emit(EVENTS.AI_MODEL_ERROR, { error: error.message });\n      throw error;\n    }\n  }\n\n  /**\n   * 过滤和排序预测结果\n   * @param {Array} predictions - 原始预测结果\n   * @returns {Array} 过滤后的预测结果\n   */\n  filterAndSortPredictions(predictions) {\n    return predictions\n      .filter(pred => pred.confidence >= this.config.confidenceThreshold)\n      .sort((a, b) => b.confidence - a.confidence)\n      .slice(0, this.config.maxPredictions);\n  }\n\n  /**\n   * 更新统计信息\n   * @param {number} inferenceTime - 推理时间\n   */\n  updateStats(inferenceTime) {\n    this.stats.totalInferences++;\n    this.stats.lastInferenceTime = inferenceTime;\n    \n    // 计算平均推理时间（移动平均）\n    const alpha = 0.1; // 平滑因子\n    this.stats.averageInferenceTime = \n      this.stats.averageInferenceTime * (1 - alpha) + inferenceTime * alpha;\n  }\n\n  /**\n   * 获取已加载的模型列表\n   * @returns {Array} 模型列表\n   */\n  getLoadedModels() {\n    const models = [];\n    \n    if (this.yamnetClassifier) {\n      models.push({\n        name: 'YAMNet',\n        type: 'audio_classification',\n        classes: 521,\n        status: 'loaded'\n      });\n    }\n    \n    if (this.speechCommandDetector) {\n      models.push({\n        name: 'SpeechCommands',\n        type: 'speech_recognition',\n        classes: this.speechCommandDetector.getClassCount(),\n        status: 'loaded'\n      });\n    }\n    \n    return models;\n  }\n\n  /**\n   * 获取模型信息\n   * @returns {Object} 模型信息\n   */\n  getModelInfo() {\n    return {\n      isInitialized: this.isInitialized,\n      backend: tf.getBackend(),\n      memory: tf.memory(),\n      models: this.getLoadedModels(),\n      config: this.config,\n      stats: this.stats\n    };\n  }\n\n  /**\n   * 更新配置\n   * @param {Object} newConfig - 新配置\n   */\n  updateConfig(newConfig) {\n    this.config = { ...this.config, ...newConfig };\n    \n    // 更新子模型配置\n    if (this.yamnetClassifier) {\n      this.yamnetClassifier.updateConfig(newConfig);\n    }\n    \n    if (this.speechCommandDetector) {\n      this.speechCommandDetector.updateConfig(newConfig);\n    }\n  }\n\n  /**\n   * 获取性能统计\n   * @returns {Object} 性能统计\n   */\n  getStats() {\n    return {\n      ...this.stats,\n      memoryUsage: tf.memory(),\n      backend: tf.getBackend()\n    };\n  }\n\n  /**\n   * 清理内存\n   */\n  cleanupMemory() {\n    // 清理TensorFlow.js内存\n    tf.disposeVariables();\n    \n    // 强制垃圾回收（如果支持）\n    if (window.gc) {\n      window.gc();\n    }\n    \n    console.log('🧹 Memory cleanup completed');\n    console.log('💾 Current memory usage:', tf.memory());\n  }\n\n  /**\n   * 销毁AI分类器\n   */\n  dispose() {\n    if (this.yamnetClassifier) {\n      this.yamnetClassifier.dispose();\n    }\n    \n    if (this.speechCommandDetector) {\n      this.speechCommandDetector.dispose();\n    }\n    \n    if (this.modelManager) {\n      this.modelManager.dispose();\n    }\n    \n    this.cleanupMemory();\n    \n    this.isInitialized = false;\n    console.log('🤖 AIClassifier: Disposed');\n  }\n}\n", "/**\n * 标签管理器 - 管理音频事件标签的显示和动画\n */\nexport class LabelManager {\n  constructor() {\n    this.labels = new Map();\n    this.container = null;\n    this.isInitialized = false;\n    \n    // 配置\n    this.config = {\n      maxLabels: 5,\n      labelDuration: 3000,\n      fadeInDuration: 300,\n      fadeOutDuration: 500,\n      position: 'top-right',\n      fontSize: 'medium',\n      enableAnimations: true\n    };\n    \n    // 样式\n    this.styles = {\n      container: {\n        position: 'fixed',\n        zIndex: '10000',\n        pointerEvents: 'none',\n        fontFamily: 'Arial, sans-serif',\n        fontSize: '14px'\n      },\n      label: {\n        background: 'rgba(0, 0, 0, 0.8)',\n        color: 'white',\n        padding: '8px 12px',\n        borderRadius: '4px',\n        marginBottom: '4px',\n        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',\n        transition: 'all 0.3s ease',\n        maxWidth: '200px',\n        wordWrap: 'break-word'\n      }\n    };\n    \n    // 标签ID计数器\n    this.labelIdCounter = 0;\n  }\n\n  /**\n   * 初始化标签管理器\n   */\n  initialize() {\n    if (this.isInitialized) {\n      return;\n    }\n    \n    console.log('🏷️ LabelManager: Initializing...');\n    \n    // 创建标签容器\n    this.createContainer();\n    \n    this.isInitialized = true;\n    console.log('✅ LabelManager: Initialized successfully');\n  }\n\n  /**\n   * 创建标签容器\n   */\n  createContainer() {\n    this.container = document.createElement('div');\n    this.container.id = 'audio-labels-container';\n    \n    // 应用容器样式\n    Object.assign(this.container.style, this.styles.container);\n    \n    // 设置位置\n    this.updateContainerPosition();\n    \n    // 添加到页面\n    document.body.appendChild(this.container);\n    \n    console.log('🏷️ Label container created');\n  }\n\n  /**\n   * 更新容器位置\n   */\n  updateContainerPosition() {\n    if (!this.container) {\n      return;\n    }\n    \n    // 清除之前的位置样式\n    this.container.style.top = '';\n    this.container.style.bottom = '';\n    this.container.style.left = '';\n    this.container.style.right = '';\n    \n    switch (this.config.position) {\n      case 'top-left':\n        this.container.style.top = '20px';\n        this.container.style.left = '20px';\n        break;\n      case 'top-right':\n        this.container.style.top = '20px';\n        this.container.style.right = '20px';\n        break;\n      case 'bottom-left':\n        this.container.style.bottom = '20px';\n        this.container.style.left = '20px';\n        break;\n      case 'bottom-right':\n        this.container.style.bottom = '20px';\n        this.container.style.right = '20px';\n        break;\n      default:\n        this.container.style.top = '20px';\n        this.container.style.right = '20px';\n    }\n  }\n\n  /**\n   * 显示标签\n   * @param {string} text - 标签文本\n   * @param {number} confidence - 置信度\n   * @param {Object} options - 选项\n   * @returns {string} 标签ID\n   */\n  showLabel(text, confidence = 1.0, options = {}) {\n    if (!this.isInitialized) {\n      this.initialize();\n    }\n    \n    // 生成标签ID\n    const labelId = `label-${++this.labelIdCounter}`;\n    \n    // 创建标签元素\n    const labelElement = this.createLabelElement(text, confidence, options);\n    \n    // 检查标签数量限制\n    this.enforceMaxLabels();\n    \n    // 添加到容器\n    this.container.appendChild(labelElement);\n    \n    // 存储标签信息\n    this.labels.set(labelId, {\n      element: labelElement,\n      text,\n      confidence,\n      timestamp: Date.now(),\n      options\n    });\n    \n    // 应用进入动画\n    if (this.config.enableAnimations) {\n      this.animateIn(labelElement);\n    }\n    \n    // 设置自动移除\n    setTimeout(() => {\n      this.hideLabel(labelId);\n    }, this.config.labelDuration);\n    \n    console.log(`🏷️ Label shown: \"${text}\" (confidence: ${confidence.toFixed(2)})`);\n    \n    return labelId;\n  }\n\n  /**\n   * 创建标签元素\n   * @param {string} text - 标签文本\n   * @param {number} confidence - 置信度\n   * @param {Object} options - 选项\n   * @returns {HTMLElement} 标签元素\n   */\n  createLabelElement(text, confidence, options) {\n    const element = document.createElement('div');\n    element.className = 'audio-event-label';\n    \n    // 应用基础样式\n    Object.assign(element.style, this.styles.label);\n    \n    // 根据置信度调整样式\n    const opacity = Math.max(0.6, confidence);\n    element.style.background = `rgba(0, 0, 0, ${opacity * 0.8})`;\n    \n    // 根据事件类型调整颜色\n    const color = this.getEventColor(text);\n    if (color) {\n      element.style.borderLeft = `4px solid ${color}`;\n    }\n    \n    // 设置内容\n    const confidenceText = confidence < 1.0 ? ` (${(confidence * 100).toFixed(0)}%)` : '';\n    element.innerHTML = `\n      <div style=\"font-weight: bold; margin-bottom: 2px;\">\n        ${this.formatEventText(text)}\n      </div>\n      ${confidenceText ? `<div style=\"font-size: 0.8em; opacity: 0.8;\">${confidenceText}</div>` : ''}\n    `;\n    \n    // 应用自定义样式\n    if (options.style) {\n      Object.assign(element.style, options.style);\n    }\n    \n    return element;\n  }\n\n  /**\n   * 格式化事件文本\n   * @param {string} text - 原始文本\n   * @returns {string} 格式化后的文本\n   */\n  formatEventText(text) {\n    // 添加图标映射\n    const iconMap = {\n      'Speech': '🗣️',\n      'Music': '🎵',\n      'Laughter': '😄',\n      'Applause': '👏',\n      'Crying': '😢',\n      'Footsteps': '👣',\n      'Door': '🚪',\n      'Knock': '✊',\n      'Glass': '🥃',\n      'Gunshot': '💥',\n      'Siren': '🚨',\n      'Car': '🚗',\n      'Dog': '🐕',\n      'Cat': '🐱',\n      'Bird': '🐦',\n      'Water': '💧',\n      'Wind': '💨',\n      'Rain': '🌧️',\n      'Thunder': '⛈️',\n      'Fire': '🔥',\n      'Explosion': '💥',\n      'Whistle': '🎵',\n      'Bell': '🔔',\n      'Horn': '📯',\n      'Alarm': '⏰',\n      'Phone': '📞'\n    };\n    \n    const icon = iconMap[text] || '🔊';\n    return `${icon} ${text}`;\n  }\n\n  /**\n   * 获取事件颜色\n   * @param {string} eventType - 事件类型\n   * @returns {string|null} 颜色值\n   */\n  getEventColor(eventType) {\n    const colorMap = {\n      'Speech': '#4CAF50',\n      'Music': '#2196F3',\n      'Gunshot': '#F44336',\n      'Explosion': '#FF5722',\n      'Siren': '#FF9800',\n      'Alarm': '#FF9800',\n      'Crying': '#9C27B0',\n      'Laughter': '#FFEB3B',\n      'Applause': '#4CAF50'\n    };\n    \n    return colorMap[eventType] || null;\n  }\n\n  /**\n   * 执行进入动画\n   * @param {HTMLElement} element - 标签元素\n   */\n  animateIn(element) {\n    element.style.opacity = '0';\n    element.style.transform = 'translateX(20px)';\n    \n    // 强制重排\n    element.offsetHeight;\n    \n    element.style.transition = `all ${this.config.fadeInDuration}ms ease`;\n    element.style.opacity = '1';\n    element.style.transform = 'translateX(0)';\n  }\n\n  /**\n   * 执行退出动画\n   * @param {HTMLElement} element - 标签元素\n   * @returns {Promise} 动画完成Promise\n   */\n  animateOut(element) {\n    return new Promise((resolve) => {\n      element.style.transition = `all ${this.config.fadeOutDuration}ms ease`;\n      element.style.opacity = '0';\n      element.style.transform = 'translateX(20px)';\n      \n      setTimeout(() => {\n        resolve();\n      }, this.config.fadeOutDuration);\n    });\n  }\n\n  /**\n   * 隐藏标签\n   * @param {string} labelId - 标签ID\n   */\n  async hideLabel(labelId) {\n    const labelInfo = this.labels.get(labelId);\n    if (!labelInfo) {\n      return;\n    }\n    \n    // 执行退出动画\n    if (this.config.enableAnimations) {\n      await this.animateOut(labelInfo.element);\n    }\n    \n    // 移除元素\n    if (labelInfo.element.parentNode) {\n      labelInfo.element.parentNode.removeChild(labelInfo.element);\n    }\n    \n    // 从映射中删除\n    this.labels.delete(labelId);\n    \n    console.log(`🏷️ Label hidden: \"${labelInfo.text}\"`);\n  }\n\n  /**\n   * 强制执行最大标签数量限制\n   */\n  enforceMaxLabels() {\n    if (this.labels.size >= this.config.maxLabels) {\n      // 移除最旧的标签\n      const oldestLabelId = this.labels.keys().next().value;\n      this.hideLabel(oldestLabelId);\n    }\n  }\n\n  /**\n   * 清除所有标签\n   */\n  clearAllLabels() {\n    const labelIds = Array.from(this.labels.keys());\n    labelIds.forEach(labelId => {\n      this.hideLabel(labelId);\n    });\n    \n    console.log('🏷️ All labels cleared');\n  }\n\n  /**\n   * 更新标签\n   * @param {string} labelId - 标签ID\n   * @param {Object} updates - 更新数据\n   */\n  updateLabel(labelId, updates) {\n    const labelInfo = this.labels.get(labelId);\n    if (!labelInfo) {\n      return;\n    }\n    \n    // 更新文本\n    if (updates.text !== undefined) {\n      labelInfo.text = updates.text;\n      labelInfo.element.querySelector('div').textContent = this.formatEventText(updates.text);\n    }\n    \n    // 更新置信度\n    if (updates.confidence !== undefined) {\n      labelInfo.confidence = updates.confidence;\n      const confidenceDiv = labelInfo.element.querySelector('div:last-child');\n      if (confidenceDiv) {\n        confidenceDiv.textContent = `(${(updates.confidence * 100).toFixed(0)}%)`;\n      }\n    }\n    \n    // 更新样式\n    if (updates.style) {\n      Object.assign(labelInfo.element.style, updates.style);\n    }\n  }\n\n  /**\n   * 获取当前标签列表\n   * @returns {Array} 标签列表\n   */\n  getCurrentLabels() {\n    return Array.from(this.labels.entries()).map(([id, info]) => ({\n      id,\n      text: info.text,\n      confidence: info.confidence,\n      timestamp: info.timestamp\n    }));\n  }\n\n  /**\n   * 更新配置\n   * @param {Object} newConfig - 新配置\n   */\n  updateConfig(newConfig) {\n    this.config = { ...this.config, ...newConfig };\n    \n    // 更新容器位置\n    if (newConfig.position) {\n      this.updateContainerPosition();\n    }\n    \n    // 更新字体大小\n    if (newConfig.fontSize && this.container) {\n      const fontSizeMap = {\n        small: '12px',\n        medium: '14px',\n        large: '16px'\n      };\n      this.container.style.fontSize = fontSizeMap[newConfig.fontSize] || '14px';\n    }\n  }\n\n  /**\n   * 获取统计信息\n   * @returns {Object} 统计信息\n   */\n  getStats() {\n    return {\n      currentLabels: this.labels.size,\n      maxLabels: this.config.maxLabels,\n      totalLabelsShown: this.labelIdCounter\n    };\n  }\n\n  /**\n   * 销毁标签管理器\n   */\n  dispose() {\n    // 清除所有标签\n    this.clearAllLabels();\n    \n    // 移除容器\n    if (this.container && this.container.parentNode) {\n      this.container.parentNode.removeChild(this.container);\n    }\n    \n    this.container = null;\n    this.labels.clear();\n    this.isInitialized = false;\n    \n    console.log('🏷️ LabelManager: Disposed');\n  }\n}\n", "/**\n * 可视化渲染器 - 继承并增强现有的音频可视化功能\n */\nexport class VisualizationRenderer {\n  constructor() {\n    this.canvas = null;\n    this.canvasCtx = null;\n    this.videoElement = null;\n    this.isInitialized = false;\n    this.isRunning = false;\n    this.animationFrameId = null;\n    \n    // 配置\n    this.config = {\n      enableFrequencyDomain: true,\n      enableTimeDomain: true,\n      enableHighFrequencyWarning: true,\n      highFrequencyThreshold: 4000,\n      opacity: 0.8,\n      showDebugInfo: false,\n      colorScheme: 'default'\n    };\n    \n    // 颜色方案\n    this.colorSchemes = {\n      default: {\n        lowFreq: { r: 50, g: 150, b: 255 },\n        midFreq: { r: 50, g: 255, b: 150 },\n        highFreq: { r: 255, g: 255, b: 0 },\n        warning: { r: 255, g: 50, b: 50 },\n        waveform: { r: 255, g: 255, b: 255 }\n      },\n      accessibility: {\n        lowFreq: { r: 0, g: 100, b: 200 },\n        midFreq: { r: 0, g: 200, b: 100 },\n        highFreq: { r: 255, g: 150, b: 0 },\n        warning: { r: 200, g: 0, b: 0 },\n        waveform: { r: 255, g: 255, b: 255 }\n      }\n    };\n    \n    // 当前音频数据\n    this.currentAudioData = null;\n    \n    // 性能统计\n    this.stats = {\n      framesRendered: 0,\n      averageRenderTime: 0,\n      lastRenderTime: 0\n    };\n  }\n\n  /**\n   * 初始化可视化渲染器\n   * @param {HTMLVideoElement} videoElement - 视频元素\n   */\n  async initialize(videoElement) {\n    try {\n      if (this.isInitialized) {\n        return;\n      }\n      \n      console.log('🎨 VisualizationRenderer: Initializing...');\n      \n      this.videoElement = videoElement;\n      \n      // 创建Canvas\n      await this.createCanvas();\n      \n      // 设置响应式监听\n      this.setupResponsiveListeners();\n      \n      this.isInitialized = true;\n      console.log('✅ VisualizationRenderer: Initialized successfully');\n      \n    } catch (error) {\n      console.error('❌ VisualizationRenderer: Initialization failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 创建Canvas元素\n   */\n  async createCanvas() {\n    // 寻找合适的容器\n    const videoContainer = this.findVideoContainer();\n    if (!videoContainer) {\n      throw new Error('Could not find suitable video container');\n    }\n    \n    // 创建canvas\n    this.canvas = document.createElement('canvas');\n    this.canvas.id = 'audio-visualizer-canvas-v2';\n    this.canvas.style.cssText = `\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      pointer-events: none;\n      opacity: ${this.config.opacity};\n      z-index: 1000;\n    `;\n    \n    // 获取2D上下文\n    this.canvasCtx = this.canvas.getContext('2d');\n    \n    // 添加到容器\n    videoContainer.appendChild(this.canvas);\n    \n    // 初始化尺寸\n    this.updateCanvasSize();\n    \n    console.log('🎨 Canvas created and added to video container');\n  }\n\n  /**\n   * 寻找视频容器\n   * @returns {HTMLElement|null} 视频容器\n   */\n  findVideoContainer() {\n    // 尝试多种选择器来找到最合适的容器\n    const selectors = [\n      '#movie_player',\n      '.html5-video-container',\n      '.video-stream',\n      '.ytp-player-content'\n    ];\n    \n    for (const selector of selectors) {\n      const container = document.querySelector(selector);\n      if (container && container.contains(this.videoElement)) {\n        return container;\n      }\n    }\n    \n    // 回退到视频元素的父元素\n    return this.videoElement.parentElement;\n  }\n\n  /**\n   * 设置响应式监听器\n   */\n  setupResponsiveListeners() {\n    // 监听窗口大小变化\n    window.addEventListener('resize', () => {\n      this.updateCanvasSize();\n    });\n    \n    // 监听视频容器大小变化\n    if (window.ResizeObserver) {\n      const resizeObserver = new ResizeObserver(() => {\n        this.updateCanvasSize();\n      });\n      \n      if (this.canvas.parentElement) {\n        resizeObserver.observe(this.canvas.parentElement);\n      }\n    }\n  }\n\n  /**\n   * 更新Canvas尺寸\n   */\n  updateCanvasSize() {\n    if (!this.canvas || !this.canvas.parentElement) {\n      return;\n    }\n    \n    const container = this.canvas.parentElement;\n    const rect = container.getBoundingClientRect();\n    \n    // 设置Canvas尺寸\n    this.canvas.width = rect.width;\n    this.canvas.height = rect.height;\n    \n    console.log(`🎨 Canvas size updated: ${rect.width}x${rect.height}`);\n  }\n\n  /**\n   * 开始渲染\n   */\n  start() {\n    if (!this.isInitialized || this.isRunning) {\n      return;\n    }\n    \n    console.log('🎨 Starting visualization rendering...');\n    \n    this.isRunning = true;\n    this.renderLoop();\n  }\n\n  /**\n   * 暂停渲染\n   */\n  pause() {\n    this.isRunning = false;\n    \n    if (this.animationFrameId) {\n      cancelAnimationFrame(this.animationFrameId);\n      this.animationFrameId = null;\n    }\n  }\n\n  /**\n   * 停止渲染\n   */\n  stop() {\n    this.pause();\n    \n    // 清空画布\n    if (this.canvasCtx) {\n      this.canvasCtx.clearRect(0, 0, this.canvas.width, this.canvas.height);\n    }\n  }\n\n  /**\n   * 渲染循环\n   */\n  renderLoop() {\n    if (!this.isRunning) {\n      return;\n    }\n    \n    const startTime = performance.now();\n    \n    try {\n      // 清空画布\n      this.canvasCtx.clearRect(0, 0, this.canvas.width, this.canvas.height);\n      \n      // 渲染可视化\n      if (this.currentAudioData) {\n        this.renderVisualization(this.currentAudioData);\n      } else {\n        this.renderWaitingState();\n      }\n      \n      // 渲染调试信息\n      if (this.config.showDebugInfo) {\n        this.renderDebugInfo();\n      }\n      \n      // 更新性能统计\n      const renderTime = performance.now() - startTime;\n      this.updateStats(renderTime);\n      \n    } catch (error) {\n      console.error('Rendering error:', error);\n    }\n    \n    // 请求下一帧\n    this.animationFrameId = requestAnimationFrame(() => this.renderLoop());\n  }\n\n  /**\n   * 渲染可视化\n   * @param {Object} audioData - 音频数据\n   */\n  renderVisualization(audioData) {\n    // 渲染频域数据\n    if (this.config.enableFrequencyDomain && audioData.raw && audioData.raw.frequency) {\n      this.renderFrequencyDomain(audioData.raw.frequency, audioData.raw.sampleRate);\n    }\n    \n    // 渲染时域数据\n    if (this.config.enableTimeDomain && audioData.raw && audioData.raw.timeDomain) {\n      this.renderTimeDomain(audioData.raw.timeDomain);\n    }\n  }\n\n  /**\n   * 渲染频域数据\n   * @param {Uint8Array} frequencyData - 频域数据\n   * @param {number} sampleRate - 采样率\n   */\n  renderFrequencyDomain(frequencyData, sampleRate) {\n    const bufferLength = frequencyData.length;\n    const barWidth = (this.canvas.width / bufferLength) * 1.5;\n    const nyquist = sampleRate / 2;\n    const colors = this.colorSchemes[this.config.colorScheme];\n    \n    let x = 0;\n    \n    for (let i = 0; i < bufferLength; i++) {\n      const barHeight = (frequencyData[i] / 255.0) * this.canvas.height * 0.5;\n      const currentFrequency = (i * nyquist) / bufferLength;\n      const intensity = frequencyData[i] / 255.0;\n      \n      // 选择颜色\n      let color;\n      if (this.config.enableHighFrequencyWarning && currentFrequency > this.config.highFrequencyThreshold) {\n        // 高频警示区域\n        color = this.interpolateColor(colors.highFreq, colors.warning, intensity);\n      } else if (currentFrequency > 1000) {\n        // 中频区域\n        color = this.interpolateColor(colors.lowFreq, colors.midFreq, intensity);\n      } else {\n        // 低频区域\n        color = this.interpolateColor({ r: 0, g: 0, b: 0 }, colors.lowFreq, intensity);\n      }\n      \n      // 绘制频谱条\n      this.canvasCtx.fillStyle = `rgba(${color.r}, ${color.g}, ${color.b}, ${intensity * 0.8})`;\n      this.canvasCtx.fillRect(x, this.canvas.height - barHeight, barWidth, barHeight);\n      \n      x += barWidth + 1;\n    }\n  }\n\n  /**\n   * 渲染时域数据\n   * @param {Uint8Array} timeDomainData - 时域数据\n   */\n  renderTimeDomain(timeDomainData) {\n    const colors = this.colorSchemes[this.config.colorScheme];\n    \n    this.canvasCtx.lineWidth = 2;\n    this.canvasCtx.strokeStyle = `rgba(${colors.waveform.r}, ${colors.waveform.g}, ${colors.waveform.b}, 0.7)`;\n    this.canvasCtx.beginPath();\n    \n    const sliceWidth = this.canvas.width / timeDomainData.length;\n    let x = 0;\n    \n    for (let i = 0; i < timeDomainData.length; i++) {\n      const v = timeDomainData[i] / 128.0;\n      const y = (v * this.canvas.height) / 2;\n      \n      if (i === 0) {\n        this.canvasCtx.moveTo(x, y);\n      } else {\n        this.canvasCtx.lineTo(x, y);\n      }\n      \n      x += sliceWidth;\n    }\n    \n    this.canvasCtx.lineTo(this.canvas.width, this.canvas.height / 2);\n    this.canvasCtx.stroke();\n  }\n\n  /**\n   * 渲染等待状态\n   */\n  renderWaitingState() {\n    this.canvasCtx.fillStyle = 'rgba(255, 255, 255, 0.1)';\n    this.canvasCtx.fillRect(10, 10, 200, 60);\n    \n    this.canvasCtx.fillStyle = 'rgba(255, 255, 255, 0.8)';\n    this.canvasCtx.font = '16px Arial';\n    this.canvasCtx.fillText('🎵 AI Audio Analysis Ready', 20, 35);\n    this.canvasCtx.fillText('Waiting for audio...', 20, 55);\n  }\n\n  /**\n   * 渲染调试信息\n   */\n  renderDebugInfo() {\n    this.canvasCtx.fillStyle = 'rgba(0, 0, 0, 0.7)';\n    this.canvasCtx.fillRect(10, this.canvas.height - 120, 300, 110);\n    \n    this.canvasCtx.fillStyle = 'white';\n    this.canvasCtx.font = '12px Arial';\n    \n    const lines = [\n      '🎨 Visualization Renderer v2.0',\n      `Canvas: ${this.canvas.width}x${this.canvas.height}`,\n      `Frames: ${this.stats.framesRendered}`,\n      `Avg Render: ${this.stats.averageRenderTime.toFixed(2)}ms`,\n      `Last Render: ${this.stats.lastRenderTime.toFixed(2)}ms`,\n      `Color Scheme: ${this.config.colorScheme}`\n    ];\n    \n    lines.forEach((line, index) => {\n      this.canvasCtx.fillText(line, 20, this.canvas.height - 100 + (index * 15));\n    });\n  }\n\n  /**\n   * 颜色插值\n   * @param {Object} color1 - 起始颜色\n   * @param {Object} color2 - 结束颜色\n   * @param {number} factor - 插值因子 (0-1)\n   * @returns {Object} 插值后的颜色\n   */\n  interpolateColor(color1, color2, factor) {\n    return {\n      r: Math.round(color1.r + (color2.r - color1.r) * factor),\n      g: Math.round(color1.g + (color2.g - color1.g) * factor),\n      b: Math.round(color1.b + (color2.b - color1.b) * factor)\n    };\n  }\n\n  /**\n   * 更新音频数据\n   * @param {Object} audioData - 音频数据\n   */\n  updateVisualization(audioData) {\n    this.currentAudioData = audioData;\n  }\n\n  /**\n   * 更新性能统计\n   * @param {number} renderTime - 渲染时间\n   */\n  updateStats(renderTime) {\n    this.stats.framesRendered++;\n    this.stats.lastRenderTime = renderTime;\n    \n    // 计算移动平均\n    const alpha = 0.1;\n    this.stats.averageRenderTime = \n      this.stats.averageRenderTime * (1 - alpha) + renderTime * alpha;\n  }\n\n  /**\n   * 更新配置\n   * @param {Object} newConfig - 新配置\n   */\n  updateConfig(newConfig) {\n    this.config = { ...this.config, ...newConfig };\n    \n    // 更新透明度\n    if (newConfig.opacity !== undefined && this.canvas) {\n      this.canvas.style.opacity = newConfig.opacity;\n    }\n  }\n\n  /**\n   * 获取统计信息\n   * @returns {Object} 统计信息\n   */\n  getStats() {\n    return { ...this.stats };\n  }\n\n  /**\n   * 销毁渲染器\n   */\n  dispose() {\n    this.stop();\n    \n    // 移除Canvas\n    if (this.canvas && this.canvas.parentElement) {\n      this.canvas.parentElement.removeChild(this.canvas);\n    }\n    \n    this.canvas = null;\n    this.canvasCtx = null;\n    this.videoElement = null;\n    this.currentAudioData = null;\n    this.isInitialized = false;\n    \n    console.log('🎨 VisualizationRenderer: Disposed');\n  }\n}\n", "/**\n * 配置管理器 - 管理用户配置和系统设置\n */\nexport class ConfigManager {\n  constructor() {\n    this.config = null;\n    this.defaultConfig = this.getDefaultConfig();\n    this.storageKey = 'youtube-aural-visual-bridge-config';\n    this.isInitialized = false;\n    \n    // 配置变更监听器\n    this.changeListeners = new Set();\n  }\n\n  /**\n   * 获取默认配置\n   * @returns {Object} 默认配置\n   */\n  getDefaultConfig() {\n    return {\n      // AI模型配置\n      ai: {\n        enableYAMNet: true,\n        enableSpeechCommands: true,\n        yamnetConfidenceThreshold: 0.5,\n        speechCommandsConfidenceThreshold: 0.7,\n        maxPredictions: 5,\n        enableLocalProcessing: true,\n        enableCloudAPI: false,\n        apiEndpoint: '',\n        apiKey: ''\n      },\n      \n      // 用户界面配置\n      ui: {\n        showLabels: true,\n        labelDuration: 3000,\n        maxLabelsOnScreen: 5,\n        labelPosition: 'top-right', // 'top-left', 'top-right', 'bottom-left', 'bottom-right'\n        enableSoundDictionary: true,\n        enableVisualization: true,\n        visualizationOpacity: 0.8,\n        fontSize: 'medium', // 'small', 'medium', 'large'\n        theme: 'auto' // 'light', 'dark', 'auto'\n      },\n      \n      // 性能配置\n      performance: {\n        processingInterval: 100,\n        enableGPUAcceleration: true,\n        maxMemoryUsage: 100 * 1024 * 1024, // 100MB\n        enablePerformanceMonitoring: true,\n        autoOptimization: true\n      },\n      \n      // 音频配置\n      audio: {\n        sampleRate: 16000,\n        fftSize: 2048,\n        smoothingTimeConstant: 0.8,\n        enablePreemphasis: true,\n        windowType: 'hann'\n      },\n      \n      // 可视化配置\n      visualization: {\n        enableFrequencyDomain: true,\n        enableTimeDomain: true,\n        enableHighFrequencyWarning: true,\n        highFrequencyThreshold: 4000,\n        colorScheme: 'default', // 'default', 'accessibility', 'custom'\n        customColors: {\n          lowFreq: '#0066cc',\n          midFreq: '#00cc66',\n          highFreq: '#cc6600',\n          warning: '#cc0000'\n        }\n      },\n      \n      // 无障碍配置\n      accessibility: {\n        enableHighContrast: false,\n        enableLargeText: false,\n        enableScreenReader: false,\n        enableKeyboardNavigation: true,\n        reduceMotion: false\n      },\n      \n      // 调试配置\n      debug: {\n        enableLogging: false,\n        enableDetailedStats: false,\n        enableModelInfo: false,\n        logLevel: 'warn' // 'debug', 'info', 'warn', 'error'\n      }\n    };\n  }\n\n  /**\n   * 加载配置\n   */\n  async loadConfig() {\n    try {\n      console.log('⚙️ ConfigManager: Loading configuration...');\n      \n      // 从存储中加载配置\n      const storedConfig = await this.loadFromStorage();\n      \n      // 合并默认配置和存储的配置\n      this.config = this.mergeConfigs(this.defaultConfig, storedConfig);\n      \n      // 验证配置\n      this.validateConfig();\n      \n      this.isInitialized = true;\n      console.log('✅ ConfigManager: Configuration loaded successfully');\n      \n    } catch (error) {\n      console.error('❌ ConfigManager: Failed to load configuration:', error);\n      // 使用默认配置\n      this.config = { ...this.defaultConfig };\n      this.isInitialized = true;\n    }\n  }\n\n  /**\n   * 从存储中加载配置\n   * @returns {Promise<Object>} 存储的配置\n   */\n  async loadFromStorage() {\n    try {\n      // 尝试从Chrome扩展存储加载\n      if (typeof chrome !== 'undefined' && chrome.storage) {\n        return await this.loadFromChromeStorage();\n      }\n      \n      // 回退到localStorage\n      return this.loadFromLocalStorage();\n      \n    } catch (error) {\n      console.warn('⚠️ Failed to load from storage:', error);\n      return {};\n    }\n  }\n\n  /**\n   * 从Chrome扩展存储加载\n   * @returns {Promise<Object>} 配置对象\n   */\n  async loadFromChromeStorage() {\n    return new Promise((resolve, reject) => {\n      chrome.storage.sync.get([this.storageKey], (result) => {\n        if (chrome.runtime.lastError) {\n          reject(chrome.runtime.lastError);\n        } else {\n          resolve(result[this.storageKey] || {});\n        }\n      });\n    });\n  }\n\n  /**\n   * 从localStorage加载\n   * @returns {Object} 配置对象\n   */\n  loadFromLocalStorage() {\n    const stored = localStorage.getItem(this.storageKey);\n    return stored ? JSON.parse(stored) : {};\n  }\n\n  /**\n   * 保存配置\n   */\n  async saveConfig() {\n    try {\n      // 保存到存储\n      await this.saveToStorage(this.config);\n      \n      // 通知配置变更\n      this.notifyConfigChange();\n      \n      console.log('✅ Configuration saved successfully');\n      \n    } catch (error) {\n      console.error('❌ Failed to save configuration:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 保存到存储\n   * @param {Object} config - 配置对象\n   */\n  async saveToStorage(config) {\n    try {\n      // 尝试保存到Chrome扩展存储\n      if (typeof chrome !== 'undefined' && chrome.storage) {\n        await this.saveToChromeStorage(config);\n      } else {\n        // 回退到localStorage\n        this.saveToLocalStorage(config);\n      }\n    } catch (error) {\n      console.warn('⚠️ Failed to save to primary storage, trying fallback:', error);\n      this.saveToLocalStorage(config);\n    }\n  }\n\n  /**\n   * 保存到Chrome扩展存储\n   * @param {Object} config - 配置对象\n   */\n  async saveToChromeStorage(config) {\n    return new Promise((resolve, reject) => {\n      chrome.storage.sync.set({ [this.storageKey]: config }, () => {\n        if (chrome.runtime.lastError) {\n          reject(chrome.runtime.lastError);\n        } else {\n          resolve();\n        }\n      });\n    });\n  }\n\n  /**\n   * 保存到localStorage\n   * @param {Object} config - 配置对象\n   */\n  saveToLocalStorage(config) {\n    localStorage.setItem(this.storageKey, JSON.stringify(config));\n  }\n\n  /**\n   * 合并配置\n   * @param {Object} defaultConfig - 默认配置\n   * @param {Object} userConfig - 用户配置\n   * @returns {Object} 合并后的配置\n   */\n  mergeConfigs(defaultConfig, userConfig) {\n    const merged = {};\n    \n    for (const key in defaultConfig) {\n      if (typeof defaultConfig[key] === 'object' && !Array.isArray(defaultConfig[key])) {\n        merged[key] = this.mergeConfigs(defaultConfig[key], userConfig[key] || {});\n      } else {\n        merged[key] = userConfig[key] !== undefined ? userConfig[key] : defaultConfig[key];\n      }\n    }\n    \n    return merged;\n  }\n\n  /**\n   * 验证配置\n   */\n  validateConfig() {\n    // 验证数值范围\n    this.config.ai.yamnetConfidenceThreshold = Math.max(0, Math.min(1, this.config.ai.yamnetConfidenceThreshold));\n    this.config.ai.speechCommandsConfidenceThreshold = Math.max(0, Math.min(1, this.config.ai.speechCommandsConfidenceThreshold));\n    this.config.ui.labelDuration = Math.max(1000, Math.min(10000, this.config.ui.labelDuration));\n    this.config.ui.maxLabelsOnScreen = Math.max(1, Math.min(10, this.config.ui.maxLabelsOnScreen));\n    this.config.performance.processingInterval = Math.max(50, Math.min(1000, this.config.performance.processingInterval));\n    \n    // 验证枚举值\n    const validPositions = ['top-left', 'top-right', 'bottom-left', 'bottom-right'];\n    if (!validPositions.includes(this.config.ui.labelPosition)) {\n      this.config.ui.labelPosition = 'top-right';\n    }\n    \n    const validThemes = ['light', 'dark', 'auto'];\n    if (!validThemes.includes(this.config.ui.theme)) {\n      this.config.ui.theme = 'auto';\n    }\n    \n    const validFontSizes = ['small', 'medium', 'large'];\n    if (!validFontSizes.includes(this.config.ui.fontSize)) {\n      this.config.ui.fontSize = 'medium';\n    }\n  }\n\n  /**\n   * 获取配置\n   * @param {string} path - 配置路径（可选）\n   * @returns {*} 配置值\n   */\n  getConfig(path = null) {\n    if (!this.isInitialized) {\n      console.warn('⚠️ ConfigManager not initialized, returning default config');\n      return path ? this.getNestedValue(this.defaultConfig, path) : this.defaultConfig;\n    }\n    \n    if (path) {\n      return this.getNestedValue(this.config, path);\n    }\n    \n    return this.config;\n  }\n\n  /**\n   * 设置配置\n   * @param {string} path - 配置路径\n   * @param {*} value - 配置值\n   */\n  async setConfig(path, value) {\n    if (!this.isInitialized) {\n      throw new Error('ConfigManager not initialized');\n    }\n    \n    this.setNestedValue(this.config, path, value);\n    this.validateConfig();\n    await this.saveConfig();\n  }\n\n  /**\n   * 批量更新配置\n   * @param {Object} updates - 配置更新\n   */\n  async updateConfig(updates) {\n    if (!this.isInitialized) {\n      throw new Error('ConfigManager not initialized');\n    }\n    \n    this.config = this.mergeConfigs(this.config, updates);\n    this.validateConfig();\n    await this.saveConfig();\n  }\n\n  /**\n   * 获取嵌套值\n   * @param {Object} obj - 对象\n   * @param {string} path - 路径\n   * @returns {*} 值\n   */\n  getNestedValue(obj, path) {\n    return path.split('.').reduce((current, key) => current && current[key], obj);\n  }\n\n  /**\n   * 设置嵌套值\n   * @param {Object} obj - 对象\n   * @param {string} path - 路径\n   * @param {*} value - 值\n   */\n  setNestedValue(obj, path, value) {\n    const keys = path.split('.');\n    const lastKey = keys.pop();\n    const target = keys.reduce((current, key) => {\n      if (!current[key] || typeof current[key] !== 'object') {\n        current[key] = {};\n      }\n      return current[key];\n    }, obj);\n    \n    target[lastKey] = value;\n  }\n\n  /**\n   * 重置配置\n   */\n  async resetConfig() {\n    this.config = { ...this.defaultConfig };\n    await this.saveConfig();\n    console.log('⚙️ Configuration reset to defaults');\n  }\n\n  /**\n   * 导出配置\n   * @returns {string} JSON格式的配置\n   */\n  exportConfig() {\n    return JSON.stringify(this.config, null, 2);\n  }\n\n  /**\n   * 导入配置\n   * @param {string} configJson - JSON格式的配置\n   */\n  async importConfig(configJson) {\n    try {\n      const importedConfig = JSON.parse(configJson);\n      this.config = this.mergeConfigs(this.defaultConfig, importedConfig);\n      this.validateConfig();\n      await this.saveConfig();\n      console.log('✅ Configuration imported successfully');\n    } catch (error) {\n      console.error('❌ Failed to import configuration:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 添加配置变更监听器\n   * @param {Function} listener - 监听器函数\n   */\n  addChangeListener(listener) {\n    this.changeListeners.add(listener);\n  }\n\n  /**\n   * 移除配置变更监听器\n   * @param {Function} listener - 监听器函数\n   */\n  removeChangeListener(listener) {\n    this.changeListeners.delete(listener);\n  }\n\n  /**\n   * 通知配置变更\n   */\n  notifyConfigChange() {\n    this.changeListeners.forEach(listener => {\n      try {\n        listener(this.config);\n      } catch (error) {\n        console.error('Error in config change listener:', error);\n      }\n    });\n  }\n\n  /**\n   * 获取配置摘要\n   * @returns {Object} 配置摘要\n   */\n  getConfigSummary() {\n    return {\n      aiEnabled: this.config.ai.enableYAMNet || this.config.ai.enableSpeechCommands,\n      uiEnabled: this.config.ui.showLabels || this.config.ui.enableVisualization,\n      performanceOptimized: this.config.performance.enableGPUAcceleration && this.config.performance.autoOptimization,\n      accessibilityEnabled: Object.values(this.config.accessibility).some(value => value === true)\n    };\n  }\n\n  /**\n   * 销毁配置管理器\n   */\n  dispose() {\n    this.changeListeners.clear();\n    this.isInitialized = false;\n    console.log('⚙️ ConfigManager: Disposed');\n  }\n}\n", "/**\n * YouTube Aural-Visual Bridge - 主内容脚本\n * 集成AI音频分类功能的新版本\n */\nimport { AudioManager } from './core/AudioManager.js';\nimport { AIClassifier } from './core/AIClassifier.js';\nimport { LabelManager } from './ui/LabelManager.js';\nimport { VisualizationRenderer } from './ui/VisualizationRenderer.js';\nimport { PerformanceMonitor } from './utils/PerformanceMonitor.js';\nimport { ConfigManager } from './api/ConfigManager.js';\nimport eventBus, { EVENTS } from './core/EventBus.js';\n\n/**\n * 主应用类\n */\nclass YouTubeAuralVisualBridge {\n  constructor() {\n    this.audioManager = null;\n    this.aiClassifier = null;\n    this.labelManager = null;\n    this.visualizationRenderer = null;\n    this.performanceMonitor = new PerformanceMonitor();\n    this.configManager = new ConfigManager();\n\n    this.isInitialized = false;\n    this.isRunning = false;\n    this.currentVideoElement = null;\n\n    // 已初始化的视频元素集合\n    this.initializedVideos = new WeakSet();\n\n    // 绑定方法\n    this.handleMutations = this.handleMutations.bind(this);\n    this.setupVideoElement = this.setupVideoElement.bind(this);\n    this.handleAudioData = this.handleAudioData.bind(this);\n    this.handleClassificationResult = this.handleClassificationResult.bind(this);\n  }\n\n  /**\n   * 初始化应用\n   */\n  async initialize() {\n    try {\n      console.log('🚀 YouTube Aural-Visual Bridge v2.0 - Initializing...');\n\n      // 1. 加载配置\n      await this.configManager.loadConfig();\n\n      // 2. 初始化性能监控\n      this.performanceMonitor.start();\n\n      // 3. 设置事件监听器\n      this.setupEventListeners();\n\n      // 4. 初始化组件\n      await this.initializeComponents();\n\n      // 5. 设置DOM观察器\n      this.setupMutationObserver();\n\n      // 6. 检查现有视频\n      await this.checkExistingVideos();\n\n      this.isInitialized = true;\n      eventBus.emit(EVENTS.SYSTEM_READY);\n\n      console.log('✅ YouTube Aural-Visual Bridge: Initialized successfully');\n\n    } catch (error) {\n      console.error('❌ YouTube Aural-Visual Bridge: Initialization failed:', error);\n      eventBus.emit(EVENTS.SYSTEM_ERROR, { error: error.message });\n      throw error;\n    }\n  }\n\n  /**\n   * 初始化组件\n   */\n  async initializeComponents() {\n    // 初始化音频管理器\n    this.audioManager = new AudioManager();\n\n    // 初始化AI分类器\n    this.aiClassifier = new AIClassifier();\n    await this.aiClassifier.initialize();\n\n    // 初始化标签管理器\n    this.labelManager = new LabelManager();\n\n    // 初始化可视化渲染器\n    this.visualizationRenderer = new VisualizationRenderer();\n  }\n\n  /**\n   * 设置事件监听器\n   */\n  setupEventListeners() {\n    // 音频数据事件\n    eventBus.on(EVENTS.AUDIO_DATA_AVAILABLE, this.handleAudioData);\n\n    // AI分类结果事件\n    eventBus.on(EVENTS.AI_CLASSIFICATION_RESULT, this.handleClassificationResult);\n\n    // 系统错误事件\n    eventBus.on(EVENTS.SYSTEM_ERROR, (data) => {\n      console.error('System error:', data.error);\n      this.handleSystemError(data.error);\n    });\n\n    // 性能警告事件\n    eventBus.on(EVENTS.UI_PERFORMANCE_WARNING, (data) => {\n      console.warn('Performance warning:', data);\n      this.handlePerformanceWarning(data);\n    });\n  }\n\n  /**\n   * 设置DOM变化观察器\n   */\n  setupMutationObserver() {\n    const observer = new MutationObserver(this.handleMutations);\n\n    observer.observe(document.body, {\n      childList: true,\n      subtree: true\n    });\n\n    console.log('🔍 MutationObserver: Set up to watch for video elements');\n  }\n\n  /**\n   * 处理DOM变化\n   * @param {MutationRecord[]} mutationsList - 变化记录列表\n   */\n  handleMutations(mutationsList) {\n    for (const mutation of mutationsList) {\n      if (mutation.type === 'childList') {\n        mutation.addedNodes.forEach(node => {\n          if (node.nodeType === 1) { // 元素节点\n            let videoElement = null;\n\n            if (node.tagName === 'VIDEO') {\n              videoElement = node;\n            } else {\n              videoElement = node.querySelector('video');\n            }\n\n            if (videoElement && !this.initializedVideos.has(videoElement)) {\n              this.setupVideoElement(videoElement);\n            }\n          }\n        });\n      }\n    }\n  }\n\n  /**\n   * 检查现有视频元素\n   */\n  async checkExistingVideos() {\n    const existingVideo = document.querySelector('video');\n    if (existingVideo && !this.initializedVideos.has(existingVideo)) {\n      console.log('🎥 Found existing video element');\n      await this.setupVideoElement(existingVideo);\n    }\n  }\n\n  /**\n   * 设置视频元素\n   * @param {HTMLVideoElement} videoElement - 视频元素\n   */\n  async setupVideoElement(videoElement) {\n    try {\n      if (this.initializedVideos.has(videoElement)) {\n        return;\n      }\n\n      console.log('🎥 Setting up video element:', videoElement);\n\n      // 标记为已初始化\n      this.initializedVideos.add(videoElement);\n      this.currentVideoElement = videoElement;\n\n      // 等待视频准备就绪\n      if (videoElement.readyState < 3) { // HAVE_FUTURE_DATA\n        await new Promise((resolve) => {\n          videoElement.addEventListener('canplay', resolve, { once: true });\n        });\n      }\n\n      // 初始化音频管理器\n      await this.audioManager.initialize(videoElement);\n\n      // 初始化可视化渲染器\n      await this.visualizationRenderer.initialize(videoElement);\n\n      // 设置视频事件监听器\n      this.setupVideoEventListeners(videoElement);\n\n      console.log('✅ Video element setup completed');\n\n    } catch (error) {\n      console.error('❌ Failed to setup video element:', error);\n      eventBus.emit(EVENTS.SYSTEM_ERROR, { error: error.message });\n    }\n  }\n\n  /**\n   * 设置视频事件监听器\n   * @param {HTMLVideoElement} videoElement - 视频元素\n   */\n  setupVideoEventListeners(videoElement) {\n    videoElement.addEventListener('play', () => {\n      console.log('🎥 Video started playing');\n      this.start();\n    });\n\n    videoElement.addEventListener('pause', () => {\n      console.log('🎥 Video paused');\n      this.pause();\n    });\n\n    videoElement.addEventListener('ended', () => {\n      console.log('🎥 Video ended');\n      this.stop();\n    });\n  }\n\n  /**\n   * 开始处理\n   */\n  start() {\n    if (!this.isInitialized || this.isRunning) {\n      return;\n    }\n\n    console.log('▶️ Starting audio processing and AI classification...');\n\n    this.isRunning = true;\n    this.audioManager.startProcessing();\n    this.visualizationRenderer.start();\n\n    eventBus.emit(EVENTS.AUDIO_STARTED);\n  }\n\n  /**\n   * 暂停处理\n   */\n  pause() {\n    if (!this.isRunning) {\n      return;\n    }\n\n    console.log('⏸️ Pausing audio processing...');\n\n    this.audioManager.stopProcessing();\n    this.visualizationRenderer.pause();\n  }\n\n  /**\n   * 停止处理\n   */\n  stop() {\n    if (!this.isRunning) {\n      return;\n    }\n\n    console.log('⏹️ Stopping audio processing...');\n\n    this.isRunning = false;\n    this.audioManager.stopProcessing();\n    this.visualizationRenderer.stop();\n    this.labelManager.clearAllLabels();\n\n    eventBus.emit(EVENTS.AUDIO_STOPPED);\n  }\n\n  /**\n   * 处理音频数据\n   * @param {Object} audioData - 音频数据\n   */\n  async handleAudioData(audioData) {\n    try {\n      // 更新可视化\n      this.visualizationRenderer.updateVisualization(audioData);\n\n      // 发送到AI分类器\n      if (this.aiClassifier && audioData.processed) {\n        await this.aiClassifier.classify(audioData.processed);\n      }\n\n      // 性能监控\n      this.performanceMonitor.recordAudioProcessing();\n\n    } catch (error) {\n      console.error('Error handling audio data:', error);\n    }\n  }\n\n  /**\n   * 处理AI分类结果\n   * @param {Object} result - 分类结果\n   */\n  handleClassificationResult(result) {\n    try {\n      // 显示标签\n      if (result.predictions && result.predictions.length > 0) {\n        const topPrediction = result.predictions[0];\n\n        if (topPrediction.confidence > 0.5) { // 置信度阈值\n          this.labelManager.showLabel(\n            topPrediction.className,\n            topPrediction.confidence,\n            { x: 50, y: 50 } // 默认位置\n          );\n        }\n      }\n\n      // 性能监控\n      this.performanceMonitor.recordAIInference();\n\n    } catch (error) {\n      console.error('Error handling classification result:', error);\n    }\n  }\n\n  /**\n   * 处理系统错误\n   * @param {string} error - 错误信息\n   */\n  handleSystemError(error) {\n    // 显示错误提示\n    this.labelManager.showLabel('系统错误', 1.0, { x: 10, y: 10 });\n\n    // 尝试恢复\n    setTimeout(() => {\n      if (!this.isRunning && this.currentVideoElement) {\n        this.setupVideoElement(this.currentVideoElement);\n      }\n    }, 5000);\n  }\n\n  /**\n   * 处理性能警告\n   * @param {Object} data - 性能数据\n   */\n  handlePerformanceWarning(data) {\n    console.warn('Performance warning:', data);\n\n    // 可以实现自适应性能调整\n    if (data.cpuUsage > 80) {\n      // 降低处理频率\n      this.audioManager.updateConfig({ processingInterval: 200 });\n    }\n  }\n\n  /**\n   * 获取应用状态\n   * @returns {Object} 应用状态\n   */\n  getStatus() {\n    return {\n      isInitialized: this.isInitialized,\n      isRunning: this.isRunning,\n      audioManager: this.audioManager?.getAudioContextInfo(),\n      performance: this.performanceMonitor.getStats(),\n      config: this.configManager.getConfig()\n    };\n  }\n\n  /**\n   * 销毁应用\n   */\n  dispose() {\n    this.stop();\n\n    if (this.audioManager) {\n      this.audioManager.dispose();\n    }\n\n    if (this.aiClassifier) {\n      this.aiClassifier.dispose();\n    }\n\n    if (this.visualizationRenderer) {\n      this.visualizationRenderer.dispose();\n    }\n\n    if (this.labelManager) {\n      this.labelManager.dispose();\n    }\n\n    eventBus.clear();\n\n    console.log('🗑️ YouTube Aural-Visual Bridge: Disposed');\n  }\n}\n\n// 创建并初始化应用实例\nconst app = new YouTubeAuralVisualBridge();\n\n// 页面加载完成后初始化\nif (document.readyState === 'loading') {\n  document.addEventListener('DOMContentLoaded', () => app.initialize());\n} else {\n  app.initialize();\n}\n\n// 导出应用实例（用于调试）\nwindow.YouTubeAuralVisualBridge = app;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "__webpack_require__.amdD = function () {\n\tthrow new Error('define cannot be used indirect');\n};", "__webpack_require__.amdO = {};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.nmd = (module) => {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "__webpack_require__.j = 854;", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t854: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkyoutube_aural_visual_bridge\"] = self[\"webpackChunkyoutube_aural_visual_bridge\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [340,121], () => (__webpack_require__(2627)))\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["deferred", "eventBus", "constructor", "this", "events", "Map", "maxListeners", "on", "event", "callback", "options", "has", "set", "listeners", "get", "length", "listener", "once", "priority", "push", "sort", "a", "b", "off", "index", "findIndex", "splice", "delete", "emit", "data", "toRemove", "i", "error", "clear", "getStats", "stats", "EVENTS", "FeatureExtractor", "config", "sampleRate", "frameLength", "<PERSON><PERSON><PERSON><PERSON>", "mel<PERSON><PERSON>", "fftSize", "minFreq", "maxFreq", "melFilterBank", "isInitialized", "initialize", "createMelFilterBank", "extract", "audioData", "Error", "features", "stft", "computeSTFT", "timeData", "powerSpectrum", "computePowerSpectrum", "melSpectrogram", "computeMelSpectrogram", "logMelSpectrogram", "computeLogMelSpectrogram", "mfcc", "computeMFCC", "spectralCentroid", "computeSpectralCentroid", "<PERSON><PERSON><PERSON><PERSON>", "computeSpectralRolloff", "zeroCrossingRate", "computeZeroCrossingRate", "numFrames", "Math", "floor", "frame", "start", "frameData", "min", "Float32Array", "windowed", "applyHannWindow", "fftResult", "computeFFT", "window", "cos", "PI", "N", "real", "imag", "k", "realSum", "imagSum", "n", "angle", "sin", "map", "power", "fftBins", "melMin", "hzToMel", "melMax", "melPoints", "binPoints", "mel", "melToHz", "hz", "filterBank", "filter", "left", "center", "right", "j", "log10", "pow", "melFrame", "sum", "logFrame", "log", "max", "weightedSum", "totalPower", "rolloffPercent", "threshold", "reduce", "val", "cumulativePower", "crossings", "getStatus", "melFilterBankSize", "dispose", "AudioProcessor", "audioContext", "windowType", "featureExtractor", "audioBuffer", "process", "preprocessed", "preprocess", "raw", "postprocess", "timestamp", "Date", "now", "convertToTimeData", "resampled", "resample", "normalized", "normalize", "applyWindow", "preemphasis", "timeDomain", "freqData", "frequency", "inputRate", "outputRate", "ratio", "outputLength", "output", "srcIndex", "srcIndexFloor", "srcIndexCeil", "fraction", "rms", "sqrt", "scale", "alpha", "normalizeFeatures", "smoothed", "smoothFeatures", "addContext", "mean", "variance", "std", "windowSize", "smoothedMel", "count", "context", "bufferSize", "PerformanceMonitor", "isRunning", "startTime", "metrics", "audioProcessing", "totalCalls", "totalTime", "averageTime", "maxTime", "minTime", "Infinity", "aiInference", "rendering", "totalFrames", "fps", "memory", "current", "peak", "average", "samples", "cpu", "sampleInterval", "maxSamples", "enableDetailedLogging", "warningThresholds", "audioProcessingTime", "aiInferenceTime", "renderingTime", "memoryUsage", "cpuUsage", "monitoringInterval", "lastCpuTime", "performance", "setInterval", "sampleMetrics", "stop", "clearInterval", "recordAudioProcessing", "processingTime", "updateMetric", "emitWarning", "recordAIInference", "inferenceTime", "recordRendering", "renderTime", "metricName", "value", "metric", "sampleMemoryUsage", "sampleCpuUsage", "checkWarningThresholds", "usedJSHeapSize", "navigator", "deviceMemory", "shift", "sample", "timeDiff", "expectedIdleTime", "thresholds", "type", "warning", "uptime", "JSON", "parse", "stringify", "summary", "getSummary", "callsPerSecond", "getCallsPerSecond", "formatBytes", "toFixed", "bytes", "parseFloat", "reset", "Object", "keys", "for<PERSON>ach", "key", "undefined", "updateConfig", "newConfig", "AudioManager", "analyser", "sourceNode", "audioProcessor", "isProcessing", "performanceMonitor", "smoothingTimeConstant", "processingInterval", "handleAudioData", "bind", "videoElement", "AudioContext", "webkitAudioContext", "createMediaElementSource", "create<PERSON><PERSON>yser", "connect", "destination", "setupVideoEventListeners", "frequencyBinCount", "message", "addEventListener", "state", "resume", "startProcessing", "stopProcessing", "processingLoop", "getAudioData", "hasValidAudio", "processedData", "processed", "recordProcessingTime", "setTimeout", "bufferLength", "timeDomainData", "Uint8Array", "frequencyData", "getByteTimeDomainData", "getByteFrequencyData", "avgFreq", "getAudioContextInfo", "currentTime", "baseLatency", "outputLatency", "getPerformanceStats", "close", "YAMNetClassifier", "model", "classNames", "modelUrl", "classMapUrl", "patchWindowSeconds", "patchHopSeconds", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "topK", "inputSpecs", "expectedSamples", "patchFrames", "totalPredictions", "averageInferenceTime", "lastInferenceTime", "loadModel", "loadClassMap", "warmupModel", "tf", "response", "fetch", "ok", "status", "statusText", "csvText", "text", "parseClassMap", "getDefaultClassMap", "lines", "trim", "split", "line", "parts", "displayName", "replace", "dummyInput", "predictions", "predict", "Array", "isArray", "tensor", "classify", "audioFeatures", "inputTensor", "preprocessAudio", "results", "postprocessPredictions", "updateStats", "modelType", "preprocessTimeData", "preprocessMelSpectrogram", "reconstructTimeData", "slice", "flatData", "flat", "targetLength", "resized", "scoresTensor", "scoresData", "className", "confidence", "classIndex", "getInfo", "classCount", "getClassNames", "SpeechCommandDetector", "commands", "vocabularyUrl", "enableCustomCommands", "customCommands", "totalDetections", "commandCounts", "loadVocabulary", "metadata", "json", "words", "getDefaultCommands", "inputShape", "inputs", "shape", "batchedInput", "expandDims", "detect", "preprocessSpectrogram", "preprocessSTFT", "preprocessFrequencyData", "height", "width", "channels", "spectrogramData", "c", "command", "commandIndex", "result", "commandCount", "getClassCount", "getCommands", "Model<PERSON><PERSON><PERSON>", "models", "modelCache", "enableCaching", "maxCacheSize", "enablePreloading", "modelTimeout", "retryAttempts", "modelRegistry", "yamnet", "url", "size", "description", "speechCommands", "modelsLoaded", "totalLoadTime", "cacheHits", "cacheMisses", "errors", "setupMemoryManagement", "preloadModels", "memoryMonitorInterval", "numBytes", "cleanupUnusedModels", "modelName", "modelConfig", "loadModelWithRetry", "loadTime", "cacheModel", "lastError", "attempt", "loadPromise", "loadModelFromUrl", "timeoutPromise", "Promise", "_", "reject", "race", "delay", "resolve", "loadOptions", "onProgress", "oldestModel", "next", "removeFromCache", "getModelInfo", "cached", "loaded", "getAvailableModels", "name", "getCacheStatus", "maxSize", "from", "hitRate", "averageLoadTime", "cacheStatus", "AIClassifier", "modelManager", "yamnetClassifier", "speechCommandDetector", "enableYAMNet", "enableSpeechCommands", "maxPredictions", "processingMode", "enableGPU", "totalInferences", "errorCount", "setupTensorFlowBackend", "loadModels", "getLoadedModels", "backend", "backendNames", "includes", "loadPromises", "loadYAMNetModel", "loadSpeechCommandModel", "all", "audioEvents", "inferencePromises", "then", "catch", "inferenceResults", "inference", "filterAndSortPredictions", "endTime", "pred", "classes", "cleanupMemory", "gc", "LabelManager", "labels", "container", "max<PERSON><PERSON><PERSON>", "labelDuration", "fadeInDuration", "fadeOutDuration", "position", "fontSize", "enableAnimations", "styles", "zIndex", "pointerEvents", "fontFamily", "label", "background", "color", "padding", "borderRadius", "marginBottom", "boxShadow", "transition", "max<PERSON><PERSON><PERSON>", "wordWrap", "labelIdCounter", "createContainer", "document", "createElement", "id", "assign", "style", "updateContainerPosition", "body", "append<PERSON><PERSON><PERSON>", "top", "bottom", "showLabel", "labelId", "labelElement", "createLabelElement", "enforceMaxLabels", "element", "animateIn", "<PERSON><PERSON><PERSON><PERSON>", "opacity", "getEventColor", "borderLeft", "confidenceText", "innerHTML", "formatEventText", "eventType", "transform", "offsetHeight", "animateOut", "labelInfo", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "oldestLabelId", "clearAllLabels", "updateLabel", "updates", "querySelector", "textContent", "confidenceDiv", "getCurrentLabels", "entries", "info", "fontSizeMap", "small", "medium", "large", "<PERSON><PERSON><PERSON><PERSON>", "totalLabelsShown", "Visualization<PERSON><PERSON><PERSON>", "canvas", "canvasCtx", "animationFrameId", "enableFrequencyDomain", "enableTimeDomain", "enableHighFrequencyWarning", "highFrequencyThreshold", "showDebugInfo", "colorScheme", "colorSchemes", "default", "lowFreq", "r", "g", "midFreq", "highFreq", "waveform", "accessibility", "currentAudioData", "framesRendered", "averageRenderTime", "lastRenderTime", "createCanvas", "setupResponsiveListeners", "videoContainer", "findVideoContainer", "cssText", "getContext", "updateCanvasSize", "selectors", "selector", "contains", "parentElement", "ResizeObserver", "resizeObserver", "observe", "rect", "getBoundingClientRect", "renderLoop", "pause", "cancelAnimationFrame", "clearRect", "renderVisualization", "renderWaitingState", "renderDebugInfo", "requestAnimationFrame", "renderFrequencyDomain", "renderTimeDomain", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "colors", "x", "barHeight", "currentFrequency", "intensity", "interpolateColor", "fillStyle", "fillRect", "lineWidth", "strokeStyle", "beginPath", "sliceWidth", "y", "moveTo", "lineTo", "stroke", "font", "fillText", "color1", "color2", "factor", "round", "updateVisualization", "ConfigManager", "defaultConfig", "getDefaultConfig", "storageKey", "changeListeners", "Set", "ai", "yamnetConfidenceThreshold", "speechCommandsConfidenceThreshold", "enableLocalProcessing", "enableCloudAPI", "apiEndpoint", "<PERSON><PERSON><PERSON><PERSON>", "ui", "showLabels", "maxLabelsOnScreen", "labelPosition", "enableSoundDictionary", "enableVisualization", "visualizationOpacity", "theme", "enableGPUAcceleration", "maxMemoryUsage", "enablePerformanceMonitoring", "autoOptimization", "audio", "enable<PERSON>reemp<PERSON><PERSON>", "visualization", "customColors", "enableHighContrast", "enableLargeText", "enableScreenReader", "enableKeyboardNavigation", "reduceMotion", "debug", "enableLogging", "enableDetailedStats", "enableModelInfo", "logLevel", "loadConfig", "storedConfig", "loadFromStorage", "mergeConfigs", "validateConfig", "chrome", "storage", "loadFromChromeStorage", "loadFromLocalStorage", "sync", "runtime", "stored", "localStorage", "getItem", "saveConfig", "saveToStorage", "notifyConfigChange", "saveToChromeStorage", "saveToLocalStorage", "setItem", "userConfig", "merged", "getConfig", "path", "getNestedValue", "setConfig", "setNestedValue", "obj", "last<PERSON>ey", "pop", "resetConfig", "exportConfig", "importConfig", "config<PERSON><PERSON>", "importedConfig", "addChangeListener", "add", "removeChangeListener", "getConfigSummary", "ai<PERSON>nabled", "uiEnabled", "performanceOptimized", "accessibilityEnabled", "values", "some", "app", "audioManager", "aiClassifier", "labelManager", "visualization<PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "currentVideoElement", "initializedVideos", "WeakSet", "handleMutations", "setupVideoElement", "handleClassificationResult", "setupEventListeners", "initializeComponents", "setupMutationObserver", "checkExistingVideos", "handleSystemError", "handlePerformanceWarning", "MutationObserver", "childList", "subtree", "mutationsList", "mutation", "addedNodes", "node", "nodeType", "tagName", "existingVideo", "readyState", "topPrediction", "_this$audioManager", "YouTubeAuralVisualBridge", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "exports", "module", "__webpack_modules__", "call", "m", "amdD", "amdO", "O", "chunkIds", "fn", "notFulfilled", "fulfilled", "every", "getter", "__esModule", "d", "definition", "o", "defineProperty", "enumerable", "globalThis", "Function", "e", "prop", "prototype", "hasOwnProperty", "Symbol", "toStringTag", "nmd", "paths", "children", "installedChunks", "chunkId", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "chunkLoadingGlobal", "self", "__webpack_exports__"], "sourceRoot": ""}