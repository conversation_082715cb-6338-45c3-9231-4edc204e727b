# 详细安装指南

## 问题诊断

如果Chrome显示"无法加载扩展程序"，通常是以下原因之一：

### 1. 文件夹选择错误
**正确做法**：
- 必须选择包含 `manifest.json` 文件的文件夹
- 不要选择上级文件夹或子文件夹
- 确保选择的文件夹直接包含以下文件：
  ```
  ├── manifest.json
  ├── content.js
  ├── visualizer.css
  ├── README.md
  └── TESTING.md
  ```

### 2. 文件权限问题
**检查方法**：
- 确保文件夹和所有文件都有读取权限
- 在macOS上，可能需要给Chrome访问文件夹的权限

### 3. 文件路径包含特殊字符
**避免**：
- 文件夹路径中包含中文字符
- 文件夹路径中包含空格或特殊符号
- 建议将项目放在简单的英文路径下，如 `/Users/<USER>/Desktop/youtube-extension/`

## 逐步安装指南

### 步骤1：验证文件完整性
在项目文件夹中，确认包含以下5个文件：
```bash
ls -la
# 应该看到：
# manifest.json
# content.js  
# visualizer.css
# README.md
# TESTING.md
```

### 步骤2：打开Chrome扩展管理页面
1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 按回车键

### 步骤3：启用开发者模式
1. 在扩展管理页面的右上角
2. 找到"开发者模式"开关
3. 点击开关，确保它是**开启状态**（蓝色）

### 步骤4：加载扩展
1. 点击"加载已解压的扩展程序"按钮
2. 在文件选择对话框中：
   - **重要**：选择包含 `manifest.json` 的文件夹
   - 不要选择 `manifest.json` 文件本身
   - 不要选择上级文件夹
3. 点击"选择文件夹"或"确定"

### 步骤5：验证安装成功
安装成功后，您应该看到：
- 扩展出现在扩展列表中
- 名称显示为"YouTube Aural-Visual Bridge"
- 状态显示为"已启用"
- 没有错误信息

## 常见错误及解决方案

### 错误："清单文件无效"
**原因**：manifest.json格式错误或找不到
**解决方案**：
1. 确保选择了正确的文件夹
2. 检查manifest.json文件是否存在
3. 验证JSON格式是否正确

### 错误："无法加载扩展程序"
**原因**：文件权限或路径问题
**解决方案**：
1. 将项目文件夹移动到桌面
2. 确保文件夹名称为英文
3. 重新尝试加载

### 错误："扩展程序已损坏"
**原因**：文件不完整或损坏
**解决方案**：
1. 重新下载所有文件
2. 确保所有文件都完整保存
3. 检查文件编码是否为UTF-8

## 替代安装方法

### 方法1：使用命令行验证
在项目文件夹中运行：
```bash
# 检查文件是否存在
ls -la manifest.json content.js visualizer.css

# 验证JSON格式
cat manifest.json | python -m json.tool
```

### 方法2：创建新的文件夹
1. 在桌面创建新文件夹：`youtube-audio-visualizer`
2. 将所有文件复制到新文件夹
3. 尝试从新文件夹加载扩展

### 方法3：检查Chrome版本
确保Chrome版本支持Manifest V3：
- 最低要求：Chrome 88+
- 检查方法：Chrome菜单 → 帮助 → 关于Google Chrome

## 成功安装的标志

安装成功后，您应该能够：
1. 在 `chrome://extensions/` 页面看到扩展
2. 扩展状态显示为"已启用"
3. 访问YouTube视频页面时，扩展自动工作
4. 在开发者工具的Console中看到相关日志

## 如果仍然无法安装

请提供以下信息以便进一步诊断：
1. Chrome浏览器版本号
2. 操作系统版本
3. 具体的错误信息截图
4. 项目文件夹的完整路径
5. 文件夹中的文件列表

## 快速检查清单

- [ ] 文件夹包含manifest.json文件
- [ ] 开发者模式已开启
- [ ] 选择的是文件夹，不是文件
- [ ] 文件夹路径为英文
- [ ] Chrome版本88+
- [ ] 所有文件都存在且完整
