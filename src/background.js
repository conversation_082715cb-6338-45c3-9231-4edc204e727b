/**
 * Chrome扩展后台脚本
 */

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener((details) => {
  console.log('YouTube Aural-Visual Bridge v2.0 installed');
  
  if (details.reason === 'install') {
    // 首次安装
    console.log('First time installation');
  } else if (details.reason === 'update') {
    // 更新
    console.log('Extension updated');
  }
});

// 处理来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Background received message:', request);
  
  switch (request.type) {
    case 'GET_CONFIG':
      // 获取配置
      chrome.storage.sync.get(null, (config) => {
        sendResponse({ success: true, config });
      });
      return true; // 异步响应
      
    case 'SET_CONFIG':
      // 保存配置
      chrome.storage.sync.set(request.config, () => {
        sendResponse({ success: true });
      });
      return true; // 异步响应
      
    case 'LOG_ERROR':
      // 记录错误
      console.error('Content script error:', request.error);
      sendResponse({ success: true });
      break;
      
    default:
      sendResponse({ success: false, error: 'Unknown message type' });
  }
});

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  // 检查是否是YouTube页面
  if (changeInfo.status === 'complete' && tab.url && tab.url.includes('youtube.com/watch')) {
    console.log('YouTube video page loaded:', tab.url);
  }
});

console.log('YouTube Aural-Visual Bridge v2.0 background script loaded');
