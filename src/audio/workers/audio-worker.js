/**
 * 音频处理Web Worker
 * 在后台线程中执行音频特征提取和预处理
 */

let isInitialized = false;
let config = {
  sampleRate: 16000,
  frameLength: 1024,
  hopLength: 512,
  melBins: 64,
  fftSize: 2048
};

// 监听主线程消息
self.addEventListener('message', async function(e) {
  const { type, data, id } = e.data;
  
  try {
    let result;
    
    switch (type) {
      case 'INITIALIZE':
        result = await initialize(data);
        break;
        
      case 'PROCESS_AUDIO':
        result = await processAudio(data);
        break;
        
      case 'EXTRACT_FEATURES':
        result = await extractFeatures(data);
        break;
        
      case 'UPDATE_CONFIG':
        result = updateConfig(data);
        break;
        
      case 'GET_STATUS':
        result = getStatus();
        break;
        
      default:
        throw new Error(`Unknown message type: ${type}`);
    }
    
    // 发送成功响应
    self.postMessage({
      id,
      success: true,
      result
    });
    
  } catch (error) {
    // 发送错误响应
    self.postMessage({
      id,
      success: false,
      error: error.message
    });
  }
});

/**
 * 初始化音频Worker
 */
async function initialize(workerConfig = {}) {
  if (isInitialized) {
    return { message: 'Already initialized' };
  }
  
  console.log('Audio Worker: Initializing...');
  
  try {
    // 更新配置
    config = { ...config, ...workerConfig };
    
    // 初始化音频处理组件
    await initializeAudioProcessing();
    
    isInitialized = true;
    
    return {
      message: 'Audio Worker initialized successfully',
      config
    };
    
  } catch (error) {
    console.error('Audio Worker initialization failed:', error);
    throw error;
  }
}

/**
 * 初始化音频处理
 */
async function initializeAudioProcessing() {
  // 这里可以初始化音频处理所需的资源
  // 例如预计算的滤波器组、窗函数等
  console.log('Audio processing components initialized');
}

/**
 * 处理音频数据
 */
async function processAudio(audioData) {
  if (!isInitialized) {
    throw new Error('Audio Worker not initialized');
  }
  
  const startTime = performance.now();
  
  try {
    // 1. 预处理音频数据
    const preprocessed = preprocessAudio(audioData);
    
    // 2. 提取特征
    const features = extractAudioFeatures(preprocessed);
    
    const processingTime = performance.now() - startTime;
    
    return {
      raw: audioData,
      preprocessed,
      features,
      processingTime,
      timestamp: Date.now()
    };
    
  } catch (error) {
    console.error('Audio processing error:', error);
    throw error;
  }
}

/**
 * 提取音频特征
 */
async function extractFeatures(audioData) {
  if (!isInitialized) {
    throw new Error('Audio Worker not initialized');
  }
  
  const startTime = performance.now();
  
  try {
    const features = extractAudioFeatures(audioData);
    const extractionTime = performance.now() - startTime;
    
    return {
      features,
      extractionTime,
      timestamp: Date.now()
    };
    
  } catch (error) {
    console.error('Feature extraction error:', error);
    throw error;
  }
}

/**
 * 预处理音频数据
 */
function preprocessAudio(audioData) {
  // 转换频域数据为时域数据
  const timeData = convertToTimeData(audioData);
  
  // 重采样到目标采样率
  const resampled = resample(timeData, audioData.sampleRate || 44100, config.sampleRate);
  
  // 归一化
  const normalized = normalize(resampled);
  
  return {
    timeData: normalized,
    sampleRate: config.sampleRate,
    frameLength: config.frameLength,
    hopLength: config.hopLength
  };
}

/**
 * 提取音频特征
 */
function extractAudioFeatures(audioData) {
  const features = {};
  
  // 计算STFT
  const stft = computeSTFT(audioData.timeData || audioData);
  features.stft = stft;
  
  // 计算功率谱
  const powerSpectrum = computePowerSpectrum(stft);
  features.powerSpectrum = powerSpectrum;
  
  // 计算梅尔频谱图
  const melSpectrogram = computeMelSpectrogram(powerSpectrum);
  features.melSpectrogram = melSpectrogram;
  
  // 计算其他特征
  features.spectralCentroid = computeSpectralCentroid(powerSpectrum);
  features.zeroCrossingRate = computeZeroCrossingRate(audioData.timeData || audioData);
  
  return features;
}

/**
 * 转换频域数据为时域数据
 */
function convertToTimeData(audioData) {
  if (audioData.timeDomain) {
    // 转换Uint8Array到Float32Array
    const timeData = new Float32Array(audioData.timeDomain.length);
    for (let i = 0; i < audioData.timeDomain.length; i++) {
      timeData[i] = (audioData.timeDomain[i] - 128) / 128.0;
    }
    return timeData;
  }
  
  if (audioData.frequency) {
    // 简单的频域到时域转换
    const timeData = new Float32Array(audioData.frequency.length);
    for (let i = 0; i < audioData.frequency.length; i++) {
      timeData[i] = (audioData.frequency[i] - 128) / 128.0;
    }
    return timeData;
  }
  
  throw new Error('No suitable audio data found');
}

/**
 * 重采样音频数据
 */
function resample(data, inputRate, outputRate) {
  if (inputRate === outputRate) {
    return data;
  }
  
  const ratio = inputRate / outputRate;
  const outputLength = Math.floor(data.length / ratio);
  const output = new Float32Array(outputLength);
  
  for (let i = 0; i < outputLength; i++) {
    const srcIndex = i * ratio;
    const srcIndexFloor = Math.floor(srcIndex);
    const srcIndexCeil = Math.min(srcIndexFloor + 1, data.length - 1);
    const fraction = srcIndex - srcIndexFloor;
    
    output[i] = data[srcIndexFloor] * (1 - fraction) + data[srcIndexCeil] * fraction;
  }
  
  return output;
}

/**
 * 归一化音频数据
 */
function normalize(data) {
  let rms = 0;
  for (let i = 0; i < data.length; i++) {
    rms += data[i] * data[i];
  }
  rms = Math.sqrt(rms / data.length);
  
  if (rms < 1e-8) {
    return data;
  }
  
  const normalized = new Float32Array(data.length);
  const scale = 0.95 / rms;
  
  for (let i = 0; i < data.length; i++) {
    normalized[i] = Math.max(-1, Math.min(1, data[i] * scale));
  }
  
  return normalized;
}

/**
 * 计算STFT（简化版本）
 */
function computeSTFT(timeData) {
  // 这是一个简化的STFT实现
  // 实际项目中应该使用更高效的FFT算法
  const frameLength = config.frameLength;
  const hopLength = config.hopLength;
  const numFrames = Math.floor((timeData.length - frameLength) / hopLength) + 1;
  
  const stft = [];
  
  for (let frame = 0; frame < numFrames; frame++) {
    const start = frame * hopLength;
    const frameData = timeData.slice(start, start + frameLength);
    
    // 简化的DFT
    const real = new Float32Array(frameLength / 2);
    const imag = new Float32Array(frameLength / 2);
    
    for (let k = 0; k < frameLength / 2; k++) {
      for (let n = 0; n < frameLength; n++) {
        const angle = -2 * Math.PI * k * n / frameLength;
        real[k] += frameData[n] * Math.cos(angle);
        imag[k] += frameData[n] * Math.sin(angle);
      }
    }
    
    stft.push({ real, imag });
  }
  
  return stft;
}

/**
 * 计算功率谱
 */
function computePowerSpectrum(stft) {
  return stft.map(frame => {
    const power = new Float32Array(frame.real.length);
    for (let i = 0; i < frame.real.length; i++) {
      power[i] = frame.real[i] * frame.real[i] + frame.imag[i] * frame.imag[i];
    }
    return power;
  });
}

/**
 * 计算梅尔频谱图（简化版本）
 */
function computeMelSpectrogram(powerSpectrum) {
  // 简化的梅尔频谱图计算
  return powerSpectrum.map(frame => {
    const melFrame = new Float32Array(config.melBins);
    const binSize = frame.length / config.melBins;
    
    for (let i = 0; i < config.melBins; i++) {
      const start = Math.floor(i * binSize);
      const end = Math.floor((i + 1) * binSize);
      let sum = 0;
      
      for (let j = start; j < end && j < frame.length; j++) {
        sum += frame[j];
      }
      
      melFrame[i] = sum / (end - start);
    }
    
    return melFrame;
  });
}

/**
 * 计算频谱质心
 */
function computeSpectralCentroid(powerSpectrum) {
  return powerSpectrum.map(frame => {
    let weightedSum = 0;
    let totalPower = 0;
    
    for (let i = 0; i < frame.length; i++) {
      const freq = i * config.sampleRate / (2 * frame.length);
      weightedSum += freq * frame[i];
      totalPower += frame[i];
    }
    
    return totalPower > 0 ? weightedSum / totalPower : 0;
  });
}

/**
 * 计算过零率
 */
function computeZeroCrossingRate(timeData) {
  let crossings = 0;
  for (let i = 1; i < timeData.length; i++) {
    if ((timeData[i] >= 0) !== (timeData[i - 1] >= 0)) {
      crossings++;
    }
  }
  return crossings / (timeData.length - 1);
}

/**
 * 更新配置
 */
function updateConfig(newConfig) {
  config = { ...config, ...newConfig };
  return { message: 'Config updated', config };
}

/**
 * 获取Worker状态
 */
function getStatus() {
  return {
    isInitialized,
    config
  };
}

console.log('Audio Worker loaded and ready');
