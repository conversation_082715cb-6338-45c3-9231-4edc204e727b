/**
 * Chrome扩展弹出窗口脚本
 */

document.addEventListener('DOMContentLoaded', function() {
  console.log('Popup loaded');
  
  // 初始化弹出窗口
  initializePopup();
});

async function initializePopup() {
  try {
    // 获取当前配置
    const config = await getConfig();
    
    // 更新UI
    updateUI(config);
    
    // 设置事件监听器
    setupEventListeners();
    
  } catch (error) {
    console.error('Failed to initialize popup:', error);
    showError('初始化失败');
  }
}

function getConfig() {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ type: 'GET_CONFIG' }, (response) => {
      if (response && response.success) {
        resolve(response.config);
      } else {
        resolve({});
      }
    });
  });
}

function setConfig(config) {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ type: 'SET_CONFIG', config }, (response) => {
      resolve(response && response.success);
    });
  });
}

function updateUI(config) {
  // 更新开关状态
  const enableAI = document.getElementById('enableAI');
  if (enableAI) {
    enableAI.checked = config.ai?.enableYAMNet !== false;
  }
  
  const enableLabels = document.getElementById('enableLabels');
  if (enableLabels) {
    enableLabels.checked = config.ui?.showLabels !== false;
  }
  
  const enableVisualization = document.getElementById('enableVisualization');
  if (enableVisualization) {
    enableVisualization.checked = config.ui?.enableVisualization !== false;
  }
}

function setupEventListeners() {
  // AI开关
  const enableAI = document.getElementById('enableAI');
  if (enableAI) {
    enableAI.addEventListener('change', async (e) => {
      const config = await getConfig();
      config.ai = config.ai || {};
      config.ai.enableYAMNet = e.target.checked;
      await setConfig(config);
      showSuccess('AI设置已更新');
    });
  }
  
  // 标签开关
  const enableLabels = document.getElementById('enableLabels');
  if (enableLabels) {
    enableLabels.addEventListener('change', async (e) => {
      const config = await getConfig();
      config.ui = config.ui || {};
      config.ui.showLabels = e.target.checked;
      await setConfig(config);
      showSuccess('标签设置已更新');
    });
  }
  
  // 可视化开关
  const enableVisualization = document.getElementById('enableVisualization');
  if (enableVisualization) {
    enableVisualization.addEventListener('change', async (e) => {
      const config = await getConfig();
      config.ui = config.ui || {};
      config.ui.enableVisualization = e.target.checked;
      await setConfig(config);
      showSuccess('可视化设置已更新');
    });
  }
}

function showSuccess(message) {
  showMessage(message, 'success');
}

function showError(message) {
  showMessage(message, 'error');
}

function showMessage(message, type) {
  const messageDiv = document.getElementById('message');
  if (messageDiv) {
    messageDiv.textContent = message;
    messageDiv.className = `message ${type}`;
    messageDiv.style.display = 'block';
    
    setTimeout(() => {
      messageDiv.style.display = 'none';
    }, 3000);
  }
}
