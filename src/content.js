/**
 * YouTube Aural-Visual Bridge - 主内容脚本
 * 集成AI音频分类功能的新版本
 */
import { AudioManager } from './core/AudioManager.js';
import { AIClassifier } from './core/AIClassifier.js';
import { LabelManager } from './ui/LabelManager.js';
import { VisualizationRenderer } from './ui/VisualizationRenderer.js';
import { PerformanceMonitor } from './utils/PerformanceMonitor.js';
import { ConfigManager } from './api/ConfigManager.js';
import eventBus, { EVENTS } from './core/EventBus.js';

/**
 * 主应用类
 */
class YouTubeAuralVisualBridge {
  constructor() {
    this.audioManager = null;
    this.aiClassifier = null;
    this.labelManager = null;
    this.visualizationRenderer = null;
    this.performanceMonitor = new PerformanceMonitor();
    this.configManager = new ConfigManager();

    this.isInitialized = false;
    this.isRunning = false;
    this.currentVideoElement = null;

    // 已初始化的视频元素集合
    this.initializedVideos = new WeakSet();

    // 绑定方法
    this.handleMutations = this.handleMutations.bind(this);
    this.setupVideoElement = this.setupVideoElement.bind(this);
    this.handleAudioData = this.handleAudioData.bind(this);
    this.handleClassificationResult = this.handleClassificationResult.bind(this);
  }

  /**
   * 初始化应用
   */
  async initialize() {
    try {
      console.log('🚀 YouTube Aural-Visual Bridge v2.0 - Initializing...');
      console.log('🌐 Current URL:', window.location.href);
      console.log('🎥 Looking for video elements...');

      // 1. 加载配置
      console.log('⚙️ Loading configuration...');
      await this.configManager.loadConfig();
      console.log('✅ Configuration loaded');

      // 2. 初始化性能监控
      this.performanceMonitor.start();

      // 3. 设置事件监听器
      this.setupEventListeners();

      // 4. 初始化组件
      await this.initializeComponents();

      // 5. 设置DOM观察器
      this.setupMutationObserver();

      // 6. 检查现有视频
      await this.checkExistingVideos();

      this.isInitialized = true;
      eventBus.emit(EVENTS.SYSTEM_READY);

      console.log('✅ YouTube Aural-Visual Bridge: Initialized successfully');

    } catch (error) {
      console.error('❌ YouTube Aural-Visual Bridge: Initialization failed:', error);
      eventBus.emit(EVENTS.SYSTEM_ERROR, { error: error.message });
      throw error;
    }
  }

  /**
   * 初始化组件
   */
  async initializeComponents() {
    // 初始化音频管理器
    this.audioManager = new AudioManager();

    // 初始化AI分类器
    this.aiClassifier = new AIClassifier();
    await this.aiClassifier.initialize();

    // 初始化标签管理器
    this.labelManager = new LabelManager();

    // 初始化可视化渲染器
    this.visualizationRenderer = new VisualizationRenderer();
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 音频数据事件
    eventBus.on(EVENTS.AUDIO_DATA_AVAILABLE, this.handleAudioData);

    // AI分类结果事件
    eventBus.on(EVENTS.AI_CLASSIFICATION_RESULT, this.handleClassificationResult);

    // 系统错误事件
    eventBus.on(EVENTS.SYSTEM_ERROR, (data) => {
      console.error('System error:', data.error);
      this.handleSystemError(data.error);
    });

    // 性能警告事件
    eventBus.on(EVENTS.UI_PERFORMANCE_WARNING, (data) => {
      console.warn('Performance warning:', data);
      this.handlePerformanceWarning(data);
    });
  }

  /**
   * 设置DOM变化观察器
   */
  setupMutationObserver() {
    const observer = new MutationObserver(this.handleMutations);

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    console.log('🔍 MutationObserver: Set up to watch for video elements');
  }

  /**
   * 处理DOM变化
   * @param {MutationRecord[]} mutationsList - 变化记录列表
   */
  handleMutations(mutationsList) {
    for (const mutation of mutationsList) {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === 1) { // 元素节点
            let videoElement = null;

            if (node.tagName === 'VIDEO') {
              videoElement = node;
            } else {
              videoElement = node.querySelector('video');
            }

            if (videoElement && !this.initializedVideos.has(videoElement)) {
              this.setupVideoElement(videoElement);
            }
          }
        });
      }
    }
  }

  /**
   * 检查现有视频元素
   */
  async checkExistingVideos() {
    const existingVideo = document.querySelector('video');
    if (existingVideo && !this.initializedVideos.has(existingVideo)) {
      console.log('🎥 Found existing video element');
      await this.setupVideoElement(existingVideo);
    }
  }

  /**
   * 设置视频元素
   * @param {HTMLVideoElement} videoElement - 视频元素
   */
  async setupVideoElement(videoElement) {
    try {
      if (this.initializedVideos.has(videoElement)) {
        return;
      }

      console.log('🎥 Setting up video element:', videoElement);

      // 标记为已初始化
      this.initializedVideos.add(videoElement);
      this.currentVideoElement = videoElement;

      // 等待视频准备就绪
      if (videoElement.readyState < 3) { // HAVE_FUTURE_DATA
        await new Promise((resolve) => {
          videoElement.addEventListener('canplay', resolve, { once: true });
        });
      }

      // 初始化音频管理器
      await this.audioManager.initialize(videoElement);

      // 初始化可视化渲染器
      await this.visualizationRenderer.initialize(videoElement);

      // 设置视频事件监听器
      this.setupVideoEventListeners(videoElement);

      console.log('✅ Video element setup completed');

    } catch (error) {
      console.error('❌ Failed to setup video element:', error);
      eventBus.emit(EVENTS.SYSTEM_ERROR, { error: error.message });
    }
  }

  /**
   * 设置视频事件监听器
   * @param {HTMLVideoElement} videoElement - 视频元素
   */
  setupVideoEventListeners(videoElement) {
    videoElement.addEventListener('play', () => {
      console.log('🎥 Video started playing');
      this.start();
    });

    videoElement.addEventListener('pause', () => {
      console.log('🎥 Video paused');
      this.pause();
    });

    videoElement.addEventListener('ended', () => {
      console.log('🎥 Video ended');
      this.stop();
    });
  }

  /**
   * 开始处理
   */
  start() {
    if (!this.isInitialized || this.isRunning) {
      return;
    }

    console.log('▶️ Starting audio processing and AI classification...');

    this.isRunning = true;
    this.audioManager.startProcessing();
    this.visualizationRenderer.start();

    eventBus.emit(EVENTS.AUDIO_STARTED);
  }

  /**
   * 暂停处理
   */
  pause() {
    if (!this.isRunning) {
      return;
    }

    console.log('⏸️ Pausing audio processing...');

    this.audioManager.stopProcessing();
    this.visualizationRenderer.pause();
  }

  /**
   * 停止处理
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    console.log('⏹️ Stopping audio processing...');

    this.isRunning = false;
    this.audioManager.stopProcessing();
    this.visualizationRenderer.stop();
    this.labelManager.clearAllLabels();

    eventBus.emit(EVENTS.AUDIO_STOPPED);
  }

  /**
   * 处理音频数据
   * @param {Object} audioData - 音频数据
   */
  async handleAudioData(audioData) {
    try {
      // 更新可视化
      this.visualizationRenderer.updateVisualization(audioData);

      // 发送到AI分类器
      if (this.aiClassifier && audioData.processed) {
        await this.aiClassifier.classify(audioData.processed);
      }

      // 性能监控
      this.performanceMonitor.recordAudioProcessing();

    } catch (error) {
      console.error('Error handling audio data:', error);
    }
  }

  /**
   * 处理AI分类结果
   * @param {Object} result - 分类结果
   */
  handleClassificationResult(result) {
    try {
      // 显示标签
      if (result.predictions && result.predictions.length > 0) {
        const topPrediction = result.predictions[0];

        if (topPrediction.confidence > 0.5) { // 置信度阈值
          this.labelManager.showLabel(
            topPrediction.className,
            topPrediction.confidence,
            { x: 50, y: 50 } // 默认位置
          );
        }
      }

      // 性能监控
      this.performanceMonitor.recordAIInference();

    } catch (error) {
      console.error('Error handling classification result:', error);
    }
  }

  /**
   * 处理系统错误
   * @param {string} error - 错误信息
   */
  handleSystemError(error) {
    // 显示错误提示
    this.labelManager.showLabel('系统错误', 1.0, { x: 10, y: 10 });

    // 尝试恢复
    setTimeout(() => {
      if (!this.isRunning && this.currentVideoElement) {
        this.setupVideoElement(this.currentVideoElement);
      }
    }, 5000);
  }

  /**
   * 处理性能警告
   * @param {Object} data - 性能数据
   */
  handlePerformanceWarning(data) {
    console.warn('Performance warning:', data);

    // 可以实现自适应性能调整
    if (data.cpuUsage > 80) {
      // 降低处理频率
      this.audioManager.updateConfig({ processingInterval: 200 });
    }
  }

  /**
   * 获取应用状态
   * @returns {Object} 应用状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isRunning: this.isRunning,
      audioManager: this.audioManager?.getAudioContextInfo(),
      performance: this.performanceMonitor.getStats(),
      config: this.configManager.getConfig()
    };
  }

  /**
   * 销毁应用
   */
  dispose() {
    this.stop();

    if (this.audioManager) {
      this.audioManager.dispose();
    }

    if (this.aiClassifier) {
      this.aiClassifier.dispose();
    }

    if (this.visualizationRenderer) {
      this.visualizationRenderer.dispose();
    }

    if (this.labelManager) {
      this.labelManager.dispose();
    }

    eventBus.clear();

    console.log('🗑️ YouTube Aural-Visual Bridge: Disposed');
  }
}

// 创建并初始化应用实例
const app = new YouTubeAuralVisualBridge();

// 页面加载完成后初始化
console.log('🎬 YouTube Aural-Visual Bridge - Content script loaded');
console.log('📍 Current URL:', window.location.href);
console.log('📄 Document ready state:', document.readyState);

if (document.readyState === 'loading') {
  console.log('⏳ Waiting for DOMContentLoaded...');
  document.addEventListener('DOMContentLoaded', () => {
    console.log('✅ DOMContentLoaded fired, initializing...');
    app.initialize().catch(error => {
      console.error('❌ Failed to initialize app:', error);
    });
  });
} else {
  console.log('✅ Document already ready, initializing immediately...');
  app.initialize().catch(error => {
    console.error('❌ Failed to initialize app:', error);
  });
}

// 导出应用实例（用于调试）
window.YouTubeAuralVisualBridge = app;
