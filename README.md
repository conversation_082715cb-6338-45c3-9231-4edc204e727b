# YouTube Aural-Visual Bridge

一个专为高频听力受损用户设计的Chrome浏览器扩展，在YouTube视频上叠加音频可视化图层，帮助用户"看见"他们可能听不到的声音。

## 功能特点

- **实时音频可视化**：在YouTube视频播放器上叠加透明的音频可视化图层
- **双重显示模式**：
  - 波形图：显示音频的整体音量变化
  - 频谱图：显示不同频率的音频强度
- **高频警示系统**：4kHz以上的高频声音使用醒目的红色/黄色显示，帮助识别可能听不到的声音
- **无干扰设计**：可视化图层完全透明，不影响正常的视频观看和控制操作
- **自适应布局**：支持全屏模式、影院模式等不同播放器尺寸

## 安装方法

### 开发者模式安装（推荐）

1. 下载或克隆此项目到本地
2. 打开Chrome浏览器，访问 `chrome://extensions/`
3. 在右上角开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含此项目文件的文件夹
6. 扩展程序将自动加载并激活

## 使用方法

1. 安装扩展后，访问任意YouTube视频页面
2. 开始播放视频，可视化图层将自动出现
3. 观察可视化效果：
   - **蓝色区域**：中低频声音（通常可以听到）
   - **黄色到红色区域**：高频声音（可能听不到的声音）
   - **波形线**：整体音量变化

## 技术实现

- **Manifest V3**：使用最新的Chrome扩展标准
- **Web Audio API**：实时分析音频数据
- **Canvas 2D**：绘制可视化图形
- **MutationObserver**：动态检测YouTube页面变化
- **ResizeObserver**：响应播放器尺寸变化

## 文件结构

```
├── manifest.json       # 扩展配置文件
├── content.js          # 核心JavaScript逻辑
├── visualizer.css      # 样式文件
└── README.md          # 说明文档
```

## 浏览器兼容性

- Chrome 88+
- Edge 88+
- 其他基于Chromium的浏览器

## 隐私说明

此扩展：
- 仅在YouTube网站上运行
- 不收集任何用户数据
- 不修改或存储音频内容
- 所有处理都在本地进行

## 故障排除

### 可视化不显示
1. 确保视频正在播放
2. 检查浏览器是否允许音频播放（某些浏览器需要用户交互后才能播放音频）
3. 尝试刷新页面

### 性能问题
- 可视化使用硬件加速的Canvas 2D，对性能影响很小
- 如遇到性能问题，可以尝试关闭其他标签页

## 开发说明

### 修改可视化效果
- 编辑 `content.js` 中的 `drawFrequencyDomain` 和 `drawTimeDomain` 函数
- 调整颜色、形状、动画效果等

### 调整高频阈值
- 修改 `content.js` 中的 `highFrequencyThreshold` 变量（默认4000Hz）

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
