{"name": "youtube-aural-visual-bridge", "version": "2.0.0", "description": "AI-powered audio classification and visualization for YouTube videos", "main": "dist/content.js", "scripts": {"dev": "webpack --mode development --watch", "build": "webpack --mode production", "build:dev": "webpack --mode development", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix", "clean": "<PERSON><PERSON><PERSON> dist", "analyze": "webpack-bundle-analyzer dist/stats.json", "serve": "http-server dist -p 8080", "prepare": "npm run build"}, "dependencies": {"@tensorflow/tfjs": "^4.15.0", "@tensorflow/tfjs-backend-webgl": "^4.15.0", "@tensorflow/tfjs-converter": "^4.15.0"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "babel-loader": "^9.1.3", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.1", "eslint": "^8.52.0", "eslint-config-standard": "^17.1.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mini-css-extract-plugin": "^2.7.6", "rimraf": "^5.0.5", "style-loader": "^3.3.3", "terser-webpack-plugin": "^5.3.9", "url-loader": "^4.1.1", "webpack": "^5.89.0", "webpack-bundle-analyzer": "^4.9.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "worker-loader": "^3.0.8", "core-js": "^3.33.0"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "collectCoverageFrom": ["src/**/*.js", "!src/**/*.test.js", "!src/workers/*.js"], "coverageThreshold": {"global": {"branches": 50, "functions": 50, "lines": 50, "statements": 50}}}, "eslintConfig": {"extends": ["standard"], "env": {"browser": true, "es2022": true, "jest": true, "webextensions": true}, "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "rules": {"no-console": "warn", "no-debugger": "error", "prefer-const": "error", "no-var": "error"}}, "browserslist": ["Chrome >= 88", "Edge >= 88", "Firefox >= 90", "Safari >= 14"], "keywords": ["audio", "classification", "tensorflow", "accessibility", "youtube", "chrome-extension", "ai", "machine-learning"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/youtube-aural-visual-bridge.git"}, "bugs": {"url": "https://github.com/yourusername/youtube-aural-visual-bridge/issues"}, "homepage": "https://github.com/yourusername/youtube-aural-visual-bridge#readme"}